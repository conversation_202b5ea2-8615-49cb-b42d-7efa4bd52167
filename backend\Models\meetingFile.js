const mongoose = require('mongoose');

const MeetingFileSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  content: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    enum: ['summary', 'transcript'],
    required: true,
  },
  createdBy: {
    type: String,
    required: true,
    index: true // Add index for faster queries
  },
  meetingId: {
    type: String,
    required: true
  },
  enterpriseCode: {
    type: String,
    required: true
  },
  date: {
    type: Date,
    default: Date.now,
    index: true // Add index for sorting
  },
  fullTranscript: {
    type: String,
    required: false
  }
}, {
  timestamps: true // Add created/updated timestamps
});

// Add logging to save middleware
MeetingFileSchema.pre('save', function(next) {
  console.log('Saving file:', {
    name: this.name,
    type: this.type,
    createdBy: this.createdBy
  });
  next();
});

const MeetingFile = mongoose.model('MeetingFile', MeetingFileSchema);
module.exports = MeetingFile;