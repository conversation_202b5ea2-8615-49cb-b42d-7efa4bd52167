import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Mic, MicOff, Video, VideoOff, Phone, MessageSquare,
  Users, Settings, Wand2, CircleDot, StopCircle, FileText,
  Eye, EyeOff, Share2, ScreenShare, Layout, LayoutGrid, Sun, Moon
} from 'lucide-react';
import UnifiedVoiceService from '../services/unifiedVoiceService';
import WebRTCService from '../services/webrtcService'; // Import WebRTCService
import { getAuth } from 'firebase/auth';
import { meetingFilesAPI, meetingAPI } from '../services/mongodb';
import { saveAs } from 'file-saver'; // npm install file-saver
import { useTranslation } from 'react-i18next';
import '../assets/styles/meeting-controls.css';
import '../assets/styles/panels.css';
import ChatPanel from '../components/ChatPanel';
import SettingsPanel from '../components/SettingsPanel';
import webrtcDiagnostic from '../utils/webrtcDiagnostic';

const Meet = () => {
  const navigate = useNavigate();
  const { roomId } = useParams();
  const { t } = useTranslation();
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOff, setIsVideoOff] = useState(false);
  const [isVoiceAssistantActive, setIsVoiceAssistantActive] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [unifiedVoiceService, setUnifiedVoiceService] = useState(null);
  const [recordingError, setRecordingError] = useState('');
  const [mediaError, setMediaError] = useState('');
  const [participants, setParticipants] = useState(['You']); // Now we'll update this with real participants
  const [showParticipants, setShowParticipants] = useState(false);
  const [isModelLoading, setIsModelLoading] = useState(true);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const localVideoRef = useRef(null);
  const localStreamRef = useRef(null);
  const auth = getAuth();
  const [meeting, setMeeting] = useState(null);
  const [transcriptParts, setTranscriptParts] = useState([]);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const voiceAssistantRef = useRef(null);
  const [summaryPoints, setSummaryPoints] = useState([]);

  const [meetingTranscript, setMeetingTranscript] = useState('');
  const [showChat, setShowChat] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [lastSavedFile, setLastSavedFile] = useState(null);
  const [showLiveTranscript, setShowLiveTranscript] = useState(false);
  const [showDownloadOptions, setShowDownloadOptions] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [currentLayout, setCurrentLayout] = useState('grid-1x1'); // grid-1x1, grid-2x2, spotlight
  const [isDarkMode, setIsDarkMode] = useState(true);

  // WebRTC related state and refs
  const webRTCRef = useRef(null);
  const [remoteStreams, setRemoteStreams] = useState({}); // userId -> stream
  const [remoteUsers, setRemoteUsers] = useState({}); // userId -> userName
  const [connectionStatus, setConnectionStatus] = useState('disconnected'); // disconnected, connecting, connected

  useEffect(() => {
    const initUnifiedVoiceService = async () => {
      try {
        setIsModelLoading(true);
        setRecordingError('Chargement du modèle de reconnaissance vocale...');
        const service = new UnifiedVoiceService();
        await service.initWhisper((progress) => {
          setLoadingProgress(progress);
          setRecordingError(`Chargement du modèle: ${progress}%`);
        });
        setUnifiedVoiceService(service);
        setRecordingError('');
        setIsModelLoading(false);
      } catch (error) {
        setRecordingError(error instanceof Error ? error.message : 'Échec de l\'initialisation du service vocal');
        setIsModelLoading(false);
      }
    };

    const initMeeting = async () => {
      if (!auth.currentUser) return;

      try {
        // Try to get meeting from MongoDB
        const meetingData = await meetingAPI.getMeetingById(roomId);

        if (meetingData) {
          setMeeting(meetingData);
        } else {
          // Create new meeting if not found
          const user = auth.currentUser;
          const newMeeting = {
            roomId,
            title: 'Untitled Meeting',
            description: 'Meeting created on ' + new Date().toLocaleString(),
            date: new Date(),
            createdBy: user.uid,
            participants: [user.uid],
            status: 'in-progress',
            enterpriseCode: 'default'
          };

          try {
            const createdMeeting = await meetingAPI.createMeeting(newMeeting);
            setMeeting(createdMeeting);
          } catch (createError) {
            console.error('Error creating meeting:', createError);
            // Still allow the meeting to proceed even if saving fails
            setMeeting(newMeeting);
          }
        }
      } catch (error) {
        console.error('Error fetching meeting:', error);
        // Create a local meeting object if all else fails
        setMeeting({
          roomId,
          title: 'Untitled Meeting',
          createdBy: auth.currentUser.uid,
          startTime: new Date(),
          status: 'in-progress'
        });
      }
    };

    // Initialize WebRTC service
    const initWebRTC = async () => {
      try {
        if (!auth.currentUser) {
          console.error('User not authenticated');
          return;
        }

        setConnectionStatus('connecting');

        // Initialize WebRTC service
        const webRTC = new WebRTCService();
        await webRTC.init(auth.currentUser.uid, auth.currentUser.displayName);
        webRTCRef.current = webRTC;

        // Set up callbacks for WebRTC events
        webRTC.setCallbacks({
          onUserConnected: (userId, userName) => {
            console.log(`User connected: ${userName} (${userId})`);

            // If the user name is in the User-XXX format, try to get the real name
            if (userName.startsWith('User-')) {
              console.log(`Requesting user info for ${userId}`);
              webRTC.socket.emit('get-user-info', { userId });

              // Listen for user info response
              webRTC.socket.once('user-info', (userInfo) => {
                if (userInfo.userId === userId && userInfo.name && !userInfo.name.startsWith('User-')) {
                  console.log(`Received updated user info: ${userInfo.name} for ${userId}`);

                  // Update the user name in the remote users list
                  setRemoteUsers(prev => ({ ...prev, [userId]: userInfo.name }));

                  // Update participants list with the new name
                  setParticipants(prev => {
                    // Remove the old User-XXX entry if it exists
                    const filteredList = prev.filter(name => name !== userName);
                    // Add the new name if it's not already in the list
                    if (!filteredList.includes(userInfo.name)) {
                      return [...filteredList, userInfo.name];
                    }
                    return filteredList;
                  });
                }
              });
            }

            // Always update the remote users list with whatever name we have
            setRemoteUsers(prev => {
              // If we already have a better name than User-XXX, keep it
              if (prev[userId] && !prev[userId].startsWith('User-') && userName.startsWith('User-')) {
                return prev;
              }
              // Otherwise update with the new name
              return { ...prev, [userId]: userName };
            });

            // Update participants list
            setParticipants(prev => {
              // If we already have this user with a better name, don't add the User-XXX version
              if (userName.startsWith('User-') &&
                  Object.entries(remoteUsers).some(([id, name]) =>
                    id === userId && !name.startsWith('User-'))) {
                return prev;
              }

              // Otherwise add the user if not already in the list
              if (!prev.includes(userName)) {
                return [...prev, userName];
              }
              return prev;
            });
          },
          onUserDisconnected: (userId) => {
            console.log(`User disconnected: ${userId}`);
            const userName = remoteUsers[userId];

            // Remove from remote users
            setRemoteUsers(prev => {
              const newUsers = { ...prev };
              delete newUsers[userId];
              return newUsers;
            });

            // Remove from remote streams
            setRemoteStreams(prev => {
              const newStreams = { ...prev };
              delete newStreams[userId];
              return newStreams;
            });

            // Update participants list
            if (userName) {
              setParticipants(prev => prev.filter(name => name !== userName));
            }
          },
          onStreamAdded: (userId, userName, stream) => {
            console.log(`Stream added from ${userName} (${userId})`);

            // Log stream details for debugging
            const videoTracks = stream.getVideoTracks();
            const audioTracks = stream.getAudioTracks();
            console.log(`Remote stream has ${videoTracks.length} video tracks and ${audioTracks.length} audio tracks`);

            // Make sure we have a valid user name
            if (!userName || userName.startsWith('User-')) {
              console.log(`Requesting updated user info for ${userId}`);
              webRTC.socket.emit('get-user-info', { userId });

              // Listen for user info response
              webRTC.socket.once('user-info', (userInfo) => {
                if (userInfo.userId === userId && userInfo.name && !userInfo.name.startsWith('User-')) {
                  console.log(`Received updated user info: ${userInfo.name} for ${userId}`);
                  setRemoteUsers(prev => ({ ...prev, [userId]: userInfo.name }));
                }
              });
            }

            // Add the stream to remote streams
            setRemoteStreams(prev => ({ ...prev, [userId]: stream }));

            // Always update the user in the remote users list with the latest name
            setRemoteUsers(prev => {
              // If we have a real name (not a User-XXX format), or if the user isn't in the list yet, update it
              if (!userName.startsWith('User-') || !prev[userId]) {
                return { ...prev, [userId]: userName || `User-${userId.substring(0, 5)}` };
              }
              // If we already have a better name than User-XXX, keep it
              if (prev[userId] && !prev[userId].startsWith('User-')) {
                return prev;
              }
              // Otherwise update with whatever name we have
              return { ...prev, [userId]: userName || `User-${userId.substring(0, 5)}` };
            });

            // Update participants list
            setParticipants(prev => {
              const participantName = userName || `User-${userId.substring(0, 5)}`;
              // Remove any User-XXX entry for this user if it exists
              const filteredList = prev.filter(name =>
                !(name.startsWith('User-') && remoteUsers[userId] && remoteUsers[userId].startsWith('User-'))
              );
              // Add the new name if it's not already in the list
              if (!filteredList.includes(participantName)) {
                return [...filteredList, participantName];
              }
              return filteredList;
            });
          },
          onUserMuteStateChanged: (userId, isMuted) => {
            console.log(`User ${userId} ${isMuted ? 'muted' : 'unmuted'} their microphone`);
            // You can update UI to show mute status if needed
          },
          onUserVideoStateChanged: (userId, isVideoOff) => {
            console.log(`User ${userId} turned their video ${isVideoOff ? 'off' : 'on'}`);
            // You can update UI to show video status if needed
          }
        });

        setConnectionStatus('connected');
        console.log('WebRTC service initialized successfully');
      } catch (error) {
        console.error('Error initializing WebRTC:', error);
        setMediaError('Failed to initialize video call. Please try again.');
        setConnectionStatus('disconnected');
      }
    };

    initUnifiedVoiceService();
    initMeeting();
    initWebRTC();

    // Initialize media devices
    initializeMediaDevices();

    return () => {
      // Cleanup media streams
      if (localStreamRef.current) {
        localStreamRef.current.getTracks().forEach(track => track.stop());
      }
      if (unifiedVoiceService) {
        unifiedVoiceService.stopListening();
      }

      // Cleanup WebRTC
      if (webRTCRef.current) {
        webRTCRef.current.leaveRoom();
      }
    };
  }, [roomId]);

  useEffect(() => {
    const initializeUnifiedVoiceService = async () => {
      try {
        setIsModelLoading(true);
        setRecordingError('Initialisation de la reconnaissance vocale...');
        const service = new UnifiedVoiceService();
        await service.initWhisper((progress) => {
          setLoadingProgress(progress);
        });
        voiceAssistantRef.current = service;
        setIsModelLoading(false);
        setRecordingError('');
      } catch (error) {
        console.error('Erreur d\'initialisation du service vocal:', error);
        setRecordingError('Échec de l\'initialisation de la reconnaissance vocale');
        setIsModelLoading(false);
      }
    };

    // Set up listener for transcript updates from other participants
    if (webRTCRef.current && webRTCRef.current.socket) {
      webRTCRef.current.socket.on('transcript-update', (data) => {
        console.log('Received transcript update:', data);

        // Only update if it's from another user
        if (data.userId !== auth.currentUser.uid) {
          // Update the current transcript display
          setCurrentTranscript(`${data.userName || 'Someone'}: ${data.transcript}`);

          // Add to the full meeting transcript if recording is active
          if (isRecording) {
            setMeetingTranscript(prev => {
              const newEntry = `${data.userName || 'Someone'}: ${data.transcript}`;
              if (prev.includes(newEntry)) return prev;
              const updated = prev ? `${prev}\n${newEntry}` : newEntry;
              window.dispatchEvent(new CustomEvent('meetingTranscriptUpdate', { detail: updated }));
              return updated;
            });
          }
        }
      });
    }

    initializeUnifiedVoiceService();

    // Apply dark mode by default
    document.body.classList.add('dark-mode');
    document.body.classList.remove('light-mode');

    return () => {
      if (voiceAssistantRef.current) {
        voiceAssistantRef.current.stopListening();
      }
      // Clean up theme classes
      document.body.classList.remove('dark-mode');
      document.body.classList.remove('light-mode');
    };
  }, []);

  // Effect to handle theme changes
  useEffect(() => {
    if (isDarkMode) {
      document.body.classList.add('dark-mode');
      document.body.classList.remove('light-mode');
    } else {
      document.body.classList.add('light-mode');
      document.body.classList.remove('dark-mode');
    }
  }, [isDarkMode]);

  const initializeMediaDevices = async () => {
    try {
      console.log('Initializing media devices...');

      // Request media with more specific constraints
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'user'
        },
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      console.log('Media devices initialized successfully:', {
        videoTracks: stream.getVideoTracks().length,
        audioTracks: stream.getAudioTracks().length
      });

      // Log details about the tracks
      stream.getTracks().forEach(track => {
        console.log(`Track: ${track.kind}, ID: ${track.id}, Enabled: ${track.enabled}`);
      });

      // Set the local stream
      localStreamRef.current = stream;
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = stream;
        console.log('Local video element updated with stream');
      }

      // Join the WebRTC room with the local stream
      if (webRTCRef.current && roomId) {
        console.log('Joining WebRTC room with local stream');
        try {
          // First, check if the backend is reachable
          const socketTestUrl = `${webRTCRef.current.apiUrl}/api/socket-test?t=${Date.now()}`;
          console.log(`Testing socket server availability: ${socketTestUrl}`);

          try {
            const response = await fetch(socketTestUrl);
            if (response.ok) {
              const data = await response.json();
              console.log('Socket test successful:', data);
            } else {
              console.warn('Socket test failed with status:', response.status);
            }
          } catch (testError) {
            console.warn('Socket test request failed:', testError);
          }

          // Try to join the room with multiple attempts
          let joinAttempts = 0;
          const maxJoinAttempts = 3;
          let joinError = null;

          while (joinAttempts < maxJoinAttempts) {
            try {
              joinAttempts++;
              console.log(`Join attempt ${joinAttempts}/${maxJoinAttempts}`);

              // Try to join the room
              await webRTCRef.current.joinRoom(roomId, stream);
              console.log('Successfully joined WebRTC room');

              // If successful, break out of the loop
              joinError = null;
              break;
            } catch (error) {
              joinError = error;
              console.error(`Join attempt ${joinAttempts} failed:`, error);

              if (joinAttempts < maxJoinAttempts) {
                // Wait before trying again
                const delay = joinAttempts * 2000; // Increasing delay
                console.log(`Retrying in ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
              }
            }
          }

          // If all attempts failed, show error
          if (joinError) {
            console.error('All join attempts failed:', joinError);
            setMediaError(`Failed to join meeting room after ${maxJoinAttempts} attempts. Please try refreshing the page.`);

            // Show a more user-friendly message with a refresh button
            setConnectionStatus('Disconnected - Please refresh');
          } else {
            setConnectionStatus('Connected');
          }
        } catch (error) {
          console.error('Error in join process:', error);
          setMediaError(`Failed to join meeting room: ${error.message}`);
          setConnectionStatus('Error');
        }
      } else {
        console.warn('WebRTC service not initialized or no room ID');
        setConnectionStatus('Not initialized');
      }

      setMediaError('');
    } catch (error) {
      console.error('Media devices error:', error);

      // Provide more specific error messages based on the error type
      if (error.name === 'NotAllowedError') {
        setMediaError('Camera or microphone access denied. Please allow access in your browser settings.');
      } else if (error.name === 'NotFoundError') {
        setMediaError('No camera or microphone found. Please connect a device and try again.');
      } else if (error.name === 'NotReadableError') {
        setMediaError('Camera or microphone is already in use by another application.');
      } else {
        setMediaError(`Failed to access camera or microphone: ${error.message}`);
      }
    }
  };

  const toggleMicrophone = () => {
    if (localStreamRef.current) {
      const audioTrack = localStreamRef.current.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        const newMuteState = !audioTrack.enabled;
        setIsMuted(newMuteState);

        // Notify other participants about mute state change
        if (webRTCRef.current && roomId) {
          webRTCRef.current.socket.emit('user-mute-state', {
            roomId,
            userId: auth.currentUser.uid,
            isMuted: newMuteState
          });
        }
      }
    }
  };

  const toggleVideo = () => {
    if (localStreamRef.current) {
      const videoTrack = localStreamRef.current.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        const newVideoState = !videoTrack.enabled;
        setIsVideoOff(newVideoState);

        // Notify other participants about video state change
        if (webRTCRef.current && roomId) {
          webRTCRef.current.socket.emit('user-video-state', {
            roomId,
            userId: auth.currentUser.uid,
            isVideoOff: newVideoState
          });
        }
      }
    }
  };

  const endMeeting = async () => {
    // Leave WebRTC room first
    if (webRTCRef.current) {
      console.log('Leaving WebRTC room');
      webRTCRef.current.leaveRoom();
    }

    // Stop all tracks
    if (localStreamRef.current) {
      localStreamRef.current.getTracks().forEach(track => track.stop());
    }

    // Save both transcript and summary if there was a recording
    if (meetingTranscript && lastSavedFile) {
      try {
        // Save full transcript
        const transcriptFileData = {
          name: `Full Transcript - ${meeting?.title || 'Untitled'} - ${new Date().toLocaleString()}`,
          content: meetingTranscript,
          createdBy: auth.currentUser.uid,
          meetingId: roomId,
          enterpriseCode: meeting?.enterpriseCode || 'default',
          date: new Date(),
          type: 'transcript'
        };

        // Save both files to MongoDB
        await Promise.all([
          meetingFilesAPI.saveMeetingFile(lastSavedFile),  // Save summary
          meetingFilesAPI.saveMeetingFile(transcriptFileData)  // Save transcript
        ]);

        // Trigger files update event
        window.dispatchEvent(new Event('filesUpdated'));
      } catch (err) {
        console.error('Error saving meeting files:', err);
      }
    }

    // Clear remote streams and users
    setRemoteStreams({});
    setRemoteUsers({});

    navigate('/home');
  };

  const handleRecording = async () => {
    if (!voiceAssistantRef.current) {
      setRecordingError('Reconnaissance vocale non initialisée');
      return;
    }

    try {
      if (!isRecording) {
        // Démarrer l'enregistrement
        setIsRecording(true);
        setMeetingTranscript('');
        setCurrentTranscript('');
        setRecordingError('');
        await voiceAssistantRef.current.startRecording(
          (text) => {
            if (text && text.trim()) {
              // Update local transcript
              setMeetingTranscript(prev => {
                let updated = prev;
                if (!prev.trim().endsWith(text.trim())) {
                  // Format with speaker name
                  const newText = `You: ${text}`;
                  updated = prev ? `${prev}\n${newText}` : newText;
                }
                window.dispatchEvent(new CustomEvent('meetingTranscriptUpdate', { detail: updated }));
                return updated;
              });

              // Final result: also show as current transcript for a moment
              setCurrentTranscript(`You: ${text.trim()}`);

              // Share transcript with all participants in the room
              if (webRTCRef.current && webRTCRef.current.socket && roomId) {
                webRTCRef.current.socket.emit('share-transcript', {
                  roomId,
                  userId: auth.currentUser.uid,
                  transcript: text.trim(),
                  userName: auth.currentUser.displayName || 'You'
                });
              }
            }
          },
          // Live transcript callback (partial results)
          (partialText) => {
            if (partialText && partialText.trim()) {
              // Update local display with speaker name
              setCurrentTranscript(`You: ${partialText.trim()}`);

              // Share partial transcript with all participants in the room
              if (webRTCRef.current && webRTCRef.current.socket && roomId) {
                webRTCRef.current.socket.emit('share-transcript', {
                  roomId,
                  userId: auth.currentUser.uid,
                  transcript: partialText.trim(),
                  userName: auth.currentUser.displayName || 'You',
                  isPartial: true
                });
              }
            }
          }
        );
      } else {
        setIsRecording(false);
        const result = await voiceAssistantRef.current.stopRecording();

        if (!result || (!result.transcript && !meetingTranscript)) {
          setRecordingError('Aucune transcription à sauvegarder');
          return;
        }

        // Utiliser le résultat du service unifié ou générer un résumé
        let summary = '';
        let transcriptContent = result.transcript?.content || meetingTranscript;

        if (result.summary?.content) {
          summary = result.summary.content;
        } else {
          try {
            summary = await voiceAssistantRef.current?.generateMeetingMinutes(transcriptContent);
          } catch (err) {
            console.error('Erreur de génération de résumé:', err);
            summary = 'Échec de la génération du résumé. Veuillez consulter la transcription complète.';
          }
        }

        const timestamp = new Date().toISOString();
        const baseFileName = `Meeting_${meeting?.title || 'Untitled'}_${timestamp}`;

        const fileData = {
          name: baseFileName,
          content: summary,
          createdBy: auth.currentUser.uid,
          meetingId: roomId,
          enterpriseCode: meeting?.enterpriseCode || 'default',
          date: new Date(),
          type: 'summary',
          fullTranscript: transcriptContent
        };

        try {
          // Save summary file
          const savedFile = await meetingFilesAPI.saveMeetingFile(fileData);

          // Check if the response contains a valid ID (either _id or id)
          if (savedFile && (savedFile._id || savedFile.id)) {
            setLastSavedFile(savedFile);
            setRecordingError('✓ Enregistrement sauvegardé avec succès');
            window.dispatchEvent(new Event('filesUpdated'));

            try {
              // Save full transcript
              const transcriptFileData = {
                name: baseFileName,
                content: transcriptContent,
                createdBy: auth.currentUser.uid,
                meetingId: roomId,
                enterpriseCode: meeting?.enterpriseCode || 'default',
                date: new Date(),
                type: 'transcript'
              };

              await meetingFilesAPI.saveMeetingFile(transcriptFileData);

              // Navigate to Files page after saving both files
              navigate('/files');
            } catch (transcriptError) {
              console.error('Error saving transcript:', transcriptError);
              setRecordingError('Summary saved, but transcript failed to save. You can still access the summary in Files.');

              // Still navigate to Files page to see at least the summary
              navigate('/files');
            }
          } else {
            console.error('Invalid response from server:', savedFile);
            throw new Error('Échec de la sauvegarde de l\'enregistrement - réponse serveur invalide');
          }
        } catch (saveError) {
          console.error('Error saving files:', saveError);
          throw new Error(`Échec de la sauvegarde de l'enregistrement: ${saveError.message}`);
        }
      }
    } catch (error) {
      setRecordingError(error.message || 'Erreur avec l\'enregistrement');
      setIsRecording(false);
      setCurrentTranscript('');
    }
  };

  const handleSaveSummary = async () => {
    try {
      if (!currentTranscript) {
        setRecordingError('Aucun contenu de réunion disponible à sauvegarder');
        return;
      }

      if (!auth.currentUser) {
        setRecordingError('Vous devez être connecté pour sauvegarder le résumé de réunion');
        return;
      }

      const minutes = voiceAssistantRef.current?.generateMeetingMinutes(currentTranscript);

      const fileData = {
        name: `Meeting Summary - ${meeting?.title || 'Untitled'} - ${new Date().toLocaleString()}`,
        content: minutes,
        createdBy: auth.currentUser.uid,
        meetingId: roomId,
        enterpriseCode: meeting?.enterpriseCode || 'default',
        date: new Date(),
        type: 'summary'
      };

      await meetingFilesAPI.saveMeetingFile(fileData);
      setRecordingError('✓ Résumé de réunion sauvegardé avec succès');
      setTimeout(() => setRecordingError(''), 3000);
    } catch (error) {
      console.error('Error saving summary:', error);
      setRecordingError('Échec de la sauvegarde du résumé de réunion');
    }
  };

  const handleDownloadSummary = () => {
    if (lastSavedFile) {
      const blob = new Blob([lastSavedFile.content], { type: 'text/plain;charset=utf-8' });
      saveAs(blob, lastSavedFile.name);
    } else {
      setRecordingError('Aucun fichier de résumé à télécharger');
    }
  };

  const handleShowChat = () => setShowChat(v => !v);

  const handleShowSettings = () => setShowSettings(v => !v);

  const handleToggleAssistant = () => setIsVoiceAssistantActive(v => !v);

  const toggleScreenShare = async () => {
    try {
      if (!isScreenSharing) {
        // Start screen sharing
        const screenStream = await navigator.mediaDevices.getDisplayMedia({
          video: true,
          audio: true
        });

        // Save the current video stream to restore later
        if (localVideoRef.current && localVideoRef.current.srcObject) {
          const currentStream = localVideoRef.current.srcObject;
          localVideoRef.current.srcObject = screenStream;

          // When screen sharing stops (user clicks "Stop sharing" in browser UI)
          screenStream.getVideoTracks()[0].onended = () => {
            localVideoRef.current.srcObject = currentStream;
            setIsScreenSharing(false);
          };

          setIsScreenSharing(true);
        }
      } else {
        // Stop screen sharing
        if (localVideoRef.current && localVideoRef.current.srcObject) {
          // Get all tracks from the screen sharing stream and stop them
          const tracks = localVideoRef.current.srcObject.getTracks();
          tracks.forEach(track => track.stop());

          // Restore the camera stream
          initializeMediaDevices();
          setIsScreenSharing(false);
        }
      }
    } catch (error) {
      console.error('Screen sharing error:', error);
      setMediaError('Failed to share screen. Please ensure you have granted the necessary permissions.');
    }
  };

  const toggleLayout = () => {
    // Cycle through layouts: grid-1x1 -> grid-2x2 -> spotlight -> grid-1x1
    if (currentLayout === 'grid-1x1') {
      setCurrentLayout('grid-2x2');
    } else if (currentLayout === 'grid-2x2') {
      setCurrentLayout('spotlight');
    } else {
      setCurrentLayout('grid-1x1');
    }
  };

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
    // Apply dark/light mode to the UI
    document.body.classList.toggle('light-mode', !isDarkMode);
  };

  return (
    <div className="h-screen bg-gray-900 flex flex-col">
      {/* Chat Panel */}
      <ChatPanel
        isOpen={showChat}
        onClose={handleShowChat}
        roomId={roomId}
        user={auth.currentUser}
      />

      {/* Settings Panel */}
      <SettingsPanel
        isOpen={showSettings}
        onClose={handleShowSettings}
      />

      {/* Main content */}
      <div className={`flex-1 flex layout-${currentLayout}`}>
        {/* Video grid */}
        <div className="flex-1 p-4 grid gap-4 video-grid">
          {/* Local video */}
          <div className="bg-gray-800 rounded-lg aspect-video relative overflow-hidden">
            <video
              ref={localVideoRef}
              autoPlay
              playsInline
              muted
              className={`w-full h-full object-cover ${isVideoOff ? 'hidden' : ''}`}
            />
            {isVideoOff && (
              <div className="absolute inset-0 flex items-center justify-center">
                <VideoOff className="w-16 h-16 text-gray-500" />
              </div>
            )}
            <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 px-3 py-1 rounded-lg text-white text-sm">
              You {isMuted && '(Muted)'}
            </div>
          </div>

          {/* Remote videos with improved handling */}
          {Object.entries(remoteStreams).map(([userId, stream]) => {
            // Check if stream has video tracks
            const hasVideoTracks = stream.getVideoTracks().length > 0;
            const hasAudioTracks = stream.getAudioTracks().length > 0;
            const videoEnabled = hasVideoTracks && stream.getVideoTracks()[0].enabled;

            return (
              <div key={userId} className="bg-gray-800 rounded-lg aspect-video relative overflow-hidden">
                {hasVideoTracks ? (
                  <video
                    autoPlay
                    playsInline
                    className={`w-full h-full object-cover ${!videoEnabled ? 'opacity-0' : ''}`}
                    ref={el => {
                      if (el && el.srcObject !== stream) {
                        console.log(`Setting video element for ${userId} with stream ID ${stream.id}`);
                        el.srcObject = stream;

                        // Add error handler
                        el.onerror = (e) => {
                          console.error(`Video element error for ${userId}:`, e);
                        };

                        // Log when video starts playing
                        el.onplaying = () => {
                          console.log(`Video for ${userId} started playing`);
                        };
                      }
                    }}
                  />
                ) : (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <VideoOff className="w-16 h-16 text-gray-500" />
                  </div>
                )}

                {/* Show video off indicator if video is disabled */}
                {(hasVideoTracks && !videoEnabled) && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <VideoOff className="w-16 h-16 text-gray-500" />
                  </div>
                )}

                {/* Show audio indicator */}
                <div className="absolute top-4 right-4">
                  {hasAudioTracks ? (
                    <Mic className="w-5 h-5 text-green-400" />
                  ) : (
                    <MicOff className="w-5 h-5 text-red-400" />
                  )}
                </div>

                <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 px-3 py-1 rounded-lg text-white text-sm">
                  {remoteUsers[userId] && !remoteUsers[userId].startsWith('User-')
                    ? remoteUsers[userId]
                    : (
                      <span className="flex items-center">
                        <span className="animate-pulse mr-1">●</span>
                        {remoteUsers[userId] || `User-${userId.substring(0, 5)}`}
                      </span>
                    )
                  }
                </div>
              </div>
            );
          })}

          {/* Empty placeholders if needed */}
          {Object.keys(remoteStreams).length === 0 && (
            <div className="bg-gray-800 rounded-lg aspect-video flex items-center justify-center">
              <div className="text-center text-gray-400">
                <Users className="w-16 h-16 mx-auto mb-2" />
                <p>Waiting for participants to join...</p>
                <p className="text-sm mt-2">Share the meeting code: <span className="font-bold">{roomId}</span></p>
              </div>
            </div>
          )}

          {/* Additional video placeholders for grid-2x2 and spotlight layouts */}
          {(currentLayout === 'grid-2x2' || currentLayout === 'spotlight') &&
           Object.keys(remoteStreams).length === 0 && (
            <>
              <div className="bg-gray-800 rounded-lg aspect-video flex items-center justify-center">
                <Users className="w-16 h-16 text-gray-500" />
              </div>
              <div className="bg-gray-800 rounded-lg aspect-video flex items-center justify-center">
                <Users className="w-16 h-16 text-gray-500" />
              </div>
            </>
          )}
        </div>

        {/* Sidebar */}
        <div className="w-80 bg-gray-800 p-4 flex flex-col">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-white text-lg font-semibold">Meeting Details</h2>
            <span className="text-gray-400">Room: {roomId}</span>
          </div>
          {/* Add live transcript toggle icon */}
          <div className="flex items-center mb-2">
            <button
              onClick={() => setShowLiveTranscript(v => !v)}
              className="ml-auto p-2 rounded hover:bg-gray-700"
              title={showLiveTranscript ? "Hide Live Transcript" : "Show Live Transcript"}
            >
              {showLiveTranscript ? <EyeOff className="text-purple-400" /> : <Eye className="text-purple-400" />}
            </button>
          </div>
          <div className="flex-1">
            {mediaError && (
              <div className="mb-4 p-3 bg-red-900 text-red-200 rounded">
                {mediaError}
                {mediaError.includes('refresh') && (
                  <button
                    onClick={() => window.location.reload()}
                    className="mt-2 px-3 py-1 bg-red-700 hover:bg-red-600 text-white rounded-md w-full"
                  >
                    Refresh Page
                  </button>
                )}
              </div>
            )}
            {recordingError && (
              <div className="mb-4 p-3 bg-red-900 text-red-200 rounded">
                {recordingError}
              </div>
            )}
            {isRecording && (
              <div className="mb-4 p-3 bg-purple-900 text-purple-200 rounded flex flex-col">
                <div className="flex items-center mb-2">
                  <CircleDot className="w-4 h-4 mr-2 animate-pulse" />
                  Recording in progress...
                </div>
                {/* Real-time transcript under "recording in progress" */}
                {meetingTranscript && (
                  <div className="bg-purple-100 text-purple-900 rounded-lg p-2 mt-2 max-h-32 overflow-y-auto text-xs whitespace-pre-line">
                    {meetingTranscript}

                    {/* Download button for transcript */}
                    <div className="mt-2 flex justify-end">
                      <button
                        onClick={() => {
                          const blob = new Blob([meetingTranscript], { type: 'text/plain;charset=utf-8' });
                          saveAs(blob, `Meeting_Transcript_${new Date().toISOString().slice(0,10)}.txt`);
                        }}
                        className="text-xs bg-purple-700 text-white px-2 py-1 rounded hover:bg-purple-800"
                        title="Download current transcript"
                      >
                        Download Transcript
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Download button for last saved transcript and summary */}
            {lastSavedFile && !isRecording && (
              <div className="mb-4 p-3 bg-green-800 text-green-100 rounded flex flex-col">
                <div className="flex items-center justify-between mb-2">
                  <span>Last saved recording</span>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => {
                        const blob = new Blob([lastSavedFile.content], { type: 'text/plain;charset=utf-8' });
                        saveAs(blob, `${lastSavedFile.name}_summary.txt`);
                      }}
                      className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                      title="Download summary"
                    >
                      Summary
                    </button>
                    <button
                      onClick={() => {
                        const blob = new Blob([lastSavedFile.fullTranscript || meetingTranscript], { type: 'text/plain;charset=utf-8' });
                        saveAs(blob, `${lastSavedFile.name}_transcript.txt`);
                      }}
                      className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                      title="Download full transcript"
                    >
                      Transcript
                    </button>
                  </div>
                </div>
              </div>
            )}

            {showParticipants && (
              <div className="mt-4">
                <h3 className="text-white text-md font-semibold mb-2">Participants ({participants.length})</h3>
                <ul className="space-y-2">
                  {/* Show current user first */}
                  <li className="text-gray-300 flex items-center">
                    <Users className="w-4 h-4 mr-2" />
                    You {isMuted && '(Muted)'} {isVideoOff && '(Video Off)'}
                  </li>

                  {/* Show remote participants */}
                  {Object.entries(remoteUsers).map(([userId, userName]) => (
                    <li key={userId} className="text-gray-300 flex items-center">
                      <Users className="w-4 h-4 mr-2" />
                      {userName}
                    </li>
                  ))}

                  {/* Show connection status */}
                  <li className="text-xs text-gray-400 mt-2">
                    Connection status: {connectionStatus}
                  </li>
                </ul>

                {/* Show meeting code for sharing */}
                <div className="mt-4 p-3 bg-gray-700 rounded-lg">
                  <p className="text-sm text-gray-300 mb-1">Share this code with friends:</p>
                  <div className="flex items-center">
                    <code className="bg-gray-800 text-green-400 px-2 py-1 rounded flex-1 text-sm">{roomId}</code>
                    <button
                      className="ml-2 p-1 bg-blue-600 rounded hover:bg-blue-700"
                      onClick={() => {
                        navigator.clipboard.writeText(roomId);
                        setRecordingError('Meeting code copied to clipboard!');
                        setTimeout(() => setRecordingError(''), 2000);
                      }}
                      title="Copy to clipboard"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Chat and Settings panels are now rendered outside the sidebar */}
            {/* Move live transcript block to always show when toggled, not just when recording */}
            {showLiveTranscript && (
              <div className="mb-4 p-3 bg-purple-100 text-purple-900 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <p className="font-medium">Live Transcript:</p>
                  {currentTranscript && (
                    <button
                      onClick={() => {
                        const blob = new Blob([currentTranscript], { type: 'text/plain;charset=utf-8' });
                        saveAs(blob, `Live_Transcript_${new Date().toISOString().slice(0,19).replace(/:/g, '-')}.txt`);
                      }}
                      className="text-xs bg-purple-700 text-white px-2 py-1 rounded hover:bg-purple-800"
                      title="Download current transcript"
                    >
                      Download
                    </button>
                  )}
                </div>
                <div className="text-xs whitespace-pre-line max-h-32 overflow-y-auto">
                  {currentTranscript && currentTranscript.trim()
                    ? currentTranscript
                    : <span className="text-gray-400">No speech detected yet.</span>}
                </div>
                <p className="text-xs mt-2 text-purple-700">
                  <span className="font-medium">Note:</span> All participants can see this transcript in real-time.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Controls - Updated to match the UI in the image */}
      <div className="bg-gray-800 p-4">
        <div className="max-w-3xl mx-auto flex items-center justify-center space-x-4">
          {/* Left side controls */}
          <div className="flex space-x-2">
            <button
              onClick={toggleMicrophone}
              className={`p-3 rounded-full ${isMuted ? 'bg-red-500' : 'bg-gray-700'} hover:bg-opacity-80`}
              title={isMuted ? t('meeting_controls.unmute') : t('meeting_controls.mute')}
            >
              {isMuted ? <MicOff className="text-white w-5 h-5" /> : <Mic className="text-white w-5 h-5" />}
            </button>

            <button
              onClick={toggleVideo}
              className={`p-3 rounded-full ${isVideoOff ? 'bg-red-500' : 'bg-gray-700'} hover:bg-opacity-80`}
              title={isVideoOff ? t('meeting_controls.start_video') : t('meeting_controls.stop_video')}
            >
              {isVideoOff ? <VideoOff className="text-white w-5 h-5" /> : <Video className="text-white w-5 h-5" />}
            </button>
          </div>

          {/* Center controls */}
          <div className="flex space-x-2">
            <button
              onClick={handleShowChat}
              className={`p-3 rounded-full ${showChat ? 'bg-blue-500' : 'bg-gray-700'} hover:bg-opacity-80`}
              title={t('meeting_controls.chat')}
            >
              <MessageSquare className="text-white w-5 h-5" />
            </button>

            <button
              onClick={() => setShowParticipants(!showParticipants)}
              className={`p-3 rounded-full ${showParticipants ? 'bg-blue-500' : 'bg-gray-700'} hover:bg-opacity-80`}
              title={t('meeting_controls.participants')}
            >
              <Users className="text-white w-5 h-5" />
            </button>

            <button
              onClick={handleShowSettings}
              className={`p-3 rounded-full ${showSettings ? 'bg-blue-500' : 'bg-gray-700'} hover:bg-opacity-80`}
              title={t('meeting_controls.settings')}
            >
              <Settings className="text-white w-5 h-5" />
            </button>
          </div>

          {/* Right side controls */}
          <div className="flex space-x-2">
            <button
              onClick={toggleScreenShare}
              className={`p-3 rounded-full ${isScreenSharing ? 'bg-green-500' : 'bg-gray-700'} hover:bg-opacity-80`}
              title={isScreenSharing ? t('meeting_controls.stop_sharing') : t('meeting_controls.share_screen')}
            >
              {isScreenSharing ? <ScreenShare className="text-white w-5 h-5" /> : <Share2 className="text-white w-5 h-5" />}
            </button>

            <button
              onClick={handleRecording}
              className={`p-3 rounded-full ${isRecording ? 'bg-red-500 animate-pulse' : 'bg-gray-700'} hover:bg-opacity-80`}
              title={isRecording ? t('meeting_controls.stop_recording') : t('meeting_controls.start_recording')}
              disabled={isModelLoading}
            >
              {isRecording ? <StopCircle className="text-white w-5 h-5" /> : <CircleDot className="text-white w-5 h-5" />}
            </button>

            {/* Download button - only enabled when there's content to download */}
            <button
              onClick={() => setShowDownloadOptions(!showDownloadOptions)}
              className={`p-3 rounded-full ${(meetingTranscript || lastSavedFile) ? 'bg-green-600' : 'bg-gray-700 opacity-50'} hover:bg-opacity-80`}
              title="Download transcript or summary"
              disabled={!meetingTranscript && !lastSavedFile}
            >
              <FileText className="text-white w-5 h-5" />
            </button>

            <button
              onClick={toggleLayout}
              className={`p-3 rounded-full bg-gray-700 hover:bg-opacity-80`}
              title={t('meeting_controls.change_layout')}
            >
              {currentLayout === 'grid-1x1' && <Layout className="text-white w-5 h-5" />}
              {currentLayout === 'grid-2x2' && <LayoutGrid className="text-white w-5 h-5" />}
              {currentLayout === 'spotlight' && <Users className="text-white w-5 h-5" />}
            </button>
          </div>

          {/* End call button - centered and highlighted */}
          <button
            onClick={endMeeting}
            className="p-3 rounded-full bg-red-500 hover:bg-red-600"
            title={t('meeting_controls.end_meeting')}
          >
            <Phone className="text-white w-5 h-5" />
          </button>

          {/* Theme toggle button */}
          <button
            onClick={toggleDarkMode}
            className="p-3 rounded-full bg-gray-700 hover:bg-opacity-80"
            title={isDarkMode ? "Light Mode" : "Dark Mode"}
          >
            {isDarkMode ? <Sun className="text-white w-5 h-5" /> : <Moon className="text-white w-5 h-5" />}
          </button>

          {/* WebRTC Diagnostic button */}
          <button
            onClick={() => {
              console.log('WebRTC Diagnostic Report:');
              webrtcDiagnostic.printReport();
              setRecordingError('WebRTC diagnostic report printed to console (press F12 to view)');

              // Also log detailed info about remote streams
              console.log('Remote Streams:', Object.entries(remoteStreams).map(([userId, stream]) => ({
                userId,
                userName: remoteUsers[userId] || `User-${userId.substring(0, 5)}`,
                streamId: stream.id,
                videoTracks: stream.getVideoTracks().map(track => ({
                  id: track.id,
                  enabled: track.enabled,
                  muted: track.muted,
                  readyState: track.readyState
                })),
                audioTracks: stream.getAudioTracks().map(track => ({
                  id: track.id,
                  enabled: track.enabled,
                  muted: track.muted,
                  readyState: track.readyState
                }))
              })));

              // Log WebRTC connection
              if (webRTCRef.current) {
                console.log('WebRTC Service:', {
                  roomId: webRTCRef.current.roomId,
                  userId: webRTCRef.current.userId,
                  userName: webRTCRef.current.userName,
                  socketConnected: webRTCRef.current.socket?.connected,
                  peerConnections: Array.from(webRTCRef.current.peers.entries()).map(([peerId, peer]) => ({
                    peerId,
                    peerName: peer.userName,
                    connectionState: peer.peerConnection.connectionState,
                    iceConnectionState: peer.peerConnection.iceConnectionState,
                    signalingState: peer.peerConnection.signalingState
                  }))
                });
              }
            }}
            className="p-3 rounded-full bg-purple-700 hover:bg-purple-600 ml-auto"
            title="WebRTC Diagnostics"
          >
            <Settings className="text-white w-5 h-5" />
          </button>
        </div>

        {/* Download options dropdown */}
        {showDownloadOptions && (
          <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50" onClick={() => setShowDownloadOptions(false)}>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 w-80" onClick={e => e.stopPropagation()}>
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">Download Options</h3>

              {/* Current transcript download option - always available if there's a transcript */}
              {meetingTranscript && (
                <button
                  onClick={() => {
                    const blob = new Blob([meetingTranscript], { type: 'text/plain;charset=utf-8' });
                    saveAs(blob, `Meeting_Transcript_${new Date().toISOString().slice(0,19).replace(/:/g, '-')}.txt`);
                    setShowDownloadOptions(false);
                  }}
                  className="w-full mb-2 px-4 py-2 text-left text-sm bg-purple-600 text-white rounded hover:bg-purple-700 flex items-center"
                >
                  <FileText className="w-4 h-4 mr-2" />
                  Download Current Transcript
                </button>
              )}

              {/* Last saved file options */}
              {lastSavedFile && (
                <>
                  <button
                    onClick={() => {
                      const blob = new Blob([lastSavedFile.content], { type: 'text/plain;charset=utf-8' });
                      saveAs(blob, `${lastSavedFile.name}_summary.txt`);
                      setShowDownloadOptions(false);
                    }}
                    className="w-full mb-2 px-4 py-2 text-left text-sm bg-green-600 text-white rounded hover:bg-green-700 flex items-center"
                  >
                    <FileText className="w-4 h-4 mr-2" />
                    Download Summary
                  </button>

                  {lastSavedFile.fullTranscript && (
                    <button
                      onClick={() => {
                        const blob = new Blob([lastSavedFile.fullTranscript], { type: 'text/plain;charset=utf-8' });
                        saveAs(blob, `${lastSavedFile.name}_full.txt`);
                        setShowDownloadOptions(false);
                      }}
                      className="w-full px-4 py-2 text-left text-sm bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center"
                    >
                      <FileText className="w-4 h-4 mr-2" />
                      Download Full Transcript
                    </button>
                  )}
                </>
              )}

              {/* Cancel button */}
              <button
                onClick={() => setShowDownloadOptions(false)}
                className="w-full mt-3 px-4 py-2 text-center text-sm bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600"
              >
                Cancel
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Meet;