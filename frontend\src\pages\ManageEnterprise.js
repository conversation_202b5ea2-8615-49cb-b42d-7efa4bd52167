import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { enterpriseAPI, enterpriseUsersAPI, userAPI } from '../services/mongodb';
import { useTranslation } from 'react-i18next';

const ManageEnterprise = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    address: '',
    email: '',
    phone: '',
    founders: '',
    numberOfEmployees: '',
  });
  const [activeTab, setActiveTab] = useState('enterprise');
  const [users, setUsers] = useState([]);
  const [editUserId, setEditUserId] = useState(null);
  const [editUserData, setEditUserData] = useState({});

  useEffect(() => {
    if (location.state?.enterprise) {
      const { enterprise } = location.state;
      setFormData({
        name: enterprise.name || '',
        address: enterprise.address || '',
        email: enterprise.email || '',
        phone: enterprise.phone || '',
        founders: Array.isArray(enterprise.founders) ? enterprise.founders.join(', ') : '',
        numberOfEmployees: enterprise.numberOfEmployees || '',
      });
    }
  }, [location.state]);

  useEffect(() => {
    if (activeTab === 'users' && location.state?.enterprise) {
      enterpriseUsersAPI.getUsers(location.state.enterprise.code)
        .then(setUsers)
        .catch(console.error);
    }
  }, [activeTab, location.state]);

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setLoading(true);

    try {
      const enterpriseCode = location.state?.enterprise?.code;
      if (!enterpriseCode) {
        throw new Error('Enterprise code not found');
      }

      const updatedData = {
        ...formData,
        founders: formData.founders.split(',').map(f => f.trim()),
        numberOfEmployees: parseInt(formData.numberOfEmployees, 10)
      };

      await enterpriseAPI.updateEnterprise(enterpriseCode, updatedData);
      setSuccess(t('manage_enterprise_details.enterprise_updated'));
    } catch (error) {
      console.error('Error updating enterprise:', error);
      setError(error.message || t('manage_enterprise_details.update_failed'));
    } finally {
      setLoading(false);
    }
  };

  const handleUserUpdate = (userId, updatedFields) => {
    setUsers(users.map(user => user._id === userId ? { ...user, ...updatedFields } : user));
  };

  const handleUserDelete = (userId) => {
    setUsers(users.filter(user => user._id !== userId));
  };

  const handleEditUser = (user) => {
    setEditUserId(user._id);
    setEditUserData({
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone,
      position: user.position,
      firebaseUid: user.firebaseUid // Add this for reference
    });
  };

  const handleEditUserChange = (e) => {
    setEditUserData({
      ...editUserData,
      [e.target.name]: e.target.value,
    });
  };

  const handleUpdateUser = async (userId) => {
    try {
      // Find the user object to get firebaseUid
      const user = users.find(u => u._id === userId);
      if (!user) {
        setError(t('manage_enterprise_details.user_not_found'));
        return;
      }
      await userAPI.updateUser(user.firebaseUid, editUserData);
      setUsers(users.map(u => u._id === userId ? { ...u, ...editUserData } : u));
      setEditUserId(null);
      setSuccess(t('manage_enterprise_details.user_updated'));
    } catch (error) {
      setError(error.message || t('manage_enterprise_details.user_update_failed'));
    }
  };

  return (
    <div className="min-h-screen theme-page-bg">
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="flex items-center mb-6">
          <button
            onClick={() => navigate('/home')}
            className="mr-4 theme-text hover:opacity-80"
          >
            <ArrowLeft className="h-6 w-6" />
          </button>
          <h1 className="text-2xl font-bold theme-text">{t('manage_enterprise_details.title')}</h1>
        </div>

        <div className="mb-6">
          <div className="border-b">
            <nav className="-mb-px flex">
              <button
                onClick={() => setActiveTab('enterprise')}
                className={`${
                  activeTab === 'enterprise'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent theme-text hover:opacity-80'
                } w-1/2 py-4 px-1 text-center border-b-2 font-medium`}
              >
                {t('manage_enterprise_details.manage_enterprise_tab')}
              </button>
              <button
                onClick={() => setActiveTab('users')}
                className={`${
                  activeTab === 'users'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent theme-text hover:opacity-80'
                } w-1/2 py-4 px-1 text-center border-b-2 font-medium`}
              >
                {t('manage_enterprise_details.manage_users_tab')}
              </button>
            </nav>
          </div>
        </div>

        {error && (
          <div className="mb-4 p-4 theme-container border rounded-lg">
            <p className="text-red-600 dark:text-red-400">{error}</p>
          </div>
        )}

        {success && (
          <div className="mb-4 p-4 theme-container border rounded-lg">
            <p className="text-green-600 dark:text-green-400">{success}</p>
          </div>
        )}

        {activeTab === 'enterprise' ? (
          <form onSubmit={handleSubmit} className="theme-container rounded-lg shadow p-6 space-y-6">
            <div>
              <label className="block text-sm font-medium theme-label mb-1">
                {t('manage_enterprise_details.enterprise_name')}
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border rounded-md theme-input focus:outline-none focus:ring-2"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium theme-label mb-1">
                {t('manage_enterprise_details.address')}
              </label>
              <input
                type="text"
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border rounded-md theme-input focus:outline-none focus:ring-2"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium theme-label mb-1">
                {t('manage_enterprise_details.email')}
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border rounded-md theme-input focus:outline-none focus:ring-2"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium theme-label mb-1">
                {t('manage_enterprise_details.phone')}
              </label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border rounded-md theme-input focus:outline-none focus:ring-2"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium theme-label mb-1">
                {t('manage_enterprise_details.founders')}
              </label>
              <input
                type="text"
                name="founders"
                value={formData.founders}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border rounded-md theme-input focus:outline-none focus:ring-2"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium theme-label mb-1">
                {t('manage_enterprise_details.number_of_employees')}
              </label>
              <input
                type="number"
                name="numberOfEmployees"
                value={formData.numberOfEmployees}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border rounded-md theme-input focus:outline-none focus:ring-2"
                required
                min="1"
              />
            </div>

            <button
              type="submit"
              disabled={loading}
              className={`w-full py-2 px-4 rounded-md theme-button theme-button-primary ${
                loading ? 'opacity-50' : ''
              } transition-colors`}
            >
              {loading ? t('manage_enterprise_details.updating') : t('manage_enterprise_details.update_enterprise')}
            </button>
          </form>
        ) : (
          <div className="theme-container rounded-lg shadow p-6">
            <div className="space-y-6">
              {users.map(user => (
                <div key={user._id} className="border rounded-lg p-4 theme-container">
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium theme-label">{t('manage_enterprise_details.name')}</label>
                      {editUserId === user._id ? (
                        <div className="flex gap-2">
                          <input
                            type="text"
                            name="firstName"
                            value={editUserData.firstName}
                            onChange={handleEditUserChange}
                            className="mt-1 block w-full rounded-md border theme-input px-3 py-2"
                          />
                          <input
                            type="text"
                            name="lastName"
                            value={editUserData.lastName}
                            onChange={handleEditUserChange}
                            className="mt-1 block w-full rounded-md border theme-input px-3 py-2"
                          />
                        </div>
                      ) : (
                        <input
                          type="text"
                          value={`${user.firstName} ${user.lastName}`}
                          className="mt-1 block w-full rounded-md border theme-input px-3 py-2"
                          readOnly
                        />
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium theme-label">{t('manage_enterprise_details.email')}</label>
                      {editUserId === user._id ? (
                        <input
                          type="email"
                          name="email"
                          value={editUserData.email}
                          onChange={handleEditUserChange}
                          className="mt-1 block w-full rounded-md border theme-input px-3 py-2"
                        />
                      ) : (
                        <input
                          type="email"
                          value={user.email}
                          className="mt-1 block w-full rounded-md border theme-input px-3 py-2"
                          readOnly
                        />
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium theme-label">{t('manage_enterprise_details.phone')}</label>
                      {editUserId === user._id ? (
                        <input
                          type="tel"
                          name="phone"
                          value={editUserData.phone}
                          onChange={handleEditUserChange}
                          className="mt-1 block w-full rounded-md border theme-input px-3 py-2"
                        />
                      ) : (
                        <input
                          type="tel"
                          value={user.phone}
                          className="mt-1 block w-full rounded-md border theme-input px-3 py-2"
                          readOnly
                        />
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium theme-label">{t('manage_enterprise_details.position')}</label>
                      {editUserId === user._id ? (
                        <input
                          type="text"
                          name="position"
                          value={editUserData.position}
                          onChange={handleEditUserChange}
                          className="mt-1 block w-full rounded-md border theme-input px-3 py-2"
                        />
                      ) : (
                        <input
                          type="text"
                          value={user.position}
                          className="mt-1 block w-full rounded-md border theme-input px-3 py-2"
                          readOnly
                        />
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium theme-label">{t('manage_enterprise_details.status')}</label>
                      <select
                        value={user.status}
                        onChange={(e) => handleUserUpdate(user._id, { status: e.target.value })}
                        className="mt-1 block w-full rounded-md border theme-input px-3 py-2"
                        disabled
                      >
                        <option value="active">{t('manage_enterprise_details.active')}</option>
                        <option value="inactive">{t('manage_enterprise_details.inactive')}</option>
                      </select>
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2">
                    {editUserId === user._id ? (
                      <>
                        <button
                          onClick={() => handleUpdateUser(user._id)}
                          className="theme-button theme-button-primary px-4 py-2 rounded"
                        >
                          {t('manage_enterprise_details.save')}
                        </button>
                        <button
                          onClick={() => setEditUserId(null)}
                          className="bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded hover:opacity-80"
                        >
                          {t('manage_enterprise_details.cancel')}
                        </button>
                      </>
                    ) : (
                      <>
                        <button
                          onClick={() => handleEditUser(user)}
                          className="bg-yellow-500 text-white px-4 py-2 rounded hover:opacity-80"
                        >
                          {t('manage_enterprise_details.update')}
                        </button>
                        <button
                          onClick={() => handleUserDelete(user._id)}
                          className="bg-red-500 text-white px-4 py-2 rounded hover:opacity-80"
                        >
                          {t('manage_enterprise_details.delete_user')}
                        </button>
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ManageEnterprise;