import React from 'react';
import LanguageToggle from './LanguageToggle';
import { useTheme } from '../contexts/ThemeContext';
import { useLocation } from 'react-router-dom';

const LanguageToggleWrapper = () => {
  const { darkMode } = useTheme();
  const location = useLocation();

  // Hide on Landing and Welcome pages (they have their own language switchers)
  const isLandingOrWelcome = location.pathname === '/' || location.pathname === '/welcome';

  if (isLandingOrWelcome) {
    return null;
  }

  return (
    <div className={`fixed bottom-6 right-24 z-50 ${
      darkMode ? 'filter-none' : 'drop-shadow-md'
    }`}>
      <LanguageToggle />
    </div>
  );
};

export default LanguageToggleWrapper;
