import React from 'react';
import ThemeToggle from './ThemeToggle';
import { useTheme } from '../contexts/ThemeContext';
import { useLocation } from 'react-router-dom';

const ThemeToggleWrapper = () => {
  const { darkMode } = useTheme();
  const location = useLocation();

  // Hide on Landing and Welcome pages
  const isLandingOrWelcome = location.pathname === '/' || location.pathname === '/welcome';

  if (isLandingOrWelcome) {
    return null;
  }

  return (
    <div className={`fixed bottom-6 right-6 z-50 ${
      darkMode ? 'filter-none' : 'drop-shadow-md'
    }`}>
      <ThemeToggle />
    </div>
  );
};

export default ThemeToggleWrapper;
