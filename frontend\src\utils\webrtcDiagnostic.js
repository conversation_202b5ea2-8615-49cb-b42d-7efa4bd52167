/**
 * WebRTC Diagnostic Tool
 * This utility helps diagnose WebRTC connection issues by providing detailed information
 * about the connection state, ICE candidates, and media streams.
 */

class WebRTCDiagnostic {
  constructor() {
    this.stats = {
      connections: {},
      iceServers: [],
      localStreams: [],
      remoteStreams: []
    };
  }

  /**
   * Initialize the diagnostic tool
   */
  init() {
    console.log('WebRTC Diagnostic Tool initialized');
    
    // Add global error handler for unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
      this.logError('Unhandled promise rejection', event.reason);
    });
    
    return this;
  }

  /**
   * Monitor a peer connection
   * @param {RTCPeerConnection} peerConnection - The peer connection to monitor
   * @param {string} peerId - An identifier for this peer connection
   */
  monitorPeerConnection(peerConnection, peerId) {
    if (!peerConnection) {
      console.error('Cannot monitor null peer connection');
      return;
    }
    
    console.log(`Monitoring peer connection for ${peerId}`);
    
    // Store connection info
    this.stats.connections[peerId] = {
      connectionState: peerConnection.connectionState,
      iceConnectionState: peerConnection.iceConnectionState,
      signalingState: peerConnection.signalingState,
      iceCandidates: {
        local: [],
        remote: []
      },
      events: []
    };
    
    // Monitor connection state changes
    peerConnection.onconnectionstatechange = () => {
      const state = peerConnection.connectionState;
      console.log(`Connection state changed for ${peerId}: ${state}`);
      this.stats.connections[peerId].connectionState = state;
      this.logEvent(peerId, 'connectionStateChange', { state });
    };
    
    // Monitor ICE connection state changes
    peerConnection.oniceconnectionstatechange = () => {
      const state = peerConnection.iceConnectionState;
      console.log(`ICE connection state changed for ${peerId}: ${state}`);
      this.stats.connections[peerId].iceConnectionState = state;
      this.logEvent(peerId, 'iceConnectionStateChange', { state });
    };
    
    // Monitor signaling state changes
    peerConnection.onsignalingstatechange = () => {
      const state = peerConnection.signalingState;
      console.log(`Signaling state changed for ${peerId}: ${state}`);
      this.stats.connections[peerId].signalingState = state;
      this.logEvent(peerId, 'signalingStateChange', { state });
    };
    
    // Monitor ICE candidate events
    peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        console.log(`New ICE candidate for ${peerId}:`, event.candidate);
        this.stats.connections[peerId].iceCandidates.local.push({
          candidate: event.candidate.candidate,
          sdpMid: event.candidate.sdpMid,
          sdpMLineIndex: event.candidate.sdpMLineIndex,
          timestamp: new Date().toISOString()
        });
        this.logEvent(peerId, 'iceCandidate', { candidate: event.candidate.candidate });
      }
    };
    
    // Monitor track events
    peerConnection.ontrack = (event) => {
      console.log(`Track received from ${peerId}:`, event.track);
      
      // Log track details
      const trackInfo = {
        id: event.track.id,
        kind: event.track.kind,
        enabled: event.track.enabled,
        readyState: event.track.readyState,
        muted: event.track.muted,
        streamId: event.streams[0]?.id
      };
      
      this.logEvent(peerId, 'trackReceived', trackInfo);
      
      // Monitor track mute/unmute events
      event.track.onmute = () => {
        console.log(`Track ${event.track.id} muted from ${peerId}`);
        this.logEvent(peerId, 'trackMuted', { trackId: event.track.id });
      };
      
      event.track.onunmute = () => {
        console.log(`Track ${event.track.id} unmuted from ${peerId}`);
        this.logEvent(peerId, 'trackUnmuted', { trackId: event.track.id });
      };
      
      event.track.onended = () => {
        console.log(`Track ${event.track.id} ended from ${peerId}`);
        this.logEvent(peerId, 'trackEnded', { trackId: event.track.id });
      };
    };
    
    // Monitor data channel events
    peerConnection.ondatachannel = (event) => {
      console.log(`Data channel received from ${peerId}:`, event.channel);
      this.logEvent(peerId, 'dataChannelReceived', { label: event.channel.label });
    };
    
    // Monitor negotiation needed events
    peerConnection.onnegotiationneeded = () => {
      console.log(`Negotiation needed for ${peerId}`);
      this.logEvent(peerId, 'negotiationNeeded', {});
    };
    
    // Store ICE servers configuration
    this.stats.iceServers = peerConnection.getConfiguration().iceServers;
  }

  /**
   * Log an event for a peer connection
   * @param {string} peerId - The peer ID
   * @param {string} eventName - The name of the event
   * @param {object} data - Additional data for the event
   */
  logEvent(peerId, eventName, data) {
    if (!this.stats.connections[peerId]) {
      this.stats.connections[peerId] = { events: [] };
    }
    
    this.stats.connections[peerId].events.push({
      timestamp: new Date().toISOString(),
      event: eventName,
      data
    });
  }

  /**
   * Log an error
   * @param {string} context - The context in which the error occurred
   * @param {Error} error - The error object
   */
  logError(context, error) {
    console.error(`[WebRTC Diagnostic] ${context}:`, error);
    
    // Add to global errors list
    if (!this.stats.errors) {
      this.stats.errors = [];
    }
    
    this.stats.errors.push({
      timestamp: new Date().toISOString(),
      context,
      message: error.message,
      stack: error.stack
    });
  }

  /**
   * Get the current diagnostic report
   * @returns {object} The diagnostic report
   */
  getReport() {
    return {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      stats: this.stats
    };
  }

  /**
   * Print the diagnostic report to the console
   */
  printReport() {
    console.log('WebRTC Diagnostic Report:', this.getReport());
  }
}

// Create a singleton instance
const webrtcDiagnostic = new WebRTCDiagnostic().init();

export default webrtcDiagnostic;
