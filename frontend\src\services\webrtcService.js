import io from 'socket.io-client';
import webrtcDiagnostic from '../utils/webrtcDiagnostic';

/**
 * WebRTC Service for handling peer-to-peer video calls
 * This service manages WebRTC connections, signaling, and media streams
 */
class WebRTCService {
  constructor() {
    this.socket = null;
    this.localStream = null;
    this.peers = new Map(); // userId -> { peerConnection, stream }
    this.roomId = null;
    this.userId = null;
    this.userName = null;
    this.onUserConnectedCallback = null;
    this.onUserDisconnectedCallback = null;
    this.onStreamAddedCallback = null;

    // Determine the backend URL based on the current environment
    const currentUrl = window.location.hostname;
    if (currentUrl.includes('localhost')) {
      this.apiUrl = 'http://localhost:8000';
    } else if (currentUrl.includes('onrender.com')) {
      // For deployed environment, use the production backend URL
      this.apiUrl = 'https://my-backend-dwmk.onrender.com';
    } else {
      // Fallback to environment variable or default
      this.apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:8000';
    }

    // Remove any trailing /api if present
    if (this.apiUrl.endsWith('/api')) {
      this.apiUrl = this.apiUrl.slice(0, -4);
    }

    console.log('WebRTC service using API URL:', this.apiUrl);
  }

  async init(userId, userName) {
    this.userId = userId;

    // Make sure we have a valid userName (not null, undefined, or empty string)
    if (!userName || userName === 'Anonymous' || userName.trim() === '') {
      // Try to get the user's display name from Firebase Auth
      try {
        const auth = window.firebase.auth();
        const currentUser = auth.currentUser;
        if (currentUser && currentUser.displayName) {
          this.userName = currentUser.displayName;
          console.log(`Using Firebase display name: ${this.userName}`);
        } else {
          // Try to get user info from the backend
          try {
            const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:8000';
            const apiUrl = baseUrl.endsWith('/api') ? baseUrl : baseUrl + '/api';
            const response = await fetch(`${apiUrl}/users/${userId}`);

            if (response.ok) {
              const userData = await response.json();
              if (userData && userData.firstName && userData.lastName) {
                this.userName = `${userData.firstName} ${userData.lastName}`.trim();
                console.log(`Using backend user data: ${this.userName}`);
              } else {
                // Fallback to email or uid if no name in backend
                this.userName = currentUser?.email?.split('@')[0] || `User-${userId.substring(0, 5)}`;
                console.log(`Using fallback name (no backend name): ${this.userName}`);
              }
            } else {
              // Fallback to email or uid if backend request fails
              this.userName = currentUser?.email?.split('@')[0] || `User-${userId.substring(0, 5)}`;
              console.log(`Using fallback name (backend request failed): ${this.userName}`);
            }
          } catch (backendError) {
            console.warn('Error getting user data from backend:', backendError);
            // Fallback to email or uid if backend request fails
            this.userName = currentUser?.email?.split('@')[0] || `User-${userId.substring(0, 5)}`;
            console.log(`Using fallback name (backend error): ${this.userName}`);
          }
        }
      } catch (error) {
        console.warn('Error getting user display name:', error);
        this.userName = `User-${userId.substring(0, 5)}`;
      }
    } else {
      this.userName = userName;
    }

    console.log(`Initialized WebRTC for user: ${this.userName} (${this.userId})`);

    // Connect to socket.io server with explicit path
    console.log(`Connecting to socket.io server at ${this.apiUrl}`);

    try {
      // Log the API URL we're connecting to
      console.log(`Connecting to Socket.io server at ${this.apiUrl} with direct URL construction`);

      // Construct the socket.io URL directly to avoid any path issues
      const socketUrl = this.apiUrl.endsWith('/') ? this.apiUrl : `${this.apiUrl}/`;
      console.log(`Constructed socket URL: ${socketUrl}`);

      // Try to detect if we're behind a proxy or firewall
      const isFirewalled = () => {
        return new Promise((resolve) => {
          const testImg = new Image();
          testImg.onload = () => resolve(false); // Not firewalled
          testImg.onerror = () => resolve(true); // Likely firewalled
          testImg.src = 'https://www.google.com/favicon.ico?_=' + Date.now();
          setTimeout(() => resolve(true), 2000); // Assume firewalled if it takes too long
        });
      };

      // Check firewall status
      const firewalled = await isFirewalled();
      console.log(`Firewall detection result: ${firewalled ? 'Possibly behind firewall' : 'No firewall detected'}`);

      // Create a more reliable socket.io connection
      this.socket = io(socketUrl, {
        path: '/socket.io',
        transports: firewalled ? ['polling'] : ['polling', 'websocket'], // Use only polling if behind firewall
        reconnection: true,
        reconnectionAttempts: 5, // Limit reconnection attempts
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        randomizationFactor: 0.5,
        timeout: 20000, // 20 seconds timeout
        autoConnect: true,
        forceNew: true,
        secure: this.apiUrl.startsWith('https'),
        rejectUnauthorized: false, // Allow self-signed certificates
        query: {
          userId: this.userId,
          t: Date.now() // Add timestamp to prevent caching
        }
      });

      // Add more detailed connection event handlers
      this.socket.on('connect', () => {
        console.log(`Socket connected successfully with ID: ${this.socket.id}`);
      });

      this.socket.on('disconnect', (reason) => {
        console.log(`Socket disconnected: ${reason}`);
      });

      this.socket.on('reconnect', (attemptNumber) => {
        console.log(`Socket reconnected after ${attemptNumber} attempts`);
      });

      this.socket.on('reconnect_attempt', (attemptNumber) => {
        console.log(`Socket reconnection attempt #${attemptNumber}`);
        // If we've tried websocket and it's failed, fall back to polling only
        if (attemptNumber > 2) {
          console.log('Switching to polling transport only after failed websocket attempts');
          this.socket.io.opts.transports = ['polling'];
        }
      });

      this.socket.on('reconnect_error', (error) => {
        console.error('Socket reconnection error:', error);
      });

      this.socket.on('reconnect_failed', () => {
        console.error('Socket reconnection failed after all attempts');
      });

      this.socket.on('error', (error) => {
        console.error('Socket error:', error);
      });

      console.log('Socket.io instance created');
    } catch (error) {
      console.error('Error creating socket.io instance:', error);
      throw error;
    }

    // Set up socket event listeners
    this.setupSocketListeners();

    return new Promise((resolve, reject) => {
      this.socket.on('connect', () => {
        console.log('Connected to signaling server');
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        console.error('Socket connection error:', error);
        reject(error);
      });
    });
  }

  setupSocketListeners() {
    // Handle new user joining the room
    this.socket.on('user-connected', async ({ userId, userName }) => {
      console.log(`User connected: ${userName} (${userId})`);

      if (userId !== this.userId) {
        await this.createPeerConnection(userId, userName, true);

        if (this.onUserConnectedCallback) {
          this.onUserConnectedCallback(userId, userName);
        }
      }
    });

    // Handle existing users in the room
    this.socket.on('existing-users', async (users) => {
      console.log('Existing users in room:', users);

      for (const user of users) {
        if (user.id !== this.userId) {
          await this.createPeerConnection(user.id, user.name, false);

          if (this.onUserConnectedCallback) {
            this.onUserConnectedCallback(user.id, user.name);
          }
        }
      }
    });

    // Handle user disconnecting
    this.socket.on('user-disconnected', (userId) => {
      console.log(`User disconnected: ${userId}`);

      this.closePeerConnection(userId);

      if (this.onUserDisconnectedCallback) {
        this.onUserDisconnectedCallback(userId);
      }
    });

    // Handle mute state changes
    this.socket.on('user-mute-state-changed', ({ userId, isMuted }) => {
      console.log(`User ${userId} ${isMuted ? 'muted' : 'unmuted'} their microphone`);

      // Update the peer's mute state
      const peer = this.peers.get(userId);
      if (peer) {
        peer.isMuted = isMuted;

        // Notify the UI about the mute state change
        if (this.onUserMuteStateChangedCallback) {
          this.onUserMuteStateChangedCallback(userId, isMuted);
        }
      }
    });

    // Handle video state changes
    this.socket.on('user-video-state-changed', ({ userId, isVideoOff }) => {
      console.log(`User ${userId} turned their video ${isVideoOff ? 'off' : 'on'}`);

      // Update the peer's video state
      const peer = this.peers.get(userId);
      if (peer) {
        peer.isVideoOff = isVideoOff;

        // Notify the UI about the video state change
        if (this.onUserVideoStateChangedCallback) {
          this.onUserVideoStateChangedCallback(userId, isVideoOff);
        }
      }
    });

    // Handle WebRTC signaling with improved error handling and logging
    this.socket.on('signal', async ({ userId, signal }) => {
      try {
        // Enhanced logging with more details
        if (signal.type) {
          console.log(`Received ${signal.type} signal from ${userId}`, { signalType: signal.type });
        } else if (signal.candidate) {
          console.log(`Received ICE candidate from ${userId}`, {
            candidateType: signal.candidate.type,
            candidateProtocol: signal.candidate.protocol,
            candidateAddress: signal.candidate.address
          });
        } else {
          console.log(`Received unknown signal type from ${userId}`, { signal });
        }

        // Check if we have a valid userId
        if (!userId) {
          console.error('Received signal with invalid userId');
          return;
        }

        let peer = this.peers.get(userId);

        // If we don't have a peer connection yet, create one
        if (!peer) {
          console.log(`No existing peer for ${userId}, creating new peer connection`);

          // Request user info from the server
          console.log(`Requesting user info for ${userId}`);
          this.socket.emit('get-user-info', { userId });

          // Wait for user info with a timeout
          const userInfo = await new Promise((resolve) => {
            const timeout = setTimeout(() => {
              console.warn(`Timeout waiting for user info for ${userId}`);
              resolve({ name: `User-${userId.substring(0, 5)}` });
            }, 3000);

            const userInfoHandler = (info) => {
              if (info.userId === userId) {
                clearTimeout(timeout);
                this.socket.off('user-info', userInfoHandler);
                resolve(info);
              }
            };

            this.socket.on('user-info', userInfoHandler);
          });

          const userName = userInfo.name || `User-${userId.substring(0, 5)}`;
          console.log(`Using name "${userName}" for user ${userId}`);

          try {
            // Create a new peer connection (not as initiator since we're receiving a signal)
            await this.createPeerConnection(userId, userName, false);
            peer = this.peers.get(userId);

            if (!peer) {
              throw new Error(`Failed to create peer connection for ${userId}`);
            }
          } catch (peerCreationError) {
            console.error(`Error creating peer connection for ${userId}:`, peerCreationError);
            return;
          }
        }

        // Handle different signal types with better error handling
        if (signal.type === 'offer') {
          console.log(`Processing offer from ${userId}`);

          try {
            // Set remote description from the offer
            await peer.peerConnection.setRemoteDescription(new RTCSessionDescription(signal));
            console.log(`Remote description set for ${userId}`);

            // Create an answer
            console.log(`Creating answer for ${userId}`);
            const answer = await peer.peerConnection.createAnswer({
              offerToReceiveAudio: true,
              offerToReceiveVideo: true
            });

            // Set local description from the answer
            console.log(`Setting local description (answer) for ${userId}`);
            await peer.peerConnection.setLocalDescription(answer);

            // Send the answer back
            console.log(`Sending answer to ${userId}`);
            this.socket.emit('signal', {
              userId: this.userId,
              targetId: userId,
              signal: answer
            });
          } catch (offerError) {
            console.error(`Error processing offer from ${userId}:`, offerError);
            // Try to recover by recreating the peer connection
            this.closePeerConnection(userId);
            await this.createPeerConnection(userId, peer.userName, false);
          }
        } else if (signal.type === 'answer') {
          console.log(`Processing answer from ${userId}`);

          try {
            // Set remote description from the answer
            await peer.peerConnection.setRemoteDescription(new RTCSessionDescription(signal));
            console.log(`Remote description set for ${userId}`);
          } catch (answerError) {
            console.error(`Error processing answer from ${userId}:`, answerError);

            // Check if the peer connection is in a valid state
            if (peer.peerConnection.signalingState !== 'stable') {
              console.log(`Peer connection for ${userId} is in ${peer.peerConnection.signalingState} state, attempting to reset`);
              this.closePeerConnection(userId);
              await this.createPeerConnection(userId, peer.userName, true);
            }
          }
        } else if (signal.candidate) {
          console.log(`Adding ICE candidate from ${userId}`);

          try {
            // Add the ICE candidate
            await peer.peerConnection.addIceCandidate(new RTCIceCandidate(signal));
            console.log(`ICE candidate added for ${userId}`);
          } catch (iceCandidateError) {
            // This can happen if the ICE candidate is received before the offer
            console.warn(`Failed to add ICE candidate for ${userId}:`, iceCandidateError);

            // If the connection is not established yet, queue the candidate
            if (peer.peerConnection.remoteDescription === null) {
              console.log(`Peer connection not ready, will retry adding ICE candidate later`);

              // Try again after a short delay with exponential backoff
              let retryCount = 0;
              const maxRetries = 3;
              const retryAddIceCandidate = async () => {
                if (retryCount >= maxRetries) {
                  console.error(`Failed to add ICE candidate after ${maxRetries} retries`);
                  return;
                }

                try {
                  if (peer.peerConnection.remoteDescription !== null) {
                    await peer.peerConnection.addIceCandidate(new RTCIceCandidate(signal));
                    console.log(`ICE candidate added for ${userId} (retry #${retryCount + 1})`);
                  } else {
                    retryCount++;
                    const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
                    console.log(`Remote description still null, retrying in ${delay}ms (retry #${retryCount})`);
                    setTimeout(retryAddIceCandidate, delay);
                  }
                } catch (retryError) {
                  console.error(`Failed to add ICE candidate on retry #${retryCount + 1}:`, retryError);
                  retryCount++;
                  if (retryCount < maxRetries) {
                    const delay = Math.pow(2, retryCount) * 1000;
                    setTimeout(retryAddIceCandidate, delay);
                  }
                }
              };

              setTimeout(retryAddIceCandidate, 1000);
            }
          }
        }
      } catch (error) {
        console.error('Error handling signal:', error);
      }
    });
  }

  async joinRoom(roomId, localStream) {
    try {
      this.roomId = roomId;

      // Validate local stream
      if (!localStream || !localStream.active) {
        console.error('Invalid or inactive local stream provided to joinRoom');
        throw new Error('Local media stream is not available or active');
      }

      this.localStream = localStream;

      console.log(`Joining room ${roomId} as ${this.userName} (${this.userId})`);
      console.log('Socket connected:', this.socket.connected);

      if (!this.socket.connected) {
        console.log('Socket not connected, attempting to reconnect...');

        // Log the current socket state
        console.log('Current socket state:', {
          connected: this.socket.connected,
          disconnected: this.socket.disconnected,
          id: this.socket.id,
          auth: this.socket.auth,
          io: !!this.socket.io
        });

        // Force disconnect and reconnect
        if (this.socket.connected === false && this.socket.disconnected === true) {
          console.log('Socket in disconnected state, forcing reconnection');
          this.socket.disconnect();

          // Wait a bit before reconnecting
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Connect with logging
        console.log('Calling socket.connect()');
        this.socket.connect();
        console.log('After socket.connect() call');

        // Wait for connection with better timeout handling and longer timeout
        await new Promise((resolve, reject) => {
          // Increase timeout to 15 seconds
          const timeout = setTimeout(() => {
            console.error('Socket connection timeout after 15 seconds');

            // Try one more time with polling only before giving up
            console.log('Trying one more time with polling transport only');
            this.socket.io.opts.transports = ['polling'];
            this.socket.connect();

            // Give it another 10 seconds
            const finalTimeout = setTimeout(() => {
              console.error('Final socket connection timeout after additional 10 seconds');
              reject(new Error('Socket connection timeout - please check your network connection and try again'));
            }, 10000);

            this.socket.once('connect', () => {
              clearTimeout(finalTimeout);
              console.log('Socket reconnected successfully on final attempt');
              resolve();
            });

            this.socket.once('connect_error', (finalError) => {
              clearTimeout(finalTimeout);
              console.error('Final socket connection error:', finalError);
              reject(new Error(`Socket connection failed: ${finalError.message}`));
            });
          }, 15000);

          this.socket.once('connect', () => {
            clearTimeout(timeout);
            console.log('Socket reconnected successfully');
            resolve();
          });

          this.socket.once('connect_error', (error) => {
            console.error('Socket connection error:', error);
            // Don't reject yet, let the timeout handle it
          });
        });
      }

      // Verify socket is connected before proceeding
      if (!this.socket.connected) {
        throw new Error('Failed to establish socket connection');
      }

      // Join the room
      this.socket.emit('join-room', {
        roomId,
        userId: this.userId,
        userName: this.userName
      });

      console.log('Join room event emitted');

      // Return a promise that resolves when we receive confirmation from the server
      // or rejects after a timeout
      return new Promise((resolve, reject) => {
        const joinTimeout = setTimeout(() => {
          console.error('Timed out waiting for room join confirmation');
          reject(new Error('Room join timeout'));
        }, 10000);

        // Listen for existing-users event as confirmation of successful join
        const existingUsersHandler = (users) => {
          clearTimeout(joinTimeout);
          console.log(`Successfully joined room ${roomId} with ${users.length} existing users`);
          this.socket.off('existing-users', existingUsersHandler);
          resolve(users);
        };

        this.socket.once('existing-users', existingUsersHandler);
      });
    } catch (error) {
      console.error('Error joining room:', error);
      throw error;
    }
  }

  async createPeerConnection(userId, userName, isInitiator) {
    console.log(`Creating peer connection with ${userName} (${userId}), initiator: ${isInitiator}`);

    // Use more STUN/TURN servers for better connectivity
    const peerConnection = new RTCPeerConnection({
      iceServers: [
        // Google STUN servers
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
        { urls: 'stun:stun2.l.google.com:19302' },
        { urls: 'stun:stun3.l.google.com:19302' },
        { urls: 'stun:stun4.l.google.com:19302' },
        // Free TURN servers from Twilio (more reliable than openrelay)
        {
          urls: [
            "turn:global.turn.twilio.com:3478?transport=udp",
            "turn:global.turn.twilio.com:3478?transport=tcp",
            "turn:global.turn.twilio.com:443?transport=tcp"
          ],
          username: "f4b4035eaa76f4a55de5f4351567653ee4ff6fa97b50b6b334fcc1be9c27212d",
          credential: "myL7nn4oN5/jZIjHPPJH4/BWJDc="
        },
        // Backup TURN servers from openrelay
        {
          urls: 'turn:openrelay.metered.ca:80',
          username: 'openrelayproject',
          credential: 'openrelayproject'
        },
        {
          urls: 'turn:openrelay.metered.ca:443',
          username: 'openrelayproject',
          credential: 'openrelayproject'
        },
        {
          urls: 'turn:openrelay.metered.ca:443?transport=tcp',
          username: 'openrelayproject',
          credential: 'openrelayproject'
        }
      ],
      iceCandidatePoolSize: 10
    });

    // Log ICE connection state changes
    peerConnection.oniceconnectionstatechange = () => {
      console.log(`ICE connection state with ${userName}: ${peerConnection.iceConnectionState}`);
    };

    // Log signaling state changes
    peerConnection.onsignalingstatechange = () => {
      console.log(`Signaling state with ${userName}: ${peerConnection.signalingState}`);
    };

    // Log connection state changes
    peerConnection.onconnectionstatechange = () => {
      console.log(`Connection state with ${userName}: ${peerConnection.connectionState}`);
    };

    // Add local tracks to the connection
    if (this.localStream) {
      console.log(`Adding ${this.localStream.getTracks().length} local tracks to peer connection`);

      this.localStream.getTracks().forEach(track => {
        console.log(`Adding track: ${track.kind} (${track.id}), enabled: ${track.enabled}`);
        peerConnection.addTrack(track, this.localStream);
      });
    } else {
      console.warn('No local stream available to add to peer connection');
    }

    // Handle ICE candidates
    peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        console.log(`Generated ICE candidate for ${userName}`);

        this.socket.emit('signal', {
          userId: this.userId,
          targetId: userId,
          signal: event.candidate
        });
      } else {
        console.log(`All ICE candidates gathered for ${userName}`);
      }
    };

    // Handle ICE gathering state changes
    peerConnection.onicegatheringstatechange = () => {
      console.log(`ICE gathering state with ${userName}: ${peerConnection.iceGatheringState}`);
    };

    // Handle remote stream with improved handling
    peerConnection.ontrack = (event) => {
      console.log(`Received remote track from ${userName}: ${event.track.kind} (${event.track.id})`);

      // Log more details about the track and stream
      console.log(`Track details:`, {
        kind: event.track.kind,
        enabled: event.track.enabled,
        readyState: event.track.readyState,
        muted: event.track.muted,
        streamId: event.streams[0]?.id || 'no stream'
      });

      // Add track-specific event handlers
      event.track.onmute = () => {
        console.log(`Track ${event.track.kind} muted from ${userName}`);

        // If this is a video track, notify UI about video state change
        if (event.track.kind === 'video' && this.onUserVideoStateChangedCallback) {
          this.onUserVideoStateChangedCallback(userId, true);
        }

        // If this is an audio track, notify UI about mute state change
        if (event.track.kind === 'audio' && this.onUserMuteStateChangedCallback) {
          this.onUserMuteStateChangedCallback(userId, true);
        }
      };

      event.track.onunmute = () => {
        console.log(`Track ${event.track.kind} unmuted from ${userName}`);

        // If this is a video track, notify UI about video state change
        if (event.track.kind === 'video' && this.onUserVideoStateChangedCallback) {
          this.onUserVideoStateChangedCallback(userId, false);
        }

        // If this is an audio track, notify UI about mute state change
        if (event.track.kind === 'audio' && this.onUserMuteStateChangedCallback) {
          this.onUserMuteStateChangedCallback(userId, false);
        }
      };

      event.track.onended = () => {
        console.log(`Track ${event.track.kind} ended from ${userName}`);

        // If this is a video track, notify UI about video state change
        if (event.track.kind === 'video' && this.onUserVideoStateChangedCallback) {
          this.onUserVideoStateChangedCallback(userId, true);
        }
      };

      // Make sure we have a valid stream
      if (event.streams && event.streams[0]) {
        const stream = event.streams[0];

        // Log stream tracks for debugging
        const videoTracks = stream.getVideoTracks();
        const audioTracks = stream.getAudioTracks();

        console.log(`Stream has ${videoTracks.length} video tracks and ${audioTracks.length} audio tracks`);

        // Create a dedicated handler for this stream to handle track changes
        stream.onaddtrack = (trackEvent) => {
          console.log(`New track added to stream: ${trackEvent.track.kind}`);

          // If a new track is added, we should notify the UI again
          if (this.onStreamAddedCallback) {
            console.log(`Calling onStreamAddedCallback for ${userName} with updated stream`);
            this.onStreamAddedCallback(userId, userName, stream);
          }
        };

        stream.onremovetrack = (trackEvent) => {
          console.log(`Track removed from stream: ${trackEvent.track.kind}`);
        };

        // Store the stream with the peer
        const peer = this.peers.get(userId);
        if (peer) {
          peer.stream = stream;

          // Store track states
          peer.hasVideo = videoTracks.length > 0;
          peer.hasAudio = audioTracks.length > 0;
          peer.isVideoEnabled = videoTracks.length > 0 && videoTracks[0].enabled;
          peer.isAudioEnabled = audioTracks.length > 0 && audioTracks[0].enabled;
        }

        // Always notify about the stream, even if it doesn't have video tracks yet
        // The UI will handle showing a placeholder if needed
        if (this.onStreamAddedCallback) {
          console.log(`Calling onStreamAddedCallback for ${userName} with stream ID ${stream.id}`);
          this.onStreamAddedCallback(userId, userName, stream);

          // If this is a new user, make sure we have their correct name
          if (userName.startsWith('User-')) {
            console.log(`Requesting updated user info for ${userId}`);
            this.socket.emit('get-user-info', { userId });

            // Listen for user info response
            this.socket.once('user-info', (userInfo) => {
              if (userInfo.userId === userId && userInfo.name && !userInfo.name.startsWith('User-')) {
                console.log(`Received updated user info: ${userInfo.name} for ${userId}`);

                // Update the peer's user name
                const peer = this.peers.get(userId);
                if (peer) {
                  peer.userName = userInfo.name;
                }

                // Call the callback again with the updated name
                this.onStreamAddedCallback(userId, userInfo.name, stream);
              }
            });
          }
        } else {
          console.warn(`No onStreamAddedCallback registered for ${userName}`);
        }
      } else {
        console.warn(`Received track event without valid stream from ${userName}`);

        // Create an empty stream if needed
        const emptyStream = new MediaStream();

        // Add the track to the empty stream
        emptyStream.addTrack(event.track);

        console.log(`Created empty stream with track: ${event.track.kind}`);

        // Store the stream with the peer
        const peer = this.peers.get(userId);
        if (peer) {
          peer.stream = emptyStream;
        }

        // Notify about the stream
        if (this.onStreamAddedCallback) {
          console.log(`Calling onStreamAddedCallback for ${userName} with empty stream`);
          this.onStreamAddedCallback(userId, userName, emptyStream);
        }
      }

      // Log diagnostic information
      webrtcDiagnostic.logEvent(userId, 'trackReceived', {
        kind: event.track.kind,
        enabled: event.track.enabled,
        readyState: event.track.readyState,
        muted: event.track.muted,
        streamId: event.streams[0]?.id || 'no stream'
      });
    };

    // Store the peer connection
    this.peers.set(userId, {
      peerConnection,
      userName
    });

    // If we're the initiator, create and send an offer
    if (isInitiator) {
      try {
        console.log(`Creating offer for ${userName}`);

        const offer = await peerConnection.createOffer({
          offerToReceiveAudio: true,
          offerToReceiveVideo: true
        });

        console.log(`Setting local description (offer) for ${userName}`);
        await peerConnection.setLocalDescription(offer);

        console.log(`Sending offer to ${userName}`);
        this.socket.emit('signal', {
          userId: this.userId,
          targetId: userId,
          signal: offer
        });
      } catch (error) {
        console.error(`Error creating offer for ${userName}:`, error);
      }
    }

    return peerConnection;
  }

  closePeerConnection(userId) {
    const peer = this.peers.get(userId);

    if (peer) {
      peer.peerConnection.close();
      this.peers.delete(userId);
    }
  }

  leaveRoom() {
    console.log(`Leaving room: ${this.roomId}`);

    // Explicitly notify server about leaving
    if (this.socket && this.socket.connected && this.roomId && this.userId) {
      console.log(`Emitting leave-room event for room ${this.roomId}`);
      this.socket.emit('leave-room', {
        roomId: this.roomId,
        userId: this.userId
      });
    }

    // Close all peer connections
    for (const [peerId, peer] of this.peers.entries()) {
      console.log(`Closing peer connection with ${peerId}`);
      peer.peerConnection.close();
    }

    this.peers.clear();

    // Stop local stream tracks
    if (this.localStream) {
      console.log('Stopping local media tracks');
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    // Disconnect socket
    if (this.socket) {
      console.log('Disconnecting socket');
      this.socket.disconnect();
    }

    this.roomId = null;
    console.log('Room left successfully');
  }

  setCallbacks(callbacks) {
    const {
      onUserConnected,
      onUserDisconnected,
      onStreamAdded,
      onUserMuteStateChanged,
      onUserVideoStateChanged
    } = callbacks;

    this.onUserConnectedCallback = onUserConnected;
    this.onUserDisconnectedCallback = onUserDisconnected;
    this.onStreamAddedCallback = onStreamAdded;
    this.onUserMuteStateChangedCallback = onUserMuteStateChanged;
    this.onUserVideoStateChangedCallback = onUserVideoStateChanged;
  }
}

export default WebRTCService;
