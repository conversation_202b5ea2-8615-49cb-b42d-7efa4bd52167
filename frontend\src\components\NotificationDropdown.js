import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';

const NotificationDropdown = ({
  notifications,
  isOpen,
  onClose,
  onJoinMeeting,
  buttonRef
}) => {
  const dropdownRef = useRef(null);
  const { t } = useTranslation();

  useEffect(() => {
    if (!isOpen) return;

    // Position the dropdown relative to the button
    const positionDropdown = () => {
      if (!buttonRef.current || !dropdownRef.current) return;

      const buttonRect = buttonRef.current.getBoundingClientRect();
      const dropdown = dropdownRef.current;

      // Position dropdown below the button
      dropdown.style.position = 'fixed';
      dropdown.style.top = `${buttonRect.bottom + 8}px`; // 8px gap
      dropdown.style.right = `${window.innerWidth - buttonRect.right}px`;
    };

    // <PERSON>le clicks outside the dropdown
    const handleClickOutside = (event) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target)
      ) {
        onClose();
      }
    };

    positionDropdown();
    window.addEventListener('resize', positionDropdown);
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      window.removeEventListener('resize', positionDropdown);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, buttonRef, onClose]);

  if (!isOpen) return null;

  return createPortal(
    <div
      ref={dropdownRef}
      className="w-80 theme-container rounded-md shadow-lg py-2 z-[9999] border max-h-[80vh] overflow-y-auto"
      style={{ boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)' }}
    >
      <div className="px-4 py-2 font-semibold border-b theme-text text-subtitle">{t('notifications')}</div>
      {notifications.length === 0 ? (
        <div className="px-4 py-4 theme-label text-sm text-body">{t('no_upcoming_events')}</div>
      ) : (
        notifications.map(notif => {
          const now = new Date();
          const start = new Date(notif.event.start);
          const diffMin = (start - now) / (60 * 1000);
          const canJoin = diffMin <= 15 && diffMin >= 0;
          return (
            <div key={notif.id} className="px-4 py-2 hover:opacity-80 cursor-pointer flex flex-col">
              <span className="theme-text text-body">{notif.message}</span>
              <span className="text-xs theme-label">{notif.time.toLocaleString()}</span>
              <button
                className={`text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 text-xs mt-1 self-start text-button ${!canJoin ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={() => {
                  if (!canJoin) {
                    onClose();
                    alert(t('meeting_join_time_error', 'You can only join the meeting 15 minutes before it starts.'));
                    return;
                  }
                  onClose();
                  onJoinMeeting(notif.event.resource?.roomId || notif.event.roomId);
                }}
                disabled={!canJoin}
              >
                {t('join_meeting')}
              </button>
            </div>
          );
        })
      )}
    </div>,
    document.body
  );
};

export default NotificationDropdown;
