/* Meeting Controls Styles */

/* Base styles for meeting controls */
.meeting-controls {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  background-color: rgba(30, 41, 59, 0.8);
  border-radius: 12px;
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  margin: 16px 0;
}

/* Light mode styles */
body.light-mode {
  background-color: #f8f9fa;
  color: #212529;
}

body.light-mode .bg-gray-900 {
  background-color: #ffffff;
}

body.light-mode .bg-gray-800 {
  background-color: #f1f3f5;
}

body.light-mode .text-white {
  color: #212529;
}

body.light-mode .text-gray-300,
body.light-mode .text-gray-400 {
  color: #495057;
}

body.light-mode .bg-gray-700 {
  background-color: #e9ecef;
}

body.light-mode button.bg-gray-700 {
  color: #212529;
}

body.light-mode button.bg-gray-700 svg {
  color: #495057;
}

body.light-mode .border-gray-700 {
  border-color: #dee2e6;
}

/* Layout styles */
.layout-grid-1x1 .video-grid {
  grid-template-columns: 1fr 1fr;
}

.layout-grid-2x2 .video-grid {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.layout-spotlight .video-grid {
  grid-template-columns: 2fr 1fr;
  grid-template-rows: auto;
}

.layout-spotlight .video-grid > div:first-child {
  grid-row: span 2;
}

/* Animation for recording button */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Responsive styles */
@media (max-width: 640px) {
  .meeting-controls {
    flex-direction: column;
    align-items: center;
  }
}
