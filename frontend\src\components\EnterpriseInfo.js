import React, { useState, useEffect } from 'react';
import { Briefcase, Users, Phone, MapPin, Mail, Building } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { enterpriseAPI, enterpriseUsersAPI } from '../services/mongodb';

const EnterpriseInfo = ({ enterpriseCode, isAdmin = false, enterprise = null }) => {
  const { t } = useTranslation();
  const [enterpriseData, setEnterpriseData] = useState(enterprise);
  const [enterpriseUsers, setEnterpriseUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchEnterpriseData = async () => {
      try {
        setLoading(true);
        setError(null);

        // If enterprise data is already provided, use it
        if (enterprise) {
          console.log('Using provided enterprise data:', enterprise);
          setEnterpriseData(enterprise);
        }
        // Otherwise fetch it using the enterprise code
        else if (enterpriseCode) {
          console.log('Fetching enterprise data for code:', enterpriseCode);
          const data = await enterpriseAPI.getEnterpriseByCode(enterpriseCode);
          console.log('Fetched enterprise data:', data);
          setEnterpriseData(data);
        } else {
          throw new Error('No enterprise code provided');
        }

        // Fetch enterprise users
        if (enterpriseCode || (enterprise && enterprise.code)) {
          const code = enterpriseCode || enterprise.code;
          const users = await enterpriseUsersAPI.getUsers(code);
          setEnterpriseUsers(users || []);
        }
      } catch (err) {
        console.error('Error fetching enterprise data:', err);
        setError(err.message || 'Failed to load enterprise information');
      } finally {
        setLoading(false);
      }
    };

    fetchEnterpriseData();
  }, [enterprise, enterpriseCode]);

  if (loading) {
    return (
      <div className="modern-card p-6 animate-pulse">
        <div className="h-8 bg-slate-700 rounded-full w-1/3 mb-6"></div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="h-24 bg-slate-700 rounded-xl"></div>
          <div className="h-24 bg-slate-700 rounded-xl"></div>
          <div className="h-24 bg-slate-700 rounded-xl"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="modern-card p-4 border border-red-800 bg-opacity-20 bg-red-900">
        <p className="text-red-300 text-sm">{error}</p>
      </div>
    );
  }

  if (!enterpriseData) {
    return (
      <div className="modern-card p-4">
        <p className="text-gray-400">{t('no_enterprise_information')}</p>
      </div>
    );
  }

  return (
    <div className="modern-card p-6 bg-gradient-to-br from-slate-800 to-slate-900 border border-slate-700 rounded-xl shadow-xl">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-blue-300 flex items-center">
          <Briefcase className="h-6 w-6 mr-3 text-blue-400" />
          {t('enterprise_information')}
        </h2>
        <div className="bg-blue-500 bg-opacity-20 px-3 py-1 rounded-full border border-blue-400">
          <span className="text-blue-300 text-sm font-medium">{enterpriseData.code}</span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Enterprise Name Card */}
        <div className="bg-slate-800 rounded-xl p-4 border border-slate-700 hover:border-blue-500 transition-all duration-300 shadow-md hover:shadow-blue-900/20">
          <div className="flex items-center mb-3">
            <div className="bg-gradient-to-br from-blue-500 to-blue-700 p-2.5 rounded-lg mr-3">
              <Building className="h-5 w-5 text-white" />
            </div>
            <h3 className="text-sm font-medium text-blue-300">{t('enterprise_name')}</h3>
          </div>
          <p className="text-gray-200 text-lg font-medium pl-2">{enterpriseData.name}</p>
        </div>

        {/* Email Card - Use enterprise email if available, otherwise construct one from name */}
        <div className="bg-slate-800 rounded-xl p-4 border border-slate-700 hover:border-blue-500 transition-all duration-300 shadow-md hover:shadow-blue-900/20">
          <div className="flex items-center mb-3">
            <div className="bg-gradient-to-br from-blue-500 to-blue-700 p-2.5 rounded-lg mr-3">
              <Mail className="h-5 w-5 text-white" />
            </div>
            <h3 className="text-sm font-medium text-blue-300">{t('email')}</h3>
          </div>
          <p className="text-gray-200 text-lg font-medium pl-2">
            {(() => {
              // Log the enterprise data for debugging
              console.log('Enterprise email data:', {
                email: enterpriseData.email,
                adminEmail: enterpriseData.adminEmail,
                name: enterpriseData.name
              });

              // Use the enterprise email if available
              if (enterpriseData.email && enterpriseData.email !== 'undefined' &&
                  !enterpriseData.email.includes('undefined')) {
                return enterpriseData.email;
              }

              // Generate a default email based on the enterprise name
              return `contact@${enterpriseData.name.toLowerCase().replace(/\s+/g, '-')}.com`;
            })()}
          </p>
        </div>

        {/* Members Card */}
        <div className="bg-slate-800 rounded-xl p-4 border border-slate-700 hover:border-blue-500 transition-all duration-300 shadow-md hover:shadow-blue-900/20">
          <div className="flex items-center mb-3">
            <div className="bg-gradient-to-br from-blue-500 to-blue-700 p-2.5 rounded-lg mr-3">
              <Users className="h-5 w-5 text-white" />
            </div>
            <h3 className="text-sm font-medium text-blue-300">{t('members')}</h3>
          </div>
          <p className="text-gray-200 text-lg font-medium pl-2">
            <span className="text-blue-400 font-bold">{enterpriseUsers.length}</span> {t('active_members')}
          </p>
        </div>

        {/* Address Card - Only show if available */}
        {enterpriseData.address && (
          <div className="bg-slate-800 rounded-xl p-4 border border-slate-700 hover:border-blue-500 transition-all duration-300 shadow-md hover:shadow-blue-900/20">
            <div className="flex items-center mb-3">
              <div className="bg-gradient-to-br from-blue-500 to-blue-700 p-2.5 rounded-lg mr-3">
                <MapPin className="h-5 w-5 text-white" />
              </div>
              <h3 className="text-sm font-medium text-blue-300">{t('address')}</h3>
            </div>
            <p className="text-gray-200 text-lg font-medium pl-2">{enterpriseData.address}</p>
          </div>
        )}

        {/* Phone Card - Only show if available */}
        {enterpriseData.phone && (
          <div className="bg-slate-800 rounded-xl p-4 border border-slate-700 hover:border-blue-500 transition-all duration-300 shadow-md hover:shadow-blue-900/20">
            <div className="flex items-center mb-3">
              <div className="bg-gradient-to-br from-blue-500 to-blue-700 p-2.5 rounded-lg mr-3">
                <Phone className="h-5 w-5 text-white" />
              </div>
              <h3 className="text-sm font-medium text-blue-300">{t('phone')}</h3>
            </div>
            <p className="text-gray-200 text-lg font-medium pl-2">{enterpriseData.phone}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnterpriseInfo;
