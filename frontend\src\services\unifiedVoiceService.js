/**
 * Service Vocal Unifié pour la Plateforme Neeting
 * Combine la reconnaissance vocale, l'analyse de commandes et l'orchestration
 * Supporte les commandes multilingues (Français, Anglais, Arabe)
 */
import AzureService from './Azure-module';
import axios from 'axios';

class UnifiedVoiceService {
  constructor() {
    // === Configuration Audio et Reconnaissance ===
    this.speechRecognition = new AzureService();
    this.mediaRecorder = null;
    this.audioChunks = [];
    this.isListening = false;
    this.isRecording = false;
    this.transcriptParts = [];

    // === État et Gestion ===
    this.isLoaded = false;
    this.loadingPromise = null;
    this.loadingCallback = null;
    this.initializationStatus = 'idle'; // 'idle', 'initializing', 'ready', 'failed'
    this.connectionStatus = 'disconnected';
    this.statusCallback = null;

    // === Gestion des Sessions ===
    this.lastStartTime = null;
    this.silenceTimeout = null;
    this.maxDuration = 7200000; // 2 heures en millisecondes
    this.recordingTimeout = null;
    this.hasReceivedSpeech = false;
    this.currentCommand = null;

    // === Reconnexion et Réseau ===
    this.reconnectionAttempts = 0;
    this.maxReconnectionAttempts = 3;
    this.isOffline = false;

    // === Callbacks ===
    this.onRecognizedCallback = null;
    this.cleanup = this.cleanup.bind(this);

    // === Configuration IA et Commandes ===
    // Motifs d'intention pour l'interface d'accueil avec support multilingue
    this.intentPatterns = {
      // Gestion des réunions (création et planification uniquement)
      createMeeting: [
        'créer', 'organiser', 'planifier', 'programmer', 'meeting', 'réunion',
        'schedule', 'nouvelle', 'nouveau', 'planifier'
      ],

      // Navigation vers différentes sections
      goToCalendar: [
        'aller', 'naviguer', 'go', 'ouvrir', 'voir', 'calendrier', 'calendar',
        'agenda', 'planning'
      ],

      goToProfile: [
        'aller', 'naviguer', 'go', 'ouvrir', 'voir', 'profil', 'profile',
        'compte', 'account', 'mon', 'my'
      ],

      goToEnterprise: [
        'aller', 'naviguer', 'go', 'ouvrir', 'voir', 'entreprise', 'enterprise',
        'société', 'company', 'organisation', 'gestion', 'manage', 'admin'
      ],

      goToDashboard: [
        'aller', 'naviguer', 'go', 'ouvrir', 'voir', 'dashboard', 'accueil',
        'home', 'tableau', 'bord'
      ],

      // Gestion des fichiers et documents (visualisation uniquement)
      viewFiles: [
        'afficher', 'montrer', 'voir', 'show', 'display', 'view', 'find',
        'search', 'open', 'get', 'minutes', 'notes', 'documents', 'files',
        'recordings', 'PV', 'procès-verbal', 'transcription', 'résumé', 'fichiers'
      ],

      // Contrôles de l'assistant vocal
      help: [
        'aide', 'help', 'assistance', 'comment', 'how', 'what', 'que',
        'quoi', 'expliquer', 'explain', 'commandes', 'commands'
      ],

      // Fonctionnalité de recherche
      search: [
        'chercher', 'rechercher', 'search', 'find', 'trouver', 'lookup'
      ],

      // Téléchargement de fichiers
      downloadFile: [
        'télécharger', 'download', 'get', 'récupérer', 'obtenir', 'grab', 'fetch',
        'transcription', 'transcript', 'transcrit', 'transcripts',
        'summary', 'résumé', 'compte-rendu', 'pv', 'minutes', 'sommaire',
        'recording', 'enregistrement', 'audio', 'video', 'record',
        'fichier', 'file', 'document', 'doc',
        'hier', 'yesterday', 'today', 'aujourd\'hui', 'demain', 'tomorrow',
        'last', 'dernier', 'dernière', 'latest', 'recent', 'récent',
        'all', 'tous', 'toutes', 'everything', 'tout'
      ]
    };

    // Support linguistique
    this.supportedLanguages = ['fr', 'en', 'ar'];
    this.currentLanguage = 'fr'; // Par défaut en français

    // Seuil de confiance pour la reconnaissance de commandes
    this.confidenceThreshold = 0.3;

    // Configuration Cohere
    this.COHERE_API_KEY = process.env.REACT_APP_COHERE_API_KEY || '96LscT5wcN63AxsQwfE4zbgwSTn4BjPSU3s7SY24';
  }

  // === MÉTHODES D'INITIALISATION ===

  setStatusCallback(callback) {
    this.statusCallback = callback;
    // Ajouter un écouteur au service de reconnaissance vocale
    this.speechRecognition.addStatusListener((status, error) => {
      this.connectionStatus = status;
      if (this.statusCallback) {
        this.statusCallback(status, error);
      }
    });
  }

  // Définir la langue actuelle pour une meilleure reconnaissance d'intention
  setLanguage(language) {
    if (this.supportedLanguages.includes(language)) {
      this.currentLanguage = language;
    }
  }

  async initWhisper(loadingCallback = null) {
    if (this.loadingPromise) {
      return this.loadingPromise;
    }

    this.loadingCallback = loadingCallback;
    this.initializationStatus = 'initializing';

    // Configuration du monitoring du statut réseau
    window.addEventListener('online', () => this.handleNetworkChange(true));
    window.addEventListener('offline', () => this.handleNetworkChange(false));

    this.loadingPromise = new Promise(async (resolve, reject) => {
      try {
        if (!navigator.onLine) {
          throw new Error('Aucune connexion internet disponible');
        }

        if (this.loadingCallback) {
          this.loadingCallback(0);
        }

        await this.speechRecognition.init();
        this.isLoaded = true;
        this.initializationStatus = 'ready';
        this.reconnectionAttempts = 0;

        if (this.loadingCallback) {
          this.loadingCallback(100);
        }

        resolve();
      } catch (error) {
        this.handleInitializationError(error);
        reject(error);
      }
    });

    return this.loadingPromise;
  }

  async handleNetworkChange(isOnline) {
    if (isOnline && this.isOffline) {
      this.isOffline = false;
      console.log('Connexion réseau restaurée, tentative de reconnexion...');
      try {
        await this.speechRecognition.reconnect();
      } catch (error) {
        console.error('Échec de la reconnexion:', error);
      }
    } else if (!isOnline) {
      this.isOffline = true;
      console.log('Connexion réseau perdue');
    }
  }

  handleInitializationError(error) {
    this.initializationStatus = 'failed';
    this.loadingPromise = null;

    if (this.loadingCallback) {
      this.loadingCallback(-1);
    }

    // Fournir un retour d'erreur spécifique
    const errorMessage = error.message.includes('Unable to establish connection') ?
      'Impossible de se connecter au service vocal - Veuillez vérifier votre connexion réseau et réessayer' :
      `Échec de l\'initialisation: ${error.message}`;

    console.error(errorMessage);
    return errorMessage;
  }

  // === MÉTHODES D'ENREGISTREMENT ===

  async startRecording(onTranscript, onLiveTranscript) {
    if (this.isRecording) {
      console.log('Enregistrement déjà en cours');
      return;
    }

    try {
      await this.stopRecording();

      this.isRecording = true;
      this.hasReceivedSpeech = false;
      this.transcriptParts = [];
      this.lastStartTime = Date.now();

      // Définir uniquement la durée maximale d'enregistrement
      this.recordingTimeout = setTimeout(() => {
        console.log('Durée maximale d\'enregistrement atteinte');
        this.stopRecording();
      }, this.maxDuration);

      await this.speechRecognition.start((text) => {
        if (!text || !text.trim()) return;

        console.log('Transcription reçue:', text);
        const cleanText = text.trim();

        // Stocker la partie de transcription et marquer la parole comme reçue
        this.transcriptParts.push(cleanText);
        this.hasReceivedSpeech = true;

        // Appeler le callback avec la nouvelle partie et la transcription complète
        if (onTranscript) {
          const fullTranscript = this.processTranscripts();
          onTranscript(cleanText, fullTranscript);
        }
        // Callback de transcription en direct pour chaque mise à jour
        if (onLiveTranscript) {
          onLiveTranscript(cleanText);
        }
      });

    } catch (error) {
      console.error('Erreur dans startRecording:', error);
      this.cleanup();
      throw error;
    }
  }

  cleanupResources() {
    if (this.recordingTimeout) {
      clearTimeout(this.recordingTimeout);
      this.recordingTimeout = null;
    }
    this.stopListening();
    this.isRecording = false;
    this.lastStartTime = null;
    this.hasReceivedSpeech = false;
    this.isListening = false;
  }

  async stopRecording() {
    if (!this.isRecording) return '';

    this.cleanupResources();

    // Attendre plus longtemps pour le traitement final
    await new Promise(resolve => setTimeout(resolve, 1500));

    const fullTranscript = this.processTranscripts();
    console.log('Transcription finale:', fullTranscript);

    if (fullTranscript && fullTranscript.trim()) {
      try {
        // Sauvegarder d'abord la transcription complète
        const transcriptFile = {
          name: `Transcription de Réunion - ${new Date().toLocaleString()}`,
          content: fullTranscript,
          type: 'transcript'
        };

        // Obtenir un résumé en utilisant Cohere
        const summary = await this.summarizeWithCohere(fullTranscript);

        // Sauvegarder le résumé comme fichier séparé
        const summaryFile = {
          name: `Résumé de Réunion - ${new Date().toLocaleString()}`,
          content: summary,
          type: 'summary'
        };

        this.transcriptParts = [];
        return {
          transcript: transcriptFile,
          summary: summaryFile
        };
      } catch (error) {
        console.error('Erreur lors du traitement de l\'enregistrement:', error);
        throw error;
      }
    }

    return '';
  }

  async cleanup() {
    this.cleanupResources();
    const fullTranscript = this.processTranscripts();
    this.transcriptParts = [];
    this.currentCommand = null;
    return fullTranscript;
  }

  processTranscripts() {
    try {
      const validParts = this.transcriptParts
        .filter(part => part && typeof part === 'string' && part.trim().length > 0)
        .map(part => part.trim());

      // Retour anticipé si aucune partie valide
      if (validParts.length === 0) {
        return '';
      }

      // Supprimer les doublons et joindre
      return [...new Set(validParts)].join(' ').trim();
    } catch (error) {
      console.error('Erreur lors du traitement de la transcription:', error);
      return '';
    }
  }

  // === MÉTHODES D'ÉCOUTE ===

  async startListening(onRecognized, onLiveTranscript) {
    if (this.isListening) {
      // Si déjà en écoute mais cela fait plus de 30 secondes, redémarrer
      const timeSinceStart = Date.now() - (this.lastStartTime || 0);
      if (timeSinceStart < 30000) {
        return;
      }
      // Forcer l'arrêt si cela fait trop longtemps
      this.stopListening();
    }

    try {
      // Attendre le nettoyage
      await new Promise(resolve => setTimeout(resolve, 250));

      this.onRecognizedCallback = onRecognized;
      this.lastStartTime = Date.now();

      await this.speechRecognition.start((text) => {
        if (text) {
          const cleanText = text.trim();
          if (cleanText) {
            // Analyser la commande avec l'IA intégrée
            const command = this.parseCommand(cleanText);

            if (this.onRecognizedCallback) {
              this.onRecognizedCallback(cleanText, command);
            }
            // Callback de transcription en direct pour chaque mise à jour
            if (onLiveTranscript) {
              onLiveTranscript(cleanText);
            }
          }
        }
      });

      this.isListening = true;
    } catch (error) {
      this.isListening = false;
      this.onRecognizedCallback = null;
      throw error;
    }
  }

  stopListening() {
    if (this.isListening) {
      this.speechRecognition.stop();
      this.isListening = false;
      this.onRecognizedCallback = null;
      this.lastStartTime = null;
    }
  }

  // === MÉTHODES D'ANALYSE DE COMMANDES ===

  parseCommand(text) {
    if (!text || typeof text !== 'string') {
      return {
        action: 'unknown',
        confidence: 0,
        transcript: text || '',
        error: 'Texte d\'entrée invalide'
      };
    }

    const lowercaseText = text.toLowerCase().trim();
    const words = lowercaseText.split(/\s+/).filter(word => word.length > 0);

    if (words.length === 0) {
      return {
        action: 'unknown',
        confidence: 0,
        transcript: text,
        error: 'Commande vide'
      };
    }

    // Calculer les scores d'intention
    const scores = {};
    for (const [intent, patterns] of Object.entries(this.intentPatterns)) {
      scores[intent] = this.calculateIntentScore(words, patterns);
    }

    // Obtenir l'intention avec le score le plus élevé
    const [mainIntent, score] = this.getHighestScoringIntent(scores);

    console.log(`Service Vocal Unifié - Intention: ${mainIntent}, Score: ${score.toFixed(3)}`);

    // Si le score est trop bas, considérer comme inconnu
    if (score < this.confidenceThreshold) {
      return {
        action: 'unknown',
        confidence: score,
        transcript: text,
        suggestedActions: this.getSuggestedActions(words, this.currentLanguage)
      };
    }

    // Extraire des informations supplémentaires
    const date = this.extractDate(lowercaseText);
    const time = this.extractTime(lowercaseText);

    // Construire la commande basée sur l'intention
    switch (mainIntent) {
      case 'createMeeting':
        return {
          action: 'createMeeting',
          confidence: score,
          parameters: {
            ...this.extractMeetingParameters(text),
            date,
            time
          }
        };

      case 'goToCalendar':
        return {
          action: 'navigate',
          confidence: score,
          parameters: {
            destination: 'calendar',
            route: '/calendar'
          }
        };

      case 'goToProfile':
        return {
          action: 'navigate',
          confidence: score,
          parameters: {
            destination: 'profile',
            route: '/profile'
          }
        };

      case 'goToEnterprise':
        return {
          action: 'navigate',
          confidence: score,
          parameters: {
            destination: 'enterprise',
            route: '/enterprise'
          }
        };

      case 'goToDashboard':
        return {
          action: 'navigate',
          confidence: score,
          parameters: {
            destination: 'dashboard',
            route: '/dashboard'
          }
        };

      case 'viewFiles':
        return {
          action: 'viewFiles',
          confidence: score,
          date: date,
          listAll: !date,
          parameters: {
            searchTerms: this.extractSearchTerms(text),
            fileType: this.extractFileType(text)
          }
        };

      case 'search':
        return {
          action: 'search',
          confidence: score,
          parameters: {
            query: this.extractSearchQuery(text),
            searchTerms: this.extractSearchTerms(text)
          }
        };

      case 'help':
        return {
          action: 'help',
          confidence: score,
          parameters: {
            topic: this.extractHelpTopic(text)
          }
        };

      case 'downloadFile':
        return {
          action: 'downloadFile',
          confidence: score,
          parameters: {
            date: date,
            fileType: this.extractFileType(text),
            searchTerms: this.extractSearchTerms(text)
          }
        };

      default:
        return {
          action: 'unknown',
          confidence: score,
          transcript: text,
          detectedIntent: mainIntent
        };
    }
  }

  calculateIntentScore(words, patterns) {
    let matches = 0;
    let totalWeight = 0;

    for (const word of words) {
      for (const pattern of patterns) {
        if (word.includes(pattern) || pattern.includes(word)) {
          matches++;
          totalWeight += word.length === pattern.length ? 2 : 1; // Correspondance exacte obtient un poids plus élevé
          break; // Ne pas compter le même mot plusieurs fois
        }
      }
    }

    // Algorithme de notation amélioré
    const baseScore = matches / Math.max(words.length, patterns.length);
    const weightedScore = totalWeight / (words.length * 2);

    return (baseScore + weightedScore) / 2;
  }

  getHighestScoringIntent(scores) {
    return Object.entries(scores).reduce(
      (max, [intent, score]) => score > max[1] ? [intent, score] : max,
      ['unknown', 0]
    );
  }

  getSuggestedActions(words, interfaceLanguage = 'fr') {
    const suggestions = [];

    // Utiliser la langue de l'interface au lieu de détecter la langue des mots
    let targetLanguage = interfaceLanguage;
    if (targetLanguage.startsWith('en')) {
      targetLanguage = 'en';
    } else if (targetLanguage.startsWith('ar')) {
      targetLanguage = 'ar';
    } else {
      targetLanguage = 'fr'; // Français par défaut
    }

    // Suggestions multilingues basées sur la langue de l'interface
    const commonActions = {
      fr: [
        'créer une réunion',
        'aller au calendrier',
        'voir mon profil',
        'gérer l\'entreprise',
        'voir les fichiers',
        'télécharger transcription',
        'aide'
      ],
      en: [
        'create a meeting',
        'go to calendar',
        'view my profile',
        'manage enterprise',
        'view files',
        'download transcript',
        'help'
      ],
      ar: [
        'إنشاء اجتماع',
        'الذهاب إلى التقويم',
        'عرض ملفي الشخصي',
        'إدارة المؤسسة',
        'عرض الملفات',
        'تحميل النسخة',
        'مساعدة'
      ]
    };

    const actionsForLanguage = commonActions[targetLanguage] || commonActions.fr;

    // Suggestion simple basée sur des correspondances partielles
    for (const action of actionsForLanguage) {
      if (words.some(word => action.toLowerCase().includes(word.toLowerCase()))) {
        suggestions.push(action);
      }
    }

    return suggestions.slice(0, 3); // Retourner les 3 meilleures suggestions
  }

  detectLanguage(words) {
    // Mots clés pour détecter la langue avec plus de variantes
    const languageKeywords = {
      en: [
        'create', 'meeting', 'calendar', 'profile', 'enterprise', 'files', 'download', 'transcript', 'help', 'go', 'view', 'manage',
        'the', 'last', 'yesterday', 'today', 'tomorrow', 'get', 'show', 'open', 'file', 'of', 'from', 'to', 'and', 'or',
        'schedule', 'join', 'start', 'stop', 'record', 'save', 'delete', 'update', 'edit', 'search', 'find', 'look'
      ],
      fr: [
        'créer', 'réunion', 'calendrier', 'profil', 'entreprise', 'fichiers', 'télécharger', 'transcription', 'aide', 'aller', 'voir', 'gérer',
        'le', 'la', 'les', 'du', 'de', 'des', 'hier', 'aujourd', 'demain', 'obtenir', 'afficher', 'ouvrir', 'fichier', 'et', 'ou',
        'planifier', 'rejoindre', 'démarrer', 'arrêter', 'enregistrer', 'sauvegarder', 'supprimer', 'modifier', 'chercher', 'trouver'
      ],
      ar: [
        'إنشاء', 'اجتماع', 'تقويم', 'ملف', 'مؤسسة', 'ملفات', 'تحميل', 'نسخة', 'مساعدة',
        'في', 'من', 'إلى', 'أمس', 'اليوم', 'غدا', 'الحصول', 'عرض', 'فتح', 'ملف', 'و', 'أو'
      ]
    };

    let scores = { fr: 0, en: 0, ar: 0 };
    const totalWords = words.length;

    // Analyser chaque mot
    for (const word of words) {
      const lowerWord = word.toLowerCase();

      for (const [lang, keywords] of Object.entries(languageKeywords)) {
        if (keywords.some(keyword => {
          // Correspondance exacte ou partielle
          return lowerWord === keyword.toLowerCase() ||
                 lowerWord.includes(keyword.toLowerCase()) ||
                 keyword.toLowerCase().includes(lowerWord);
        })) {
          scores[lang]++;
        }
      }
    }

    // Analyser les caractères pour détecter l'arabe
    const fullText = words.join(' ');
    const arabicChars = fullText.match(/[\u0600-\u06FF]/g);
    if (arabicChars && arabicChars.length > 0) {
      scores.ar += arabicChars.length / 2; // Bonus pour les caractères arabes
    }

    // Analyser les patterns anglais (articles, prépositions communes)
    const englishPatterns = ['the', 'of', 'to', 'and', 'a', 'in', 'is', 'it', 'you', 'that', 'he', 'was', 'for', 'on', 'are', 'as', 'with', 'his', 'they', 'i', 'at', 'be', 'this', 'have', 'from', 'or', 'one', 'had', 'by', 'word', 'but', 'not', 'what', 'all', 'were', 'we', 'when', 'your', 'can', 'said', 'there', 'each', 'which', 'she', 'do', 'how', 'their', 'if', 'will', 'up', 'other', 'about', 'out', 'many', 'then', 'them', 'these', 'so', 'some', 'her', 'would', 'make', 'like', 'into', 'him', 'has', 'two', 'more', 'very', 'after', 'words', 'first', 'where', 'much', 'through', 'back', 'years', 'work', 'came', 'right', 'used', 'take', 'three', 'states', 'himself', 'few', 'house', 'use', 'during', 'without', 'again', 'place', 'around', 'however', 'small', 'found', 'mrs', 'thought', 'went', 'say', 'part', 'once', 'general', 'high', 'upon', 'school', 'every', 'don', 'does', 'got', 'united', 'left', 'number', 'course', 'war', 'until', 'always', 'away', 'something', 'fact', 'though', 'water', 'less', 'public', 'put', 'think', 'almost', 'hand', 'enough', 'far', 'took', 'head', 'yet', 'government', 'system', 'better', 'set', 'told', 'nothing', 'night', 'end', 'why', 'called', 'didn', 'eyes', 'find', 'going', 'look', 'asked', 'later', 'knew', 'let', 'great', 'year', 'come', 'since', 'against', 'go', 'came', 'right', 'used', 'take'];

    for (const word of words) {
      if (englishPatterns.includes(word.toLowerCase())) {
        scores.en += 0.5; // Bonus modéré pour les mots anglais communs
      }
    }

    // Analyser les patterns français (articles, prépositions communes)
    const frenchPatterns = ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour', 'dans', 'ce', 'son', 'une', 'sur', 'avec', 'ne', 'se', 'pas', 'tout', 'plus', 'par', 'grand', 'en', 'une', 'être', 'et', 'en', 'avoir', 'que', 'pour'];

    for (const word of words) {
      if (frenchPatterns.includes(word.toLowerCase())) {
        scores.fr += 0.5; // Bonus modéré pour les mots français communs
      }
    }

    console.log('Language detection scores:', scores, 'for words:', words);

    // Retourner la langue avec le score le plus élevé, français par défaut
    const maxLang = Object.entries(scores).reduce((max, [lang, score]) =>
      score > max[1] ? [lang, score] : max, ['fr', 0])[0];

    return maxLang;
  }

  extractSearchQuery(text) {
    // Extraire la requête de recherche en supprimant les mots de commande
    const commandWords = [
      'chercher', 'rechercher', 'search', 'find', 'trouver', 'lookup'
    ];

    let query = text.toLowerCase();
    for (const word of commandWords) {
      query = query.replace(new RegExp(`\\b${word}\\b`, 'gi'), '').trim();
    }

    return query || text;
  }

  // === MÉTHODES D'EXTRACTION DE DONNÉES ===

  extractDate(text) {
    // Motifs de date améliorés avec support français
    const patterns = {
      relative: /\b(?:aujourd'hui|today|demain|tomorrow|hier|yesterday)\b/i,
      french_formal: /\b(?:janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)\s+\d{1,2}\b/i,
      english_formal: /\b(?:january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2}(?:st|nd|rd|th)?\s*,?\s*\d{4}\b/i,
      numeric: /\b\d{1,2}[-/]\d{1,2}(?:[-/]\d{4})?\b/,
      relative_days: /\b(?:prochain|next|dernier|last|ce|this)\s+(?:lundi|monday|mardi|tuesday|mercredi|wednesday|jeudi|thursday|vendredi|friday|samedi|saturday|dimanche|sunday)\b/i
    };

    for (const [type, pattern] of Object.entries(patterns)) {
      const match = text.match(pattern);
      if (match) {
        return this.parseDateFromMatch(match[0], type);
      }
    }
    return null;
  }

  extractTime(text) {
    // Motifs de temps
    const timePatterns = [
      /\b(\d{1,2}):(\d{2})\s*(?:h|am|pm)?\b/i,
      /\b(\d{1,2})h(\d{2})?\b/i,
      /\b(\d{1,2})\s*(?:heures?|h)\s*(\d{2})?\b/i
    ];

    for (const pattern of timePatterns) {
      const match = text.match(pattern);
      if (match) {
        const hours = parseInt(match[1]);
        const minutes = parseInt(match[2] || '0');
        return { hours, minutes };
      }
    }
    return null;
  }

  parseDateFromMatch(match, type) {
    switch (type) {
      case 'relative':
        return this.parseRelativeDate(match);
      case 'french_formal':
      case 'english_formal':
        return new Date(match);
      case 'numeric':
        return this.parseNumericDate(match);
      case 'relative_days':
        return this.parseRelativeDayOfWeek(match);
      default:
        return null;
    }
  }

  parseRelativeDate(match) {
    const today = new Date();
    const lowerMatch = match.toLowerCase();

    if (lowerMatch.includes('aujourd') || lowerMatch.includes('today')) {
      return today;
    } else if (lowerMatch.includes('demain') || lowerMatch.includes('tomorrow')) {
      const tomorrow = new Date(today);
      tomorrow.setDate(today.getDate() + 1);
      return tomorrow;
    } else if (lowerMatch.includes('hier') || lowerMatch.includes('yesterday')) {
      const yesterday = new Date(today);
      yesterday.setDate(today.getDate() - 1);
      return yesterday;
    }
    return null;
  }

  parseNumericDate(match) {
    try {
      // Gérer différents formats de date
      const parts = match.split(/[-/]/);
      if (parts.length >= 2) {
        const day = parseInt(parts[0]);
        const month = parseInt(parts[1]) - 1; // Les mois JavaScript sont indexés à partir de 0
        const year = parts.length > 2 ? parseInt(parts[2]) : new Date().getFullYear();
        return new Date(year, month, day);
      }
    } catch (error) {
      console.error('Erreur lors de l\'analyse de la date numérique:', error);
    }
    return null;
  }

  parseRelativeDayOfWeek(match) {
    // Implémentation pour l'analyse des jours relatifs
    const today = new Date();
    const dayNames = {
      'lundi': 1, 'monday': 1,
      'mardi': 2, 'tuesday': 2,
      'mercredi': 3, 'wednesday': 3,
      'jeudi': 4, 'thursday': 4,
      'vendredi': 5, 'friday': 5,
      'samedi': 6, 'saturday': 6,
      'dimanche': 0, 'sunday': 0
    };

    const lowerMatch = match.toLowerCase();
    for (const [dayName, dayNumber] of Object.entries(dayNames)) {
      if (lowerMatch.includes(dayName)) {
        const targetDate = new Date(today);
        const currentDay = today.getDay();
        let daysToAdd = dayNumber - currentDay;

        if (lowerMatch.includes('prochain') || lowerMatch.includes('next')) {
          daysToAdd += daysToAdd <= 0 ? 7 : 0;
        } else if (lowerMatch.includes('dernier') || lowerMatch.includes('last')) {
          daysToAdd -= daysToAdd >= 0 ? 7 : 0;
        }

        targetDate.setDate(today.getDate() + daysToAdd);
        return targetDate;
      }
    }
    return null;
  }

  extractMeetingParameters(text) {
    // Extraction améliorée des paramètres de réunion
    const titlePatterns = [
      /(?:about|regarding|for|on|sur|à propos de|concernant)\s+(.+?)(?:\s+(?:at|on|with|in|à|avec|dans)\s+|$)/i,
      /(?:réunion|meeting)\s+(.+?)(?:\s+(?:at|on|with|in|à|avec|dans)\s+|$)/i
    ];

    let title = 'Nouvelle Réunion';
    for (const pattern of titlePatterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        title = match[1].trim();
        break;
      }
    }

    return {
      title,
      isUrgent: /\b(?:urgent|immediate|asap|emergency|urgente|immédiat)\b/i.test(text),
      duration: this.extractDuration(text)
    };
  }

  extractDuration(text) {
    const durationPattern = /(\d+)\s*(?:minutes?|mins?|heures?|hours?|h)/i;
    const match = text.match(durationPattern);
    if (match) {
      const value = parseInt(match[1]);
      const unit = match[0].toLowerCase();
      if (unit.includes('h') || unit.includes('hour')) {
        return value * 60; // Convertir en minutes
      }
      return value;
    }
    return 60; // Par défaut 1 heure
  }

  extractSearchTerms(text) {
    // Extraction améliorée des termes de recherche avec support français
    const stopWords = [
      'show', 'display', 'find', 'search', 'get', 'me', 'the', 'files', 'for', 'about',
      'afficher', 'montrer', 'voir', 'chercher', 'trouver', 'les', 'des', 'pour', 'sur'
    ];

    return text
      .toLowerCase()
      .split(/\s+/)
      .filter(term => term.length > 2 && !stopWords.includes(term))
      .slice(0, 5); // Limiter à 5 termes de recherche
  }

  extractFileType(text) {
    const lowerText = text.toLowerCase();

    // Patterns plus spécifiques pour détecter le type de fichier
    const fileTypes = {
      'transcript': [
        'transcription', 'transcript', 'transcrit', 'transcripts',
        'transcription file', 'transcript file', 'texte', 'text',
        'paroles', 'speech', 'spoken', 'verbal', 'dialogue'
      ],
      'summary': [
        'résumé', 'summary', 'compte-rendu', 'pv', 'minutes', 'summaries',
        'summary file', 'résumé de', 'compte rendu', 'procès-verbal',
        'sommaire', 'synthèse', 'recap', 'overview', 'brief',
        'rapport', 'report', 'notes', 'note', 'conclusion'
      ],
      'recording': [
        'enregistrement', 'recording', 'audio', 'video', 'recordings',
        'recording file', 'fichier audio', 'fichier vidéo', 'son', 'sound',
        'voice', 'voix', 'media', 'multimedia', 'mp3', 'mp4', 'wav', 'avi'
      ]
    };

    // Chercher des correspondances exactes d'abord
    for (const [type, keywords] of Object.entries(fileTypes)) {
      for (const keyword of keywords) {
        if (lowerText.includes(keyword.toLowerCase())) {
          console.log(`File type detected: ${type} (matched: ${keyword})`);
          return type;
        }
      }
    }

    console.log('No specific file type detected, returning all');
    return 'all';
  }

  extractHelpTopic(text) {
    const topics = {
      'meeting': ['réunion', 'meeting', 'conference'],
      'navigation': ['navigation', 'naviguer', 'aller', 'calendrier', 'profil', 'entreprise'],
      'files': ['fichiers', 'files', 'documents'],
      'search': ['chercher', 'rechercher', 'search']
    };

    for (const [topic, keywords] of Object.entries(topics)) {
      if (keywords.some(keyword => text.toLowerCase().includes(keyword))) {
        return topic;
      }
    }
    return 'general';
  }

  // === MÉTHODES D'EXÉCUTION DE COMMANDES ===

  // Exécution de commande améliorée avec meilleure gestion d'erreurs
  async executeCommand(command) {
    try {
      switch (command.action) {
        case 'createMeeting':
          return this.handleCreateMeeting(command.parameters);

        case 'navigate':
          return this.handleNavigation(command.parameters);

        case 'viewFiles':
          return this.handleViewFiles(command.parameters);

        case 'search':
          return this.handleSearch(command.parameters);

        case 'help':
          return this.handleHelp(command.parameters);

        case 'downloadFile':
          return this.handleDownloadFile(command.parameters);

        default:
          return {
            success: false,
            message: "Je ne sais pas comment traiter cette commande.",
            suggestions: this.getSuggestedActions(command.transcript?.split(' ') || [], this.currentLanguage)
          };
      }
    } catch (error) {
      console.error('Erreur lors de l\'exécution de la commande:', error);
      return {
        success: false,
        message: "Une erreur s'est produite lors de l'exécution de la commande.",
        error: error.message
      };
    }
  }

  // Gestionnaires de commandes
  handleCreateMeeting(parameters) {
    return {
      success: true,
      message: `Création de la réunion "${parameters.title}" programmée.`,
      action: 'redirect',
      url: '/calendar/new',
      data: parameters
    };
  }

  handleNavigation(parameters) {
    const destinationMessages = {
      calendar: "Navigation vers le calendrier.",
      profile: "Ouverture de votre profil.",
      enterprise: "Accès à la gestion d'entreprise.",
      dashboard: "Retour au tableau de bord."
    };

    return {
      success: true,
      message: destinationMessages[parameters.destination] || `Navigation vers ${parameters.destination}.`,
      action: 'navigate',
      destination: parameters.destination,
      route: parameters.route
    };
  }

  handleViewFiles(parameters) {
    return {
      success: true,
      message: "Affichage des fichiers demandés.",
      action: 'viewFiles',
      filters: parameters
    };
  }

  handleSearch(parameters) {
    return {
      success: true,
      message: `Recherche en cours pour: "${parameters.query}"`,
      action: 'search',
      query: parameters.query,
      searchTerms: parameters.searchTerms
    };
  }

  handleHelp(parameters) {
    const helpMessages = {
      meeting: "Vous pouvez dire 'créer une réunion' pour planifier une nouvelle réunion.",
      navigation: "Dites 'aller au calendrier', 'voir mon profil', ou 'gérer l'entreprise'.",
      files: "Dites 'voir les fichiers' ou 'chercher [terme]' pour trouver des documents.",
      search: "Utilisez 'chercher [terme]' pour rechercher dans l'application.",
      general: "Je peux vous aider avec la navigation, la création de réunions, et la recherche de fichiers. Dites 'aide' suivi du sujet pour plus d'informations."
    };

    return {
      success: true,
      message: helpMessages[parameters.topic] || helpMessages.general,
      action: 'help',
      topic: parameters.topic
    };
  }

  handleDownloadFile(parameters) {
    const { date, fileType, searchTerms } = parameters;

    // Construire le message de confirmation multilingue
    const currentLang = this.currentLanguage || 'fr';
    let message = "";

    if (currentLang.startsWith('en')) {
      message = "Downloading";

      if (fileType && fileType !== 'all') {
        const typeNames = {
          'transcript': 'transcript',
          'summary': 'summary',
          'recording': 'recording'
        };
        message += ` ${typeNames[fileType] || fileType} file`;
      } else {
        message += " file";
      }

      if (date) {
        const dateStr = date.toLocaleDateString('en-US');
        message += ` from ${dateStr}`;
      }

      message += "...";

    } else if (currentLang.startsWith('ar')) {
      message = "تحميل";

      if (fileType && fileType !== 'all') {
        const typeNames = {
          'transcript': 'ملف النسخة',
          'summary': 'ملف الملخص',
          'recording': 'ملف التسجيل'
        };
        message += ` ${typeNames[fileType] || fileType}`;
      } else {
        message += " الملف";
      }

      if (date) {
        const dateStr = date.toLocaleDateString('ar-SA');
        message += ` من ${dateStr}`;
      }

      message += "...";

    } else {
      // Français par défaut
      message = "Téléchargement";

      if (fileType && fileType !== 'all') {
        const typeNames = {
          'transcript': 'de la transcription',
          'summary': 'du résumé',
          'recording': 'de l\'enregistrement'
        };
        message += ` ${typeNames[fileType] || 'du fichier'}`;
      } else {
        message += " du fichier";
      }

      if (date) {
        const dateStr = date.toLocaleDateString('fr-FR');
        message += ` du ${dateStr}`;
      }

      message += " en cours...";
    }

    console.log('Download request:', {
      date: date ? date.toISOString() : null,
      fileType,
      searchTerms,
      message
    });

    return {
      success: true,
      message: message,
      action: 'downloadFile',
      directDownload: true,
      filters: {
        date: date,
        fileType: fileType,
        searchTerms: searchTerms,
        specificDownload: true
      }
    };
  }

  // === MÉTHODES DE RÉSUMÉ ET IA ===

  async summarizeWithCohere(transcript) {
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:8000';
      const apiUrl = baseUrl.endsWith('/api') ? baseUrl : baseUrl + '/api';
      console.log('Service Vocal Unifié - Utilisation de l\'URL API pour la résumé:', apiUrl);

      const response = await axios.post(`${apiUrl}/meetings/cohere/summarize`, {
        transcript,
        model: 'command'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        timeout: 60000
      });

      console.log('Réponse reçue:', response.data);

      if (!response.data || !response.data.summary) {
        console.error('Réponse de résumé invalide:', response.data);
        throw new Error('Réponse de résumé invalide du serveur');
      }

      return response.data.summary;
    } catch (error) {
      console.error('Détails de l\'erreur de résumé:', {
        endpoint: error.config?.url,
        type: error.code,
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });
      throw new Error(`Échec du résumé: ${error.message}`);
    }
  }

  async generateMeetingMinutes(transcript) {
    try {
      if (!transcript || typeof transcript !== 'string') {
        throw new Error('Transcription invalide fournie');
      }
      return await this.summarizeWithCohere(transcript);
    } catch (error) {
      console.error('Erreur lors de la génération des minutes:', error);
      throw error;
    }
  }

  // === MÉTHODES UTILITAIRES ===

  // Traitement de transcription en direct pour les mises à jour en temps réel
  processLiveTranscript(text, onUpdate) {
    if (typeof onUpdate === 'function') {
      // Nettoyer et formater la transcription
      const cleanText = text.trim();
      if (cleanText) {
        onUpdate(cleanText);
      }
    }
    return text;
  }

  // Méthode utilitaire pour valider la structure de commande
  validateCommand(command) {
    return command &&
           typeof command === 'object' &&
           command.action &&
           typeof command.confidence === 'number';
  }

  // Méthode pour obtenir le statut du service
  getStatus() {
    return {
      isLoaded: this.isLoaded,
      isListening: this.isListening,
      isRecording: this.isRecording,
      initializationStatus: this.initializationStatus,
      connectionStatus: this.connectionStatus,
      currentLanguage: this.currentLanguage,
      isOffline: this.isOffline
    };
  }

  // Méthode pour définir la langue
  setLanguage(language) {
    this.currentLanguage = language;
    console.log('UnifiedVoiceService: Language set to', language);
  }

  // Méthode pour réinitialiser le service
  reset() {
    this.stopListening();
    this.stopRecording();
    this.transcriptParts = [];
    this.currentCommand = null;
    this.hasReceivedSpeech = false;
    this.reconnectionAttempts = 0;
  }
}

export default UnifiedVoiceService;