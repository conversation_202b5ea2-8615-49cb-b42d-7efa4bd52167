// filepath: /c:/PFE-2025/backend/models/Enterprise.js
const mongoose = require('mongoose');

const EnterpriseSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  code: {
    type: String,
    required: true,
    unique: true
  },
  adminEmail: {
    type: String,
    required: true
  },
  email: {
    type: String,
    default: function() {
      // Generate a default email based on enterprise name if not provided
      return `contact@${this.name.toLowerCase().replace(/\s+/g, '-')}.com`;
    }
  },
  address: String,
  phone: String,
  founders: [String],
  numberOfEmployees: Number,
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

const Enterprise = mongoose.model('Enterprise', EnterpriseSchema);
module.exports = Enterprise;