{"name": "react-meeting-app", "private": true, "version": "0.1.0", "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@craco/craco": "^7.1.0", "@types/webpack": "4.41.32", "autoprefixer": "^10.4.18", "postcss": "^8.4.35", "react-refresh": "^0.16.0", "serve": "^14.2.0", "tailwindcss": "^3.4.1", "webpack": "^5.90.3", "webpack-cli": "4.10.0"}, "scripts": {"start": "node server.js", "dev": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "dependencies": {"axios": "^1.6.8", "buffer": "^6.0.3", "crypto-browserify": "^3.12.0", "date-fns": "^2.29.3", "file-saver": "^2.0.5", "firebase": "^10.14.1", "framer-motion": "^12.12.2", "i18next": "^25.0.2", "i18next-browser-languagedetector": "^8.1.0", "jszip": "^3.10.1", "lucide-react": "^0.344.0", "microsoft-cognitiveservices-speech-sdk": "^1.43.1", "moment": "^2.29.4", "path-browserify": "^1.0.1", "process": "0.11.10", "react": "^18.3.1", "react-big-calendar": "^1.18.0", "react-dom": "^18.3.1", "react-i18next": "^15.5.1", "react-router-dom": "^6.22.3", "react-scripts": "^5.0.1", "socket.io-client": "^4.8.1", "stream-browserify": "^3.0.0", "web-vitals": "^3.5.2"}, "resolutions": {"process": "0.11.10"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}