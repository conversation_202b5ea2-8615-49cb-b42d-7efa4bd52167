const express = require('express');
const router = express.Router();
const fetch = require('node-fetch');

const COHERE_API_KEY = process.env.COHERE_API_KEY;

router.post('/summarize', async (req, res) => {
  try {
    const { text } = req.body;
    
    const response = await fetch('https://api.cohere.ai/v1/generate', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${COHERE_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'command-r7b-12-2024',
        prompt: `Summarize this meeting transcript in detail, highlighting key points, decisions, and action items:\n\n${text}`,
        max_tokens: 500,
        temperature: 0.3,
        stop_sequences: ["END"]
      })
    });

    if (!response.ok) {
      throw new Error('Failed to generate summary');
    }

    const data = await response.json();
    res.json({ summary: data.generations[0].text });
  } catch (error) {
    console.error('Summarization error:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
