<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>React</title>
    <meta http-equiv="Content-Security-Policy" content="default-src * 'self' data: blob:; connect-src * 'self' http://* https://* https://*.onrender.com https://my-backend-dwmk.onrender.com https://api.cohere.ai https://*.googleapis.com https://*.google.com https://*.firebaseio.com https://*.firebaseapp.com https://securetoken.googleapis.com https://identitytoolkit.googleapis.com https://*.cloudfunctions.net https://apis.google.com https://*.speech.microsoft.com wss://*.speech.microsoft.com http://localhost:* ws://localhost:* wss://localhost:* blob: data:;">
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
