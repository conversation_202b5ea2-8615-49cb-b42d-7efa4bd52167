const { CohereClient } = require('cohere-ai');

const cohere = new CohereClient({
  token: process.env.COHERE_API_KEY,
});

async function generateSummary(text, model = 'command-r7b-12-2024') {
  try {
    const response = await cohere.summarize({
      text,
      model,
      length: 'medium',
      format: 'bullets',
      extractiveness: 'medium',
      temperature: 0.3,
    });
    
    return response.summary;
  } catch (error) {
    console.error('Cohere API error:', error);
    throw new Error('Failed to generate summary: ' + error.message);
  }
}

module.exports = { generateSummary };
