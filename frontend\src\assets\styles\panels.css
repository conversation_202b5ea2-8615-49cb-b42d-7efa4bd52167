/* Styles for Cha<PERSON> and Settings Panels */

/* Common panel styles */
.chat-panel,
.settings-panel {
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.2);
  z-index: 50;
}

.chat-panel[data-open="true"],
.settings-panel[data-open="true"] {
  transform: translateX(0);
}

/* Chat panel specific styles */
.chat-messages {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.typing-indicator .dot {
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  margin: 0 1px;
}

/* Settings panel specific styles */
.settings-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.settings-content::-webkit-scrollbar {
  width: 6px;
}

.settings-content::-webkit-scrollbar-track {
  background: transparent;
}

.settings-content::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

/* Light mode styles */
body.light-mode .chat-panel,
body.light-mode .settings-panel {
  background-color: #f8f9fa;
  color: #212529;
  border-left: 1px solid #dee2e6;
}

body.light-mode .chat-header,
body.light-mode .settings-header,
body.light-mode .settings-tabs,
body.light-mode .settings-footer {
  border-color: #dee2e6;
}

body.light-mode .chat-header h3,
body.light-mode .settings-header h3 {
  color: #212529;
}

body.light-mode .chat-input input,
body.light-mode .settings-content select,
body.light-mode .settings-content input[type="range"] {
  background-color: #e9ecef;
  color: #212529;
  border: 1px solid #ced4da;
}

body.light-mode .message .bg-gray-700 {
  background-color: #e9ecef;
  color: #212529;
}

body.light-mode .message .bg-blue-600 {
  background-color: #007bff;
  color: white;
}

/* Responsive styles */
@media (max-width: 640px) {
  .chat-panel,
  .settings-panel {
    width: 100%;
  }
}
