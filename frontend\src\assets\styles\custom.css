/* Custom styles for the application */

/* Subtle hover animation */
@keyframes subtle-hover {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-3px);
  }
}

/* Subtle pulse animation */
@keyframes subtle-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.1);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Glass effect for cards */
.glass-card {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease-in-out;
}

.glass-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* Improved calendar styling */
.rbc-calendar {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.rbc-header {
  background: rgba(241, 245, 249, 0.8);
  padding: 10px 0;
  font-weight: 500;
  color: #475569;
}

.rbc-event {
  background-color: rgba(59, 130, 246, 0.8) !important;
  border-radius: 4px !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.rbc-event:hover {
  background-color: rgba(59, 130, 246, 0.9) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rbc-today {
  background-color: rgba(219, 234, 254, 0.4) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .glass-card {
    background: rgba(255, 255, 255, 0.98);
  }

  .rbc-calendar {
    border-radius: 4px;
  }
}
