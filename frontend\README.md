# Neeting - Plateforme de Visioconférence Intelligente

Une application de visioconférence moderne avec intelligence artificielle intégrée, développée dans le cadre d'un Projet de Fin d'Études (PFE).

## 🚀 Fonctionnalités Principales

### 🎥 Visioconférence
- Réunions vidéo en temps réel avec WebRTC
- Partage d'écran et contrôles audio/vidéo
- Interface utilisateur responsive et moderne

### 🤖 Intelligence Artificielle
- **Transcription automatique** avec Microsoft Azure Speech Services
- **Génération de résumés** intelligents avec Cohere AI
- **Assistant vocal** pour la navigation et les commandes
- **Reconnaissance vocale multilingue** (Français, Anglais, Arabe)

### 👥 Gestion d'Entreprise
- Système d'authentification avec Firebase
- Gestion des utilisateurs et des rôles
- Espaces d'entreprise sécurisés
- Validation d'administrateur pour nouveaux comptes

### 📅 Calendrier Intégré
- Planification de réunions
- Gestion des événements
- Invitations automatiques aux participants

### 📁 Gestion de Fichiers
- Sauvegarde automatique des transcriptions
- Téléchargement des comptes-rendus
- Historique des réunions par entreprise

## 🛠️ Technologies Utilisées

### Frontend
- **React.js** - Interface utilisateur
- **Tailwind CSS** - Styling moderne
- **Firebase Authentication** - Authentification
- **React Router** - Navigation
- **Framer Motion** - Animations
- **i18next** - Internationalisation

### Backend
- **Node.js & Express** - API RESTful
- **MongoDB & Mongoose** - Base de données
- **Socket.io** - Communication temps réel
- **Firebase Admin SDK** - Gestion utilisateurs

### Services IA
- **Microsoft Azure Cognitive Services** - Reconnaissance vocale
- **Cohere AI** - Génération de texte et résumés
- **Service Vocal Unifié** - Architecture propriétaire

## 📦 Installation et Démarrage

### Prérequis
- Node.js (v16 ou supérieur)
- MongoDB
- Compte Firebase
- Clés API Azure et Cohere

### Installation

1. **Cloner le repository**
   ```bash
   git clone [repository-url]
   cd neeting-project
   ```

2. **Installer les dépendances Frontend**
   ```bash
   cd frontend
   npm install
   ```

3. **Installer les dépendances Backend**
   ```bash
   cd ../backend
   npm install
   ```

4. **Configuration des variables d'environnement**

   **Frontend (.env)**
   ```env
   REACT_APP_FIREBASE_API_KEY=your_firebase_api_key
   REACT_APP_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
   REACT_APP_FIREBASE_PROJECT_ID=your_project_id
   REACT_APP_API_URL=http://localhost:8000
   REACT_APP_AZURE_SPEECH_KEY=your_azure_speech_key
   REACT_APP_AZURE_SPEECH_REGION=your_azure_region
   REACT_APP_COHERE_API_KEY=your_cohere_api_key
   ```

   **Backend (.env)**
   ```env
   MONGODB_URI=your_mongodb_connection_string
   FIREBASE_PROJECT_ID=your_project_id
   FIREBASE_PRIVATE_KEY=your_firebase_private_key
   FIREBASE_CLIENT_EMAIL=your_firebase_client_email
   COHERE_API_KEY=your_cohere_api_key
   PORT=8000
   ```

### Démarrage

1. **Démarrer le Backend**
   ```bash
   cd backend
   npm run dev
   ```

2. **Démarrer le Frontend**
   ```bash
   cd frontend
   npm run dev
   ```

3. **Accéder à l'application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000

## 🏗️ Architecture du Projet

```
neeting-project/
├── frontend/                 # Application React
│   ├── src/
│   │   ├── components/      # Composants réutilisables
│   │   ├── pages/          # Pages principales
│   │   ├── services/       # Services et API
│   │   ├── contexts/       # Contextes React
│   │   └── assets/         # Ressources statiques
│   └── public/             # Fichiers publics
├── backend/                 # API Node.js
│   ├── Models/             # Modèles MongoDB
│   ├── Routes/             # Routes API
│   ├── middleware/         # Middlewares
│   └── utils/              # Utilitaires
├── les diagrammes/         # Documentation technique
└── render.yaml            # Configuration déploiement
```

## 🔧 Fonctionnalités Avancées

### Service Vocal Unifié
Le projet utilise un service vocal unifié qui combine :
- Reconnaissance vocale en temps réel
- Analyse de commandes intelligente
- Exécution automatique d'actions
- Support multilingue

### Commandes Vocales Supportées
```javascript
// Navigation
"aller au calendrier" → Navigation vers le calendrier
"voir mon profil" → Ouverture du profil
"gérer l'entreprise" → Accès à la gestion d'entreprise

// Réunions
"créer une réunion demain à 14h" → Création avec date/heure
"démarrer l'enregistrement" → Début de transcription

// Recherche
"chercher [terme]" → Recherche dans les fichiers
"voir les fichiers d'hier" → Filtrage par date
```

## 🚀 Déploiement

### Frontend (Render.com)
Le frontend est configuré pour le déploiement automatique sur Render.com via `render.yaml`.

### Backend (Render.com)
Le backend est déployé séparément avec MongoDB Atlas.

## 📊 Modèles de Données

### User
- Informations personnelles et professionnelles
- Statut et rôle dans l'entreprise
- Authentification Firebase

### Enterprise
- Informations de l'entreprise
- Gestion des utilisateurs
- Configuration des paramètres

### Meeting
- Détails de la réunion
- Participants et statut
- Fichiers associés

### MeetingFile
- Transcriptions et résumés
- Métadonnées et traçabilité
- Association avec réunions et entreprises

## 🤝 Contribution

Ce projet a été développé dans le cadre d'un PFE à l'entreprise Omnilink avec supervision académique et professionnelle.

## 📄 Licence

Projet académique - Tous droits réservés

## 📞 Support

Pour toute question technique, consulter la documentation dans le dossier `les diagrammes/` ou contacter l'équipe de développement.