import React from 'react';
import { motion } from 'framer-motion';

const AnimatedCard = ({ 
  children, 
  className = '',
  variant = 'default',
  hover = true,
  delay = 0,
  direction = 'up',
  onClick,
  ...props 
}) => {
  const baseClasses = 'rounded-lg shadow-lg transition-all duration-300';
  
  const variants = {
    default: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
    glass: 'bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border border-white/20',
    gradient: 'bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-800 dark:to-blue-900 border border-blue-200 dark:border-blue-700',
    elevated: 'bg-white dark:bg-gray-800 shadow-xl border border-gray-100 dark:border-gray-700'
  };

  const directions = {
    up: { y: 30, opacity: 0 },
    down: { y: -30, opacity: 0 },
    left: { x: -30, opacity: 0 },
    right: { x: 30, opacity: 0 },
    scale: { scale: 0.8, opacity: 0 }
  };

  const cardVariants = {
    hidden: directions[direction],
    visible: {
      x: 0,
      y: 0,
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.6,
        delay: delay,
        ease: "easeOut"
      }
    }
  };

  const hoverVariants = hover ? {
    hover: {
      y: -5,
      scale: 1.02,
      boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  } : {};

  return (
    <motion.div
      className={`${baseClasses} ${variants[variant]} ${className} ${onClick ? 'cursor-pointer' : ''}`}
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover={hover ? "hover" : undefined}
      onClick={onClick}
      {...hoverVariants}
      {...props}
    >
      {children}
    </motion.div>
  );
};

// Specialized card components
export const FeatureCard = ({ icon: Icon, title, description, delay = 0 }) => {
  return (
    <AnimatedCard 
      variant="glass" 
      delay={delay}
      className="p-6 text-center group"
    >
      <motion.div
        className="flex justify-center mb-4"
        whileHover={{ rotate: 5, scale: 1.1 }}
        transition={{ duration: 0.3 }}
      >
        <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full group-hover:bg-blue-200 dark:group-hover:bg-blue-800 transition-colors duration-300">
          <Icon className="w-8 h-8 text-blue-600 dark:text-blue-400" />
        </div>
      </motion.div>
      
      <motion.h3 
        className="text-xl font-semibold mb-2 text-gray-900 dark:text-white"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: delay + 0.2 }}
      >
        {title}
      </motion.h3>
      
      <motion.p 
        className="text-gray-600 dark:text-gray-300"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: delay + 0.4 }}
      >
        {description}
      </motion.p>
    </AnimatedCard>
  );
};

export const StatsCard = ({ value, label, icon: Icon, trend, delay = 0 }) => {
  return (
    <AnimatedCard 
      variant="elevated" 
      delay={delay}
      className="p-6"
    >
      <div className="flex items-center justify-between">
        <div>
          <motion.p 
            className="text-3xl font-bold text-gray-900 dark:text-white"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: delay + 0.2, type: "spring", stiffness: 200 }}
          >
            {value}
          </motion.p>
          <motion.p 
            className="text-sm text-gray-600 dark:text-gray-400"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: delay + 0.4 }}
          >
            {label}
          </motion.p>
          {trend && (
            <motion.div 
              className={`flex items-center mt-2 text-sm ${
                trend > 0 ? 'text-green-600' : 'text-red-600'
              }`}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: delay + 0.6 }}
            >
              <span>{trend > 0 ? '↗' : '↘'}</span>
              <span className="ml-1">{Math.abs(trend)}%</span>
            </motion.div>
          )}
        </div>
        
        {Icon && (
          <motion.div
            className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full"
            whileHover={{ rotate: 360 }}
            transition={{ duration: 0.6 }}
          >
            <Icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </motion.div>
        )}
      </div>
    </AnimatedCard>
  );
};

export const NotificationCard = ({ 
  type = 'info', 
  title, 
  message, 
  onClose, 
  autoClose = true,
  delay = 0 
}) => {
  const typeStyles = {
    success: 'border-green-500 bg-green-50 dark:bg-green-900/20',
    error: 'border-red-500 bg-red-50 dark:bg-red-900/20',
    warning: 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20',
    info: 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
  };

  React.useEffect(() => {
    if (autoClose && onClose) {
      const timer = setTimeout(onClose, 5000);
      return () => clearTimeout(timer);
    }
  }, [autoClose, onClose]);

  return (
    <motion.div
      className={`border-l-4 p-4 rounded-r-lg shadow-lg ${typeStyles[type]}`}
      initial={{ opacity: 0, x: 100, scale: 0.8 }}
      animate={{ opacity: 1, x: 0, scale: 1 }}
      exit={{ opacity: 0, x: 100, scale: 0.8 }}
      transition={{ delay, duration: 0.3 }}
    >
      <div className="flex justify-between items-start">
        <div>
          {title && (
            <h4 className="font-semibold text-gray-900 dark:text-white mb-1">
              {title}
            </h4>
          )}
          <p className="text-gray-700 dark:text-gray-300">{message}</p>
        </div>
        
        {onClose && (
          <motion.button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            ×
          </motion.button>
        )}
      </div>
    </motion.div>
  );
};

export default AnimatedCard;
