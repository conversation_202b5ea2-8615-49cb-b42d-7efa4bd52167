const mongoose = require('mongoose');

const participantSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
    ref: 'User'
  },
  status: {
    type: String,
    enum: ['pending', 'accepted', 'declined'],
    default: 'pending'
  },
  responseTime: {
    type: Date
  }
}, { _id: false });

const EnterpriseEventSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: String,
  startTime: {
    type: Date,
    required: true,
    set: v => v ? new Date(v) : null,
    get: v => v ? v.toISOString() : null
  },
  endTime: {
    type: Date,
    required: true,
    set: v => v ? new Date(v) : null,
    get: v => v ? v.toISOString() : null
  },
  roomId: {
    type: String,
    required: true,
    unique: true
  },
  enterpriseCode: {
    type: String,
    required: true
  },
  createdBy: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['enterprise_event', 'user_event'],
    required: true
  },
  status: {
    type: String,
    enum: ['scheduled', 'in-progress', 'completed', 'cancelled'],
    default: 'scheduled'
  },
  participants: [participantSchema],
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true }
});

// Add validation middleware
EnterpriseEventSchema.pre('save', function(next) {
  if (this.startTime > this.endTime) {
    next(new Error('End time must be after start time'));
  }
  next();
});

module.exports = mongoose.model('EnterpriseEvent', EnterpriseEventSchema);
