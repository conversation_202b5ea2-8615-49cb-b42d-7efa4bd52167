/**
 * API URL utilities to ensure proper URL formatting
 */

/**
 * Ensures a URL has a protocol (http:// or https://)
 * @param {string} url - The URL to check and format
 * @returns {string} - The URL with protocol
 */
export const ensureProtocol = (url) => {
  if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
    return `http://${url}`;
  }
  return url;
};

/**
 * Returns the API URL with protocol
 * @returns {string} - The API URL with protocol
 */
export const getApiUrl = () => {
  const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:8000';
  const apiUrl = baseUrl.endsWith('/api') ? baseUrl : baseUrl + '/api';
  console.log('apiUtils - Using API URL:', apiUrl);
  return ensureProtocol(apiUrl);
};
