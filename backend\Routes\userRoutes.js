const express = require('express');
const router = express.Router();
const User = require('../Models/user');
const nodemailer = require('nodemailer');

// Configure Nodemailer transporter (replace with your SMTP credentials)
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL_USER, // Get from .env
    pass: process.env.EMAIL_PASS  // Get from .env
  }
});

// Verify transporter at startup
transporter.verify(function(error, success) {
  if (error) {
    console.error('Nodemailer transporter error:', error);
  } else {
    console.log('Nodemailer transporter is ready');
  }
});

// Utility to send email
async function sendEmail(to, subject, text) {
  const mailOptions = {
    from: process.env.EMAIL_USER,
    to,
    subject,
    text
  };
  try {
    let info = await transporter.sendMail(mailOptions);
    console.log('Email sent:', info.response);
  } catch (err) {
    console.error('Email send error:', err);
    throw err; // Rethrow to catch in route if needed
  }
}

// Create a new user (registration)
router.post('/register', async (req, res) => {
  try {
    // Accept all fields from the frontend
    const {
      firebaseUid,
      firstName,
      lastName,
      email,
      phone,
      position,
      enterpriseCode,
      status,
      createdAt,
      updatedAt,
      fcmToken
    } = req.body;

    // Check if user already exists by firebaseUid
    let user = await User.findOne({ firebaseUid });

    // Also check if there's a placeholder admin user created during enterprise creation
    let placeholderUser = await User.findOne({
      email: email,
      firebaseUid: email // Placeholder users have email as firebaseUid
    });

    if (user) {
      // Only update fields if the new value is provided (not undefined or null)
      if (typeof firstName !== 'undefined') user.firstName = firstName;
      if (typeof lastName !== 'undefined') user.lastName = lastName;
      if (typeof email !== 'undefined') user.email = email;
      if (typeof phone !== 'undefined') user.phone = phone;
      if (typeof position !== 'undefined') user.position = position;
      if (typeof enterpriseCode !== 'undefined') user.enterpriseCode = enterpriseCode;
      if (typeof status !== 'undefined') user.status = status;
      if (typeof fcmToken !== 'undefined') user.fcmToken = fcmToken;
      user.createdAt = user.createdAt || (createdAt ? new Date(createdAt) : new Date());
      user.updatedAt = new Date();
      await user.save();
    } else if (placeholderUser) {
      // Update placeholder user with real Firebase UID and data
      placeholderUser.firebaseUid = firebaseUid;
      if (typeof firstName !== 'undefined') placeholderUser.firstName = firstName;
      if (typeof lastName !== 'undefined') placeholderUser.lastName = lastName;
      if (typeof phone !== 'undefined') placeholderUser.phone = phone;
      if (typeof position !== 'undefined') placeholderUser.position = position;
      if (typeof enterpriseCode !== 'undefined') placeholderUser.enterpriseCode = enterpriseCode;

      // Check if this user is an admin of an enterprise
      const Enterprise = require('../Models/Enterprise');
      const adminEnterprise = await Enterprise.findOne({ adminEmail: email });
      if (adminEnterprise) {
        placeholderUser.status = 'active'; // Auto-activate enterprise admins
        placeholderUser.position = 'Admin';
      } else {
        if (typeof status !== 'undefined') placeholderUser.status = status;
      }

      placeholderUser.updatedAt = new Date();
      if (typeof fcmToken !== 'undefined') placeholderUser.fcmToken = fcmToken;
      await placeholderUser.save();
      user = placeholderUser;
    } else {
      // Check if this user is an admin of an enterprise
      const Enterprise = require('../Models/Enterprise');
      const adminEnterprise = await Enterprise.findOne({ adminEmail: email });

      // Build the user object with all fields
      const userObj = {
        firebaseUid,
        firstName: firstName || '',
        lastName: lastName || '',
        email: email || '',
        phone: phone || '',
        position: adminEnterprise ? 'Admin' : (position || 'Member'),
        enterpriseCode: enterpriseCode || 'default',
        status: adminEnterprise ? 'active' : (status || 'pending'), // Auto-activate enterprise admins
        createdAt: createdAt ? new Date(createdAt) : new Date(),
        updatedAt: updatedAt ? new Date(updatedAt) : new Date(),
        fcmToken: fcmToken || null
      };
      user = new User(userObj);
      await user.save();
    }

    res.status(201).json(user);
  } catch (error) {
    console.error('Error registering user:', error);
    res.status(500).json({ message: 'Error registering user', error: error.message });
  }
});

// Get pending users for an enterprise
router.get('/pending/:enterpriseCode', async (req, res) => {
  try {
    console.log('Fetching pending users for enterprise:', req.params.enterpriseCode);
    const users = await User.find({
      enterpriseCode: req.params.enterpriseCode,
      $or: [
        { status: 'pending' },
        { status: { $exists: false } }
      ]
    });
    console.log(`Found ${users.length} pending users`);
    res.json(users);
  } catch (error) {
    console.error('Error getting pending users:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Approve user
router.post('/approve/:userId', async (req, res) => {
  try {
    const user = await User.findByIdAndUpdate(
      req.params.userId,
      { status: 'active' },
      { new: true }
    );
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    // Send approval email
    if (user.email) {
      await sendEmail(
        user.email,
        'Your account has been approved',
        `Dear ${user.firstName || ''},\n\nYour account has been approved by the administrator. You can now log in and use the platform.\n\nBest regards,\nNeeting Team`
      );
    }
    res.json(user);
  } catch (error) {
    console.error('Error approving user:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Reject user
router.post('/reject/:userId', async (req, res) => {
  try {
    const user = await User.findByIdAndDelete(req.params.userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    // Send rejection email
    if (user.email) {
      await sendEmail(
        user.email,
        'Your account request has been declined',
        `Dear ${user.firstName || ''},\n\nWe regret to inform you that your account request has been declined by the administrator. If you believe this is a mistake, please contact your enterprise admin.\n\nBest regards,\nNeeting Team`
      );
    }
    res.json({ message: 'User rejected and removed' });
  } catch (error) {
    console.error('Error rejecting user:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Login endpoint to check user status before allowing login
router.post('/login', async (req, res) => {
  try {
    const { email } = req.body;
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    // Only allow login if status is 'active'
    if (user.status !== 'active') {
      return res.status(403).json({ message: 'Account not approved by admin yet.', user });
    }
    res.json({ message: 'Login allowed', user });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get user by Firebase UID (keep this at the end)
router.get('/:firebaseUid', async (req, res) => {
  try {
    console.log('Looking for user with firebaseUid:', req.params.firebaseUid);
    const user = await User.findOne({ firebaseUid: req.params.firebaseUid });

    if (!user) {
      console.log('User not found');
      return res.status(404).json({ message: 'User not found' });
    }

    console.log('User found:', user);
    res.json(user);
  } catch (error) {
    console.error('Error finding user:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Update user
router.put('/:firebaseUid', async (req, res) => {
  try {
    console.log('Updating user:', req.params.firebaseUid, req.body);
    const user = await User.findOneAndUpdate(
      { firebaseUid: req.params.firebaseUid },
      { ...req.body, updatedAt: new Date() },
      { new: true }
    );

    if (!user) {
      console.log('User not found for update');
      return res.status(404).json({ message: 'User not found' });
    }

    console.log('User updated successfully:', user);
    res.json(user);
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ message: 'Error updating user', error: error.message });
  }
});

// Utility endpoint to fix duplicate admin users (for debugging/maintenance)
router.post('/fix-admin-duplicates', async (req, res) => {
  try {
    const { adminEmail } = req.body;

    if (!adminEmail) {
      return res.status(400).json({ message: 'Admin email is required' });
    }

    // Find all users with this email
    const users = await User.find({ email: adminEmail });

    if (users.length <= 1) {
      return res.json({ message: 'No duplicates found', users });
    }

    // Find the placeholder user (firebaseUid = email)
    const placeholderUser = users.find(user => user.firebaseUid === adminEmail);
    // Find the real Firebase user (firebaseUid != email)
    const realUser = users.find(user => user.firebaseUid !== adminEmail);

    if (placeholderUser && realUser) {
      // Delete the placeholder user
      await User.findByIdAndDelete(placeholderUser._id);

      // Ensure the real user has admin status
      const Enterprise = require('../Models/Enterprise');
      const adminEnterprise = await Enterprise.findOne({ adminEmail });
      if (adminEnterprise) {
        realUser.status = 'active';
        realUser.position = 'Admin';
        realUser.enterpriseCode = adminEnterprise.code;
        await realUser.save();
      }

      return res.json({
        message: 'Duplicates fixed successfully',
        deletedPlaceholder: placeholderUser,
        updatedUser: realUser
      });
    }

    res.json({ message: 'No action needed', users });
  } catch (error) {
    console.error('Error fixing admin duplicates:', error);
    res.status(500).json({ message: 'Error fixing duplicates', error: error.message });
  }
});

module.exports = router;