const express = require('express');
const router = express.Router();
const cohere = require('cohere-ai');

// Check for API key and initialize
const COHERE_API_KEY = process.env.COHERE_API_KEY;
if (!COHERE_API_KEY) {
  console.error('Missing COHERE_API_KEY in environment variables');
  process.exit(1);
}

cohere.init(COHERE_API_KEY);

router.post('/summarize', async (req, res) => {
  const { text } = req.body;
  if (!text) return res.status(400).json({ error: 'No text provided' });

  try {
    const response = await cohere.generate({
      model: 'command-r',
      prompt: `Summarize this meeting transcript:\n${text}`,
      max_tokens: 300,
      temperature: 0.3,
    });
    const summary = response.body.generations[0].text.trim();
    res.json({ summary });
  } catch (err) {
    console.error('Cohere API error:', err);
    res.status(500).json({ error: 'Internal server error in Cohere service' });
  }
});

// Add error logging
router.use((err, req, res, next) => {
  console.error('Cohere Route Error:', err);
  res.status(500).json({ error: 'Internal server error in Cohere service' });
});

module.exports = router;
