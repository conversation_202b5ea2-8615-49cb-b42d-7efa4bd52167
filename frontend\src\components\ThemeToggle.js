import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { Sun, Moon } from 'lucide-react';

const ThemeToggle = () => {
  const { darkMode, toggleTheme } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className={`flex items-center justify-center w-12 h-12 rounded-full transition-all duration-300 focus:outline-none shadow-lg ${
        darkMode
          ? 'bg-slate-800 hover:bg-slate-700'
          : 'bg-white hover:bg-gray-100'
      }`}
      aria-label={darkMode ? "Switch to light mode" : "Switch to dark mode"}
    >
      {darkMode ? (
        <Sun className="h-6 w-6 text-yellow-300" />
      ) : (
        <Moon className="h-6 w-6 text-blue-700" />
      )}
    </button>
  );
};

export default ThemeToggle;
