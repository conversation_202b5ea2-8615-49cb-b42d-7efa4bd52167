import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../contexts/ThemeContext';

const AnimatedBackground = ({ variant = 'gradient', intensity = 'medium' }) => {
  const { darkMode } = useTheme();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  if (variant === 'particles') {
    return <ParticleBackground darkMode={darkMode} intensity={intensity} />;
  }

  if (variant === 'waves') {
    return <WaveBackground darkMode={darkMode} />;
  }

  if (variant === 'geometric') {
    return <GeometricBackground darkMode={darkMode} mousePosition={mousePosition} />;
  }

  // Default gradient background
  return <GradientBackground darkMode={darkMode} mousePosition={mousePosition} />;
};

const GradientBackground = ({ darkMode, mousePosition }) => {
  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      <motion.div
        className="absolute inset-0"
        style={{
          background: darkMode
            ? `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, 
               rgba(59, 130, 246, 0.15) 0%, 
               rgba(147, 51, 234, 0.1) 25%, 
               rgba(15, 23, 42, 0.95) 50%,
               rgba(15, 23, 42, 1) 100%)`
            : `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, 
               rgba(59, 130, 246, 0.1) 0%, 
               rgba(147, 51, 234, 0.05) 25%, 
               rgba(248, 250, 252, 0.95) 50%,
               rgba(248, 250, 252, 1) 100%)`
        }}
        animate={{
          background: darkMode
            ? `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, 
               rgba(59, 130, 246, 0.15) 0%, 
               rgba(147, 51, 234, 0.1) 25%, 
               rgba(15, 23, 42, 0.95) 50%,
               rgba(15, 23, 42, 1) 100%)`
            : `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, 
               rgba(59, 130, 246, 0.1) 0%, 
               rgba(147, 51, 234, 0.05) 25%, 
               rgba(248, 250, 252, 0.95) 50%,
               rgba(248, 250, 252, 1) 100%)`
        }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      />
      
      {/* Animated gradient orbs */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full opacity-20"
        style={{
          background: darkMode
            ? 'linear-gradient(45deg, #3b82f6, #8b5cf6)'
            : 'linear-gradient(45deg, #dbeafe, #e0e7ff)'
        }}
        animate={{
          scale: [1, 1.2, 1],
          rotate: [0, 180, 360]
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "linear"
        }}
      />
      
      <motion.div
        className="absolute bottom-1/4 right-1/4 w-64 h-64 rounded-full opacity-15"
        style={{
          background: darkMode
            ? 'linear-gradient(-45deg, #8b5cf6, #ec4899)'
            : 'linear-gradient(-45deg, #fce7f3, #fef3c7)'
        }}
        animate={{
          scale: [1, 0.8, 1],
          rotate: [360, 180, 0]
        }}
        transition={{
          duration: 15,
          repeat: Infinity,
          ease: "linear"
        }}
      />
    </div>
  );
};

const ParticleBackground = ({ darkMode, intensity }) => {
  const particleCount = intensity === 'low' ? 30 : intensity === 'high' ? 80 : 50;
  
  const particles = Array.from({ length: particleCount }, (_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    size: Math.random() * 3 + 1,
    duration: Math.random() * 10 + 10,
    delay: Math.random() * 5
  }));

  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      <div 
        className="absolute inset-0"
        style={{
          background: darkMode
            ? 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)'
            : 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)'
        }}
      />
      
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            background: darkMode
              ? `rgba(59, 130, 246, ${0.3 + Math.random() * 0.4})`
              : `rgba(59, 130, 246, ${0.2 + Math.random() * 0.3})`
          }}
          animate={{
            y: [0, -100, 0],
            x: [0, Math.random() * 50 - 25, 0],
            opacity: [0.3, 0.8, 0.3],
            scale: [1, 1.5, 1]
          }}
          transition={{
            duration: particle.duration,
            repeat: Infinity,
            ease: "easeInOut",
            delay: particle.delay
          }}
        />
      ))}
    </div>
  );
};

const WaveBackground = ({ darkMode }) => {
  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      <div 
        className="absolute inset-0"
        style={{
          background: darkMode
            ? 'linear-gradient(180deg, #0f172a 0%, #1e293b 100%)'
            : 'linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%)'
        }}
      />
      
      <svg
        className="absolute bottom-0 w-full h-64"
        viewBox="0 0 1200 120"
        preserveAspectRatio="none"
      >
        <motion.path
          d="M0,60 C300,120 900,0 1200,60 L1200,120 L0,120 Z"
          fill={darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'}
          animate={{
            d: [
              "M0,60 C300,120 900,0 1200,60 L1200,120 L0,120 Z",
              "M0,80 C300,40 900,100 1200,80 L1200,120 L0,120 Z",
              "M0,60 C300,120 900,0 1200,60 L1200,120 L0,120 Z"
            ]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        <motion.path
          d="M0,80 C300,40 900,100 1200,80 L1200,120 L0,120 Z"
          fill={darkMode ? 'rgba(147, 51, 234, 0.1)' : 'rgba(147, 51, 234, 0.05)'}
          animate={{
            d: [
              "M0,80 C300,40 900,100 1200,80 L1200,120 L0,120 Z",
              "M0,100 C300,60 900,120 1200,100 L1200,120 L0,120 Z",
              "M0,80 C300,40 900,100 1200,80 L1200,120 L0,120 Z"
            ]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        />
      </svg>
    </div>
  );
};

const GeometricBackground = ({ darkMode, mousePosition }) => {
  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      <div 
        className="absolute inset-0"
        style={{
          background: darkMode
            ? 'linear-gradient(45deg, #0f172a 0%, #1e293b 100%)'
            : 'linear-gradient(45deg, #f8fafc 0%, #e2e8f0 100%)'
        }}
      />
      
      {/* Animated geometric shapes */}
      <motion.div
        className="absolute top-20 left-20 w-32 h-32 border-2 border-blue-500/20 rotate-45"
        animate={{
          rotate: [45, 225, 45],
          scale: [1, 1.2, 1]
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: "linear"
        }}
      />
      
      <motion.div
        className="absolute top-1/2 right-20 w-24 h-24 bg-purple-500/10 rounded-full"
        animate={{
          scale: [1, 1.5, 1],
          x: [0, 20, 0],
          y: [0, -20, 0]
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      
      <motion.div
        className="absolute bottom-32 left-1/3 w-16 h-16 bg-gradient-to-r from-blue-500/20 to-purple-500/20 transform rotate-12"
        animate={{
          rotate: [12, 192, 12],
          x: [0, 30, 0]
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "linear"
        }}
      />
      
      {/* Mouse-following element */}
      <motion.div
        className="absolute w-6 h-6 bg-blue-500/30 rounded-full pointer-events-none"
        style={{
          left: `${mousePosition.x}%`,
          top: `${mousePosition.y}%`
        }}
        animate={{
          scale: [1, 1.5, 1]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </div>
  );
};

export default AnimatedBackground;
