import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Video, User, Briefcase } from 'lucide-react';
import axios from 'axios';
import { createUserWithEmailAndPassword } from 'firebase/auth';
import { auth } from '../services/firebase';
import { getApiUrl } from '../utils/apiUtils';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

const Welcome = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [isLogin, setIsLogin] = useState(true);
  const [isResetPassword, setIsResetPassword] = useState(false);
  const [isEnterprise, setIsEnterprise] = useState(false); // <-- Add this line
  const [isResettingPassword, setIsResettingPassword] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    position: '',
    password: '',
    confirmPassword: '',
    enterpriseCode: '',
    enterpriseName: '',
    enterpriseAddress: '',
    enterpriseEmail: '',
    enterprisePhone: '',
    enterpriseFounders: '',
    enterpriseEmployees: '',
    adminEmail: ''
  });
  const [error, setError] = useState('');
  const {
    signIn,
    resetPassword,
    isConfigured
  } = useAuth();

  const countryPhoneRules = {
    '+213': { name: 'Algeria', length: 9 },
    '+244': { name: 'Angola', length: 9 },
    '+61': { name: 'Australia', length: 9 },
    '+43': { name: 'Austria', length: 10 },
    '+32': { name: 'Belgium', length: 9 },
    '+229': { name: 'Benin', length: 8 },
    '+267': { name: 'Botswana', length: 8 },
    '+55': { name: 'Brazil', length: 11 },
    '+226': { name: 'Burkina Faso', length: 8 },
    '+257': { name: 'Burundi', length: 8 },
    '+855': { name: 'Cambodia', length: 9 },
    '+237': { name: 'Cameroon', length: 9 },
    '+1': { name: 'Canada', length: 10 },
    '+238': { name: 'Cape Verde', length: 7 },
    '+236': { name: 'Central African Republic', length: 8 },
    '+235': { name: 'Chad', length: 8 },
    '+56': { name: 'Chile', length: 9 },
    '+86': { name: 'China', length: 11 },
    '+57': { name: 'Colombia', length: 10 },
    '+269': { name: 'Comoros', length: 7 },
    '+242': { name: 'Congo', length: 9 },
    '+243': { name: 'DR Congo', length: 9 },
    '+506': { name: 'Costa Rica', length: 8 },
    '+225': { name: 'Ivory Coast', length: 8 },
    '+385': { name: 'Croatia', length: 9 },
    '+53': { name: 'Cuba', length: 8 },
    '+357': { name: 'Cyprus', length: 8 },
    '+420': { name: 'Czech Republic', length: 9 },
    '+45': { name: 'Denmark', length: 8 },
    '+253': { name: 'Djibouti', length: 8 },
    '+20': { name: 'Egypt', length: 10 },
    '+503': { name: 'El Salvador', length: 8 },
    '+240': { name: 'Equatorial Guinea', length: 9 },
    '+291': { name: 'Eritrea', length: 7 },
    '+372': { name: 'Estonia', length: 7 },
    '+251': { name: 'Ethiopia', length: 9 },
    '+358': { name: 'Finland', length: 9 },
    '+33': { name: 'France', length: 9 },
    '+241': { name: 'Gabon', length: 7 },
    '+220': { name: 'Gambia', length: 7 },
    '+995': { name: 'Georgia', length: 9 },
    '+49': { name: 'Germany', length: 10 },
    '+233': { name: 'Ghana', length: 9 },
    '+30': { name: 'Greece', length: 10 },
    '+502': { name: 'Guatemala', length: 8 },
    '+224': { name: 'Guinea', length: 9 },
    '+245': { name: 'Guinea-Bissau', length: 7 },
    '+592': { name: 'Guyana', length: 7 },
    '+509': { name: 'Haiti', length: 8 },
    '+504': { name: 'Honduras', length: 8 },
    '+36': { name: 'Hungary', length: 9 },
    '+91': { name: 'India', length: 10 },
    '+62': { name: 'Indonesia', length: 10 },
    '+98': { name: 'Iran', length: 10 },
    '+964': { name: 'Iraq', length: 10 },
    '+353': { name: 'Ireland', length: 9 },
    '+972': { name: 'Israel', length: 9 },
    '+39': { name: 'Italy', length: 10 },
    '+81': { name: 'Japan', length: 10 },
    '+962': { name: 'Jordan', length: 9 },
    '+254': { name: 'Kenya', length: 9 },
    '+965': { name: 'Kuwait', length: 8 },
    '+996': { name: 'Kyrgyzstan', length: 9 },
    '+856': { name: 'Laos', length: 10 },
    '+371': { name: 'Latvia', length: 8 },
    '+961': { name: 'Lebanon', length: 8 },
    '+266': { name: 'Lesotho', length: 8 },
    '+231': { name: 'Liberia', length: 7 },
    '+218': { name: 'Libya', length: 9 },
    '+370': { name: 'Lithuania', length: 8 },
    '+352': { name: 'Luxembourg', length: 9 },
    '+261': { name: 'Madagascar', length: 9 },
    '+265': { name: 'Malawi', length: 9 },
    '+60': { name: 'Malaysia', length: 9 },
    '+960': { name: 'Maldives', length: 7 },
    '+223': { name: 'Mali', length: 8 },
    '+356': { name: 'Malta', length: 8 },
    '+222': { name: 'Mauritania', length: 8 },
    '+230': { name: 'Mauritius', length: 7 },
    '+52': { name: 'Mexico', length: 10 },
    '+373': { name: 'Moldova', length: 8 },
    '+377': { name: 'Monaco', length: 8 },
    '+976': { name: 'Mongolia', length: 8 },
    '+212': { name: 'Morocco', length: 9 },
    '+258': { name: 'Mozambique', length: 9 },
    '+95': { name: 'Myanmar', length: 9 },
    '+264': { name: 'Namibia', length: 9 },
    '+977': { name: 'Nepal', length: 10 },
    '+31': { name: 'Netherlands', length: 9 },
    '+64': { name: 'New Zealand', length: 9 },
    '+505': { name: 'Nicaragua', length: 8 },
    '+227': { name: 'Niger', length: 8 },
    '+234': { name: 'Nigeria', length: 10 },
    '+47': { name: 'Norway', length: 8 },
    '+968': { name: 'Oman', length: 8 },
    '+92': { name: 'Pakistan', length: 10 },
    '+507': { name: 'Panama', length: 8 },
    '+595': { name: 'Paraguay', length: 9 },
    '+51': { name: 'Peru', length: 9 },
    '+63': { name: 'Philippines', length: 10 },
    '+48': { name: 'Poland', length: 9 },
    '+351': { name: 'Portugal', length: 9 },
    '+974': { name: 'Qatar', length: 8 },
    '+40': { name: 'Romania', length: 9 },
    '+7': { name: 'Russia', length: 10 },
    '+250': { name: 'Rwanda', length: 9 },
    '+966': { name: 'Saudi Arabia', length: 9 },
    '+221': { name: 'Senegal', length: 9 },
    '+248': { name: 'Seychelles', length: 7 },
    '+232': { name: 'Sierra Leone', length: 8 },
    '+65': { name: 'Singapore', length: 8 },
    '+421': { name: 'Slovakia', length: 9 },
    '+386': { name: 'Slovenia', length: 8 },
    '+27': { name: 'South Africa', length: 9 },
    '+82': { name: 'South Korea', length: 10 },
    '+34': { name: 'Spain', length: 9 },
    '+94': { name: 'Sri Lanka', length: 9 },
    '+249': { name: 'Sudan', length: 9 },
    '+46': { name: 'Sweden', length: 9 },
    '+41': { name: 'Switzerland', length: 9 },
    '+963': { name: 'Syria', length: 9 },
    '+886': { name: 'Taiwan', length: 9 },
    '+255': { name: 'Tanzania', length: 9 },
    '+66': { name: 'Thailand', length: 9 },
    '+228': { name: 'Togo', length: 8 },
    '+216': { name: 'Tunisia', length: 8 },
    '+90': { name: 'Turkey', length: 10 },
    '+256': { name: 'Uganda', length: 9 },
    '+380': { name: 'Ukraine', length: 9 },
    '+971': { name: 'United Arab Emirates', length: 9 },
    '+44': { name: 'UK', length: 10 },
    '+1': { name: 'USA', length: 10 },
    '+598': { name: 'Uruguay', length: 8 },
    '+998': { name: 'Uzbekistan', length: 9 },
    '+58': { name: 'Venezuela', length: 10 },
    '+84': { name: 'Vietnam', length: 9 },
    '+260': { name: 'Zambia', length: 9 },
    '+263': { name: 'Zimbabwe', length: 9 },
  };
  const countryPrefixes = Object.keys(countryPhoneRules);

  const [phonePrefix, setPhonePrefix] = useState('+216'); // Default to Tunisia

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const validateEmail = (email) => {
    // Simple RFC 5322 email regex
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const validateEnterpriseCode = (code) => {
    // Must contain at least one letter and one number
    return /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]+$/.test(code);
  };

  const validatePhone = (prefix, number) => {
    const rule = countryPhoneRules[prefix];
    if (!rule) return false;
    return number.length === rule.length && /^\d+$/.test(number);
  };



  // We no longer need the handleSocialSignupComplete function
  // since we're using a dedicated SocialSignup page

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    if (!isConfigured) {
      setError('Please configure Firebase by setting up your environment variables.');
      return;
    }

    try {
      if (isResetPassword) {
        setIsResettingPassword(true);
        await resetPassword(formData.email);
        setError('Check your email for password reset instructions.');
        setIsResetPassword(false);
        return;
      }

      if (isLogin) {
        const userCredential = await signIn(formData.email, formData.password);
        if (!userCredential || !userCredential.user) {
          setError('Login failed: No user information returned from Firebase.');
          return;
        }
        // Debug logs
        const firebaseUid = userCredential.user.uid;
        const idToken = await userCredential.user.getIdToken();
        console.log('API URL:', getApiUrl());
        console.log('Firebase UID:', firebaseUid);
        console.log('ID Token:', idToken);

        // Fetch user status in MongoDB using UID
        try {
          const response = await axios.get(
            `${getApiUrl()}/users/${firebaseUid}`,
            {
              headers: {
                Authorization: `Bearer ${idToken}`
              }
            }
          );
          console.log('MongoDB user:', response.data); // <-- Add this line for debugging
          const status = response.data?.status;
          if (status !== 'active') {
            await auth.signOut();
            let statusMessage = '';
            if (status === 'pending') {
              statusMessage = 'Your account is still waiting for admin approval.';
            } else if (status === 'inactive') {
              statusMessage = 'Your account has been declined or deactivated by the admin.';
            } else {
              statusMessage = 'Your account is not approved by admin yet.';
            }
            setError(statusMessage);
            setIsLogin(true);
            return;
          }
        } catch (err) {
          await auth.signOut();
          console.error('Login error:', err, err?.response);
          if (err.response && err.response.status === 404) {
            setError('Your account is not registered in our system. Please sign up or contact your administrator.');
            // Optionally, switch to sign up form:
            // setIsLogin(false);
          } else {
            setError(err.response?.data?.message || 'Unable to login. Please wait for admin approval.');
          }
          setIsLogin(true);
          return;
        }
      } else if (isEnterprise) {
        // Enterprise validation
        if (
          !formData.enterpriseName ||
          !formData.enterpriseCode ||
          !formData.enterpriseAddress ||
          !formData.enterpriseEmail ||
          !formData.enterprisePhone ||
          !formData.enterpriseFounders ||
          !formData.enterpriseEmployees ||
          !formData.adminEmail
        ) {
          setError('All fields are required.');
          return;
        }
        if (!validateEnterpriseCode(formData.enterpriseCode)) {
          setError('Enterprise code must contain both letters and numbers.');
          return;
        }
        if (!validateEmail(formData.enterpriseEmail)) {
          setError('Please enter a valid enterprise email address.');
          return;
        }
        if (!validateEmail(formData.adminEmail)) {
          setError('Please enter a valid admin email address.');
          return;
        }
        // When registering enterprise
        const enterpriseData = {
          name: formData.enterpriseName,
          code: formData.enterpriseCode,
          address: formData.enterpriseAddress,
          email: formData.enterpriseEmail,
          phone: formData.enterprisePhone,
          founders: formData.enterpriseFounders.split(','),
          numberOfEmployees: parseInt(formData.enterpriseEmployees, 10),
          adminEmail: formData.adminEmail
        };
        await axios.post(`${getApiUrl()}/enterprises`, enterpriseData);
        // --- Create placeholder admin user in MongoDB ---
        try {
          await axios.post(`${getApiUrl()}/users/register`, {
            firebaseUid: formData.adminEmail, // Use email as placeholder UID
            firstName: 'Admin',
            lastName: '',
            email: formData.adminEmail,
            phone: '',
            position: 'Admin',
            enterpriseCode: formData.enterpriseCode,
            status: 'active' // Will be auto-activated when real admin registers
          });
        } catch (err) {
          // Ignore if already exists
          console.log('Admin user creation skipped:', err.response?.data?.message || err.message);
        }
        setError('Enterprise created successfully. You are now the admin.');
      } else {
        // User validation
        if (
          !formData.firstName ||
          !formData.lastName ||
          !formData.email ||
          !formData.phone ||
          !formData.position ||
          !formData.password ||
          !formData.confirmPassword ||
          !formData.enterpriseCode
        ) {
          setError('All fields are required.');
          return;
        }
        if (!validateEmail(formData.email)) {
          setError('Please enter a valid email address.');
          return;
        }
        if (!validateEnterpriseCode(formData.enterpriseCode)) {
          setError('Enterprise code must contain both letters and numbers.');
          return;
        }
        if (!validatePhone(phonePrefix, formData.phone)) {
          setError(
            `Phone number must be ${countryPhoneRules[phonePrefix].length} digits for ${countryPhoneRules[phonePrefix].name}.`
          );
          return;
        }
        if (formData.password !== formData.confirmPassword) {
          setError('Passwords do not match.');
          return;
        }
        // Create Firebase user first
        const userCredential = await createUserWithEmailAndPassword(
          auth,
          formData.email,
          formData.password
        );
        if (!userCredential || !userCredential.user) {
          setError('Registration failed: No user information returned from Firebase.');
          return;
        }

        // Then register user in MongoDB with all required fields
        const userData = {
          firebaseUid: userCredential.user.uid,
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: `${phonePrefix}${formData.phone}`,
          position: formData.position,
          enterpriseCode: formData.enterpriseCode || 'default',
          status: 'pending' // Backend will auto-activate if this is an enterprise admin
        };

        // Log the endpoint and data for debugging
        console.log('Registering user at:', `${getApiUrl()}/users/register`);
        console.log('User data:', userData);

        try {
          // If your backend expects POST /users, change the endpoint here:
          // const response = await axios.post(`${getApiUrl()}/users`, userData);
          const response = await axios.post(`${getApiUrl()}/users/register`, userData);
          console.log('User registration response:', response.data);
          if (!response.data) {
            throw new Error('No data received from server');
          }
          // Sign out the user after registration
          await auth.signOut();
          setFormData({
            firstName: '',
            lastName: '',
            email: '',
            phone: '',
            position: '',
            password: '',
            confirmPassword: '',
            enterpriseCode: '',
            enterpriseName: '',
            enterpriseAddress: '',
            enterpriseEmail: '',
            enterprisePhone: '',
            enterpriseFounders: '',
            enterpriseEmployees: '',
            adminEmail: ''
          });
          setError('Registration request sent. Please wait for admin approval. You can login once approved.');
          setIsLogin(true); // Switch to login form
        } catch (err) {
          console.error('Registration error:', err.response?.data || err.message);
          throw new Error(err.response?.data?.message || 'Failed to register user');
        }
      }
    } catch (err) {
      console.error('Error during form submission:', err);
      setError(err.response?.data?.message || err.message || 'An error occurred');
    } finally {
      setIsResettingPassword(false);
    }
  };

  const renderResetPassword = () => (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.email')}</label>
        <input
          type="email"
          name="email"
          value={formData.email}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
          required
        />
      </div>
      <button
        type="submit"
        className="w-full bg-blue-600 text-white rounded-md py-2 hover:bg-blue-700 transition-colors disabled:bg-blue-400 dark:disabled:bg-blue-500"
        disabled={isResettingPassword}
      >
        {isResettingPassword ? t('welcome_page.sending_reset_link') : t('welcome_page.reset_password')}
      </button>
      <button
        type="button"
        onClick={() => setIsResetPassword(false)}
        className="w-full text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
        disabled={isResettingPassword}
      >
        {t('welcome_page.back_to_login')}
      </button>
    </form>
  );

  const renderLoginForm = () => (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.email')}</label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.password')}</label>
          <input
            type="password"
            name="password"
            value={formData.password}
            onChange={handleInputChange}
            autoComplete="current-password"
            className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
            required
          />
        </div>

        <button
          type="button"
          onClick={() => setIsResetPassword(true)}
          className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
        >
          {t('welcome_page.forgot_password')}
        </button>

        <button
          type="submit"
          className="w-full bg-blue-600 text-white rounded-md py-2 hover:bg-blue-700 transition-colors"
          disabled={!isConfigured}
        >
          {t('welcome_page.sign_in')}
        </button>
      </form>


    </div>
  );

  // We no longer need the renderSocialSignupForm function
  // since we're using a dedicated SocialSignup page

  const renderSignUpForm = () => (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.enterprise_code')}</label>
          <input
            type="text"
            name="enterpriseCode"
            value={formData.enterpriseCode}
            onChange={handleInputChange}
            className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
            required
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.first_name')}</label>
            <input
              type="text"
              name="firstName"
              value={formData.firstName}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.last_name')}</label>
            <input
              type="text"
              name="lastName"
              value={formData.lastName}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
              required
            />
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.email')}</label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.phone')}</label>
          <div className="flex">
            <select
              value={phonePrefix}
              onChange={e => setPhonePrefix(e.target.value)}
              className="rounded-l-md border border-gray-300 dark:border-gray-700 px-2 py-2 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              style={{ minWidth: 90 }}
            >
              {countryPrefixes.map(prefix => (
                <option key={prefix} value={prefix}>
                  {prefix} ({countryPhoneRules[prefix].name})
                </option>
              ))}
            </select>
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              className="mt-0 block w-full rounded-r-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
              required
              maxLength={countryPhoneRules[phonePrefix]?.length || 15}
              pattern="\d*"
              placeholder={`e.g. ${'0'.repeat(countryPhoneRules[phonePrefix]?.length || 8)}`}
            />
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.position')}</label>
          <input
            type="text"
            name="position"
            value={formData.position}
            onChange={handleInputChange}
            className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.password')}</label>
          <input
            type="password"
            name="password"
            value={formData.password}
            onChange={handleInputChange}
            autoComplete="new-password"
            className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.confirm_password')}</label>
          <input
            type="password"
            name="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleInputChange}
            autoComplete="new-password"
            className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
            required
          />
        </div>
        <button
          type="submit"
          className="w-full bg-blue-600 text-white rounded-md py-2 hover:bg-blue-700 transition-colors"
          disabled={!isConfigured}
        >
          {t('welcome_page.sign_up')}
        </button>
      </form>


    </div>
  );

  const renderEnterpriseForm = () => (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.enterprise_name')}</label>
        <input
          type="text"
          name="enterpriseName"
          value={formData.enterpriseName}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
          required
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.enterprise_code')}</label>
        <input
          type="text"
          name="enterpriseCode"
          value={formData.enterpriseCode}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
          required
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.enterprise_address')}</label>
        <input
          type="text"
          name="enterpriseAddress"
          value={formData.enterpriseAddress}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
          required
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.enterprise_email')}</label>
        <input
          type="email"
          name="enterpriseEmail"
          value={formData.enterpriseEmail}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
          required
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.enterprise_phone')}</label>
        <input
          type="tel"
          name="enterprisePhone"
          value={formData.enterprisePhone}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
          required
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.founders')}</label>
        <input
          type="text"
          name="enterpriseFounders"
          value={formData.enterpriseFounders}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
          required
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.employees_number')}</label>
        <input
          type="number"
          name="enterpriseEmployees"
          value={formData.enterpriseEmployees}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
          required
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('welcome_page.admin_email')}</label>
        <input
          type="email"
          name="adminEmail"
          value={formData.adminEmail}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-blue-500"
          required
        />
      </div>
      <button
        type="submit"
        className="w-full bg-blue-600 text-white rounded-md py-2 hover:bg-blue-700 transition-colors"
        disabled={!isConfigured}
      >
        {t('welcome_page.create_enterprise')}
      </button>
    </form>
  );

  // Set dark mode for welcome page
  React.useEffect(() => {
    // Add dark mode class to body
    document.body.classList.add('dark-mode');

    // Clean up when component unmounts
    return () => {
      document.body.classList.remove('dark-mode');
    };
  }, []);



  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 dark:from-gray-900 dark:to-blue-900 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-xl p-8 w-full max-w-md border border-gray-200 dark:border-gray-700">
        <div
          className="flex items-center justify-center mb-8 cursor-pointer"
          onClick={() => navigate('/')}
          title="Go to landing page"
        >
          <Video className="w-12 h-12 text-blue-600 dark:text-blue-400" />
          <h1 className="text-3xl font-bold ml-2 text-gray-800 dark:text-white">Neeting</h1>
        </div>

        <div className="flex justify-center mb-6">
          <button
            onClick={() => { setIsEnterprise(false); setIsLogin(true); }}
            className={`flex items-center px-4 py-2 mr-2 rounded-md ${!isEnterprise ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'}`}
          >
            <User className="w-5 h-5 mr-2" />
            {t('welcome_page.employee')}
          </button>
          <button
            onClick={() => { setIsEnterprise(true); setIsLogin(false); }}
            className={`flex items-center px-4 py-2 rounded-md ${isEnterprise ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'}`}
          >
            <Briefcase className="w-5 h-5 mr-2" />
            {t('welcome_page.enterprise')}
          </button>
        </div>

        {!isConfigured && (
          <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <p className="text-yellow-800 dark:text-yellow-400 text-sm">
              Please configure Firebase by setting up your environment variables.
            </p>
          </div>
        )}

        {error && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-red-800 dark:text-red-400 text-sm">{error}</p>
          </div>
        )}

        {isResetPassword ? (
          renderResetPassword()
        ) : (
          <>
            {isEnterprise ? renderEnterpriseForm() : (isLogin ? renderLoginForm() : renderSignUpForm())}
            <p className="mt-4 text-center text-sm text-gray-600 dark:text-gray-400">
              {isLogin ? t('welcome_page.dont_have_account') + " " : t('welcome_page.already_have_account') + " "}
              <button
                onClick={() => setIsLogin(!isLogin)}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
              >
                {isLogin ? t('welcome_page.sign_up') : t('welcome_page.sign_in')}
              </button>
            </p>
          </>
        )}
      </div>
    </div>
  );
};

export default Welcome;