const express = require('express');
const MeetingFile = require('../Models/meetingFile');
const router = express.Router();
const admin = require('../firebaseConfig');

// Add auth middleware
const verifyToken = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.split('Bearer ')[1];
    if (!token) {
      return res.status(401).json({ message: 'No token provided' });
    }
    const decodedToken = await admin.auth().verifyIdToken(token);
    req.user = decodedToken;
    next();
  } catch (error) {
    console.error('Auth error:', error);
    res.status(401).json({ message: 'Invalid token' });
  }
};

// Apply auth to all routes
router.use(verifyToken);

// Get meeting files for a user
router.get('/user/:userId', async (req, res) => {
  try {
    console.log('Fetching files for user:', req.params.userId);
    console.log('Auth user:', req.user.uid);
    
    // Verify user is requesting their own files
    if (req.user.uid !== req.params.userId) {
      return res.status(403).json({ message: 'Unauthorized access' });
    }

    const files = await MeetingFile.find({ createdBy: req.params.userId });
    console.log(`Found ${files.length} files:`, files.map(f => ({
      id: f._id,
      name: f.name,
      type: f.type
    })));
    
    res.json(files);
  } catch (err) {
    console.error('Error fetching files:', err);
    res.status(500).json({ message: 'Error fetching files', error: err.message });
  }
});

router.post('/', async (req, res) => {
  try {
    console.log('Creating file with data:', {
      name: req.body.name,
      type: req.body.type,
      createdBy: req.body.createdBy,
      meetingId: req.body.meetingId
    });

    if (req.user.uid !== req.body.createdBy) {
      return res.status(403).json({ message: 'Unauthorized: User ID mismatch' });
    }

    const newFile = new MeetingFile({
      ...req.body,
      date: new Date(),
      type: req.body.type || 'summary'
    });

    const savedFile = await newFile.save();
    console.log('Successfully saved file:', savedFile._id);
    res.status(201).json(savedFile);
  } catch (error) {
    console.error('Save error:', error);
    res.status(500).json({ message: 'Error saving file', error: error.message });
  }
});

// Delete meeting file
router.delete('/:id', async (req, res) => {
  try {
    await MeetingFile.findByIdAndDelete(req.params.id);
    res.json({ message: 'Meeting file deleted successfully' });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;