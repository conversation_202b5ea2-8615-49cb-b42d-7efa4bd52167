import React, { createContext, useState, useContext, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

const LanguageContext = createContext();

export const useLanguage = () => useContext(LanguageContext);

export const LanguageProvider = ({ children }) => {
  const { i18n } = useTranslation();
  
  // Available languages
  const languages = [
    { code: 'en', label: 'English' },
    { code: 'fr', label: 'Français' },
    { code: 'ar', label: 'العربية' },
    { code: 'de', label: 'Deutsch' },
  ];

  // Check if user has a language preference in localStorage
  const [currentLanguage, setCurrentLanguage] = useState(() => {
    const savedLanguage = localStorage.getItem('language');
    // If user has a saved preference, use it
    if (savedLanguage) {
      return savedLanguage;
    }
    // Otherwise, use the current i18n language or default to 'en'
    return i18n.language || 'en';
  });

  // Update language when it changes
  useEffect(() => {
    localStorage.setItem('language', currentLanguage);
    i18n.changeLanguage(currentLanguage);
    
    // Set document direction for RTL languages
    if (currentLanguage === 'ar') {
      document.documentElement.dir = 'rtl';
      document.documentElement.lang = 'ar';
    } else {
      document.documentElement.dir = 'ltr';
      document.documentElement.lang = currentLanguage;
    }
  }, [currentLanguage, i18n]);

  // Change language function
  const changeLanguage = (langCode) => {
    if (languages.some(lang => lang.code === langCode)) {
      setCurrentLanguage(langCode);
    }
  };

  return (
    <LanguageContext.Provider value={{ 
      currentLanguage, 
      changeLanguage, 
      languages 
    }}>
      {children}
    </LanguageContext.Provider>
  );
};

export default LanguageProvider;
