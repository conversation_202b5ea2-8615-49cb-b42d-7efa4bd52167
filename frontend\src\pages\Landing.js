import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Globe } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import AnimatedButton from '../components/AnimatedButton';
import AnimatedCard from '../components/AnimatedCard';
import { StaggerContainer, StaggerItem } from '../components/PageTransition';
import '../assets/styles/enhanced-animations.css';

const Landing = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { currentLanguage, changeLanguage, languages } = useLanguage();
  const [showLang, setShowLang] = useState(false);
  const dropdownRef = useRef(null);
  const buttonRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target)
      ) {
        setShowLang(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-100 to-purple-100 flex flex-col relative">

      {/* Language Switcher */}
      <div className="absolute top-4 right-4 z-50">
        <button
          ref={buttonRef}
          onClick={() => setShowLang(!showLang)}
          className="flex items-center justify-center p-2 rounded-full transition-all duration-300 focus:outline-none bg-white/80 hover:bg-white shadow-md"
          aria-label="Change language"
        >
          <Globe className="h-5 w-5 text-blue-600" />
        </button>

        {showLang && (
          <div
            ref={dropdownRef}
            className="absolute top-12 right-0 bg-white rounded-lg shadow-lg border border-gray-200 min-w-[150px] z-50 animate-slide-down"
          >
            <div className="py-1">
              {languages.map((lang, index) => (
                <button
                  key={lang.code}
                  onClick={() => {
                    changeLanguage(lang.code);
                    setShowLang(false);
                  }}
                  className={`flex items-center w-full text-left px-4 py-2 hover:bg-gray-100 transition-colors stagger-${index + 1} ${
                    currentLanguage === lang.code
                      ? 'font-bold text-blue-600'
                      : 'text-gray-700'
                  }`}
                >
                  {lang.label}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Hero Section */}
      <StaggerContainer className="flex flex-col md:flex-row items-center justify-between px-8 py-16 max-w-6xl mx-auto">
        <StaggerItem className="md:w-1/2 mb-8 md:mb-0">
          <div className="animate-fade-in-up">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 animate-fade-in-up">
              {t('welcome')}
            </h1>
            <p className="text-lg text-gray-700 mb-8 animate-fade-in-up stagger-2">
              {t('description')}
            </p>
            <AnimatedButton
              onClick={() => navigate('/welcome')}
              variant="primary"
              size="lg"
              className="animate-fade-in-up stagger-3"
            >
              {t('sign_in')}
            </AnimatedButton>
          </div>
        </StaggerItem>

        <StaggerItem className="md:w-1/2 flex justify-center">
          <img
            src="/Photo1.png"
            alt="Video conference illustration"
            className="rounded-xl shadow-lg w-full max-w-md hover-lift animate-fade-in-right"
            style={{ objectFit: 'cover' }}
          />
        </StaggerItem>
      </StaggerContainer>

      {/* About Section */}
      <div className="bg-white py-16 px-8">
        <StaggerContainer className="max-w-5xl mx-auto flex flex-col md:flex-row items-center">
          <StaggerItem className="md:w-1/2 mb-8 md:mb-0">
            <img
              src="/Photo2.png"
              alt="About Neeting"
              className="rounded-xl shadow-lg w-full max-w-md hover-lift animate-fade-in-left"
              style={{ objectFit: 'cover' }}
            />
          </StaggerItem>

          <StaggerItem className="md:w-1/2 md:pl-12">
            <AnimatedCard variant="glass" className="p-6">
              <h2 className="text-3xl font-bold text-gray-900 mb-4 animate-fade-in-up">
                {t('about')}
              </h2>
              <p className="text-gray-700 mb-4 animate-fade-in-up stagger-2">
                {t('about_text')}
              </p>
              <ul className="list-disc pl-6 text-gray-700 space-y-2">
                <li className="animate-fade-in-up stagger-3">{t('easy_scheduling')}</li>
                <li className="animate-fade-in-up stagger-4">{t('ai_assistant')}</li>
                <li className="animate-fade-in-up stagger-5">{t('auto_minutes')}</li>
                <li className="animate-fade-in-up stagger-5">{t('enterprise_ready')}</li>
              </ul>
            </AnimatedCard>
          </StaggerItem>
        </StaggerContainer>
      </div>

      {/* Footer */}
      <footer className="mt-auto py-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white text-center animate-fade-in-up">
        &copy; {new Date().getFullYear()} Neeting. {t('all_rights')}
      </footer>
    </div>
  );
};

export default Landing;
