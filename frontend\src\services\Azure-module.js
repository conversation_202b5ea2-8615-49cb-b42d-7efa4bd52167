import * as SpeechSDK from 'microsoft-cognitiveservices-speech-sdk';

class AzureService {
  constructor() {
    this.speechRecognizer = null;
    this.isInitialized = false;
    this.statusListeners = [];
    this.azureConfig = {
      subscriptionKeys: [
        'mJUpBHMrXFRv5dv24CiqHipG46wra01Fre70sUCrNW518bxHep8nJQQJ99BDACYeBjFXJ3w3AAAYACOGNyZU',
        'Cwv9sFu5J2YiQNiQ1XuKVrMOvVLfVuqHOvk2uIVqL6PjtvHcrRdPJQQJ99BDACYeBjFXJ3w3AAAYACOGKffa'
      ],
      region: 'eastus'
    };
    this.activeKeyIndex = 0;
  }

  addStatusListener(callback) {
    this.statusListeners.push(callback);
    callback('initializing');
  }

  updateStatus(status, error = null) {
    this.statusListeners.forEach(callback => callback(status, error));
  }

  async init() {
    let lastError = null;
    for (let i = 0; i < this.azureConfig.subscriptionKeys.length; i++) {
      const key = this.azureConfig.subscriptionKeys[i];
      try {
        if (!key || !this.azureConfig.region) {
          throw new Error('Azure Speech credentials not set');
        }
        const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(key, this.azureConfig.region);
        if (!speechConfig) throw new Error('Invalid Azure Speech config');
        this.activeKeyIndex = i;
        this.isInitialized = true;
        this.updateStatus('ready');
        return Promise.resolve();
      } catch (error) {
        lastError = error;
      }
    }
    this.updateStatus('failed', {
      error: lastError?.message || 'Failed to initialize Azure Speech service',
      details: 'Failed to initialize Azure Speech service'
    });
    throw lastError;
  }

  async transcribeAudio(audioData) {
    return '';
  }

  async start(onTranscribe, onPartial) {
    if (!this.isInitialized) {
      throw new Error('Speech recognition not initialized');
    }
    this.updateStatus('listening');

    const key = this.azureConfig.subscriptionKeys[this.activeKeyIndex];
    const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(
      key,
      this.azureConfig.region
    );
    speechConfig.speechRecognitionLanguage = 'en-US';

    const audioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput();
    this.speechRecognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);

    // Debug: log when recognition starts
    console.log('Azure Speech: Recognition started');

    // Remove previous event handlers (not strictly necessary, but for safety)
    this.speechRecognizer.recognizing = undefined;
    this.speechRecognizer.recognized = undefined;
    this.speechRecognizer.canceled = undefined;
    this.speechRecognizer.sessionStopped = undefined;

    // Partial results (live transcript)
    this.speechRecognizer.recognizing = (s, e) => {
      if (e.result && e.result.text) {
        console.log('[Azure Partial]', e.result.text);
        if (onPartial) onPartial(e.result.text);
      }
    };

    // Final results
    this.speechRecognizer.recognized = (s, e) => {
      if (
        e.result &&
        e.result.text &&
        e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech
      ) {
        console.log('[Azure Final]', e.result.text);
        if (onTranscribe) onTranscribe(e.result.text);
      } else if (
        e.result &&
        e.result.reason === SpeechSDK.ResultReason.NoMatch
      ) {
        console.log('[Azure NoMatch]', e.result.text);
      }
    };

    this.speechRecognizer.canceled = (s, e) => {
      console.error('[Azure Canceled]', e.errorDetails || e);
      this.updateStatus('error', e.errorDetails);
    };

    this.speechRecognizer.sessionStarted = (s, e) => {
      console.log('[Azure Session Started]', e);
    };

    this.speechRecognizer.sessionStopped = (s, e) => {
      console.log('[Azure Session Stopped]', e);
      this.updateStatus('ready');
    };

    // Start recognition and handle errors
    return new Promise((resolve, reject) => {
      try {
        this.speechRecognizer.startContinuousRecognitionAsync(
          () => {
            console.log('Azure Speech: startContinuousRecognitionAsync success');
            resolve(this.speechRecognizer);
          },
          (err) => {
            console.error('Azure Speech: startContinuousRecognitionAsync error', err);
            this.updateStatus('error', err);
            reject(err);
          }
        );
      } catch (err) {
        this.updateStatus('error', err);
        reject(err);
      }
    });
  }

  async stop() {
    if (this.speechRecognizer) {
      return new Promise((resolve) => {
        this.speechRecognizer.stopContinuousRecognitionAsync(() => {
          this.updateStatus('ready');
          this.speechRecognizer = null;
          resolve();
        }, (err) => {
          this.updateStatus('error', err);
          this.speechRecognizer = null;
          resolve();
        });
      });
    } else {
      this.updateStatus('ready');
    }
  }

  async reconnect() {
    try {
      this.updateStatus('reconnecting');
      await this.init();
      return true;
    } catch (error) {
      this.updateStatus('failed', error);
      return false;
    }
  }
}

export default AzureService;
