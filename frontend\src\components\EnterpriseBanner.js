import React, { useState, useEffect } from 'react';
import { Briefcase, Copy, ExternalLink } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { enterpriseAPI } from '../services/mongodb';
import { useTheme } from '../contexts/ThemeContext';

const EnterpriseBanner = ({ enterprise, enterpriseCode }) => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { darkMode } = useTheme();
  const [enterpriseData, setEnterpriseData] = useState(enterprise);
  const [loading, setLoading] = useState(!enterprise && !!enterpriseCode);
  const [copySuccess, setCopySuccess] = useState(false);
  const [currentLang, setCurrentLang] = useState(i18n.language);

  // Debug log for theme and language
  useEffect(() => {
    console.log('EnterpriseBanner: Current theme:', darkMode ? 'dark' : 'light');
    console.log('EnterpriseBanner: Current language:', i18n.language);
    console.log('EnterpriseBanner: Available languages:', i18n.languages);
    console.log('EnterpriseBanner: Translation for "your_enterprise":', t('your_enterprise'));

    // Force a component re-render when language changes
    const handleLanguageChanged = (lang) => {
      console.log('EnterpriseBanner: Language changed event detected to:', lang);
      // Update the current language state to force re-render
      setCurrentLang(lang);
    };

    // Listen for language change events
    i18n.on('languageChanged', handleLanguageChanged);

    // Update current language if it's different from state
    if (i18n.language !== currentLang) {
      console.log('EnterpriseBanner: Language mismatch, updating from', currentLang, 'to', i18n.language);
      setCurrentLang(i18n.language);
    }

    return () => {
      i18n.off('languageChanged', handleLanguageChanged);
    };
  }, [darkMode, i18n, t, currentLang]);

  // Fetch enterprise data if only enterpriseCode is provided
  useEffect(() => {
    const fetchEnterpriseData = async () => {
      if (!enterprise && enterpriseCode) {
        try {
          setLoading(true);
          console.log('EnterpriseBanner: Fetching enterprise data for code:', enterpriseCode);
          const data = await enterpriseAPI.getEnterpriseByCode(enterpriseCode);
          console.log('EnterpriseBanner: Fetched enterprise data:', data);
          setEnterpriseData(data);
        } catch (error) {
          console.error('EnterpriseBanner: Error fetching enterprise data:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchEnterpriseData();
  }, [enterprise, enterpriseCode]);

  if (loading) {
    return (
      <div className={`${
        darkMode
          ? 'bg-gradient-to-r from-slate-800 to-slate-900 border-slate-700'
          : 'bg-gradient-to-r from-gray-100 to-gray-200 border-gray-300'
      } border rounded-xl p-3 mb-5 shadow-md animate-pulse`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className={`${
              darkMode ? 'bg-blue-500 bg-opacity-20' : 'bg-blue-100'
            } p-2 rounded-lg mr-3`}>
              <div className={`h-5 w-5 ${
                darkMode ? 'bg-blue-400' : 'bg-blue-300'
              } rounded-full opacity-50`}></div>
            </div>
            <div>
              <div className={`h-3 w-24 ${
                darkMode ? 'bg-blue-300' : 'bg-blue-200'
              } rounded mb-2`}></div>
              <div className={`h-4 w-32 ${
                darkMode ? 'bg-gray-200' : 'bg-gray-300'
              } rounded`}></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!enterpriseData || !enterpriseData.name) {
    return null;
  }

  const copyEnterpriseCode = () => {
    if (enterpriseData.code) {
      navigator.clipboard.writeText(enterpriseData.code);
      // Show a temporary success message
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    }
  };

  return (
    <div
      key={`enterprise-banner-${currentLang}-${darkMode ? 'dark' : 'light'}`}
      className={`${
        darkMode
          ? 'bg-gradient-to-r from-slate-800 to-slate-900 border-slate-700 text-white'
          : 'bg-gradient-to-r from-blue-50 to-indigo-50 border-gray-300 text-gray-800'
      } border rounded-xl p-3 mb-5 shadow-md transition-colors duration-200`}>
      <div className="flex flex-wrap md:flex-nowrap items-center justify-between gap-3">
        <div className="flex items-center">
          <div className={`${
            darkMode
              ? 'bg-blue-500 bg-opacity-20'
              : 'bg-blue-100'
          } p-2 rounded-lg mr-3`}>
            <Briefcase className={`h-5 w-5 ${
              darkMode ? 'text-blue-400' : 'text-blue-600'
            }`} />
          </div>
          <div>
            <h3 className={`${
              darkMode ? 'text-blue-300' : 'text-blue-700'
            } text-sm font-medium`}>{t('your_enterprise')}</h3>
            <p className={`${
              darkMode ? 'text-gray-200' : 'text-gray-800'
            } font-semibold`}>{enterpriseData.name}</p>
          </div>
        </div>

        <div className="flex items-center space-x-3 ml-auto">
          <div
            className={`relative group ${
              darkMode
                ? 'bg-blue-900 bg-opacity-30 hover:bg-blue-800'
                : 'bg-blue-100 hover:bg-blue-200'
            } px-3 py-1.5 rounded-lg flex items-center cursor-pointer transition-colors`}
            onClick={copyEnterpriseCode}
            title={t('click_to_copy')}
          >
            <span className={`${
              darkMode ? 'text-blue-300' : 'text-blue-700'
            } text-sm font-mono mr-2`}>{enterpriseData.code}</span>
            <Copy className={`h-3.5 w-3.5 ${
              darkMode ? 'text-blue-400' : 'text-blue-600'
            } opacity-70 group-hover:opacity-100`} />

            {/* Copy success message */}
            {copySuccess && (
              <div className={`absolute -top-8 left-1/2 transform -translate-x-1/2 ${
                darkMode
                  ? 'bg-green-800 text-green-100'
                  : 'bg-green-100 text-green-800'
              } px-2 py-1 rounded text-xs whitespace-nowrap`}>
                {t('code_copied')}
              </div>
            )}
          </div>

          <button
            onClick={() => navigate('/profile')}
            className={`text-xs ${
              darkMode
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            } px-3 py-1.5 rounded-lg transition-colors flex items-center`}
            aria-label={t('view_details')}
          >
            <span className="mr-1">{t('view_details')}</span>
            <ExternalLink className="h-3 w-3" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default EnterpriseBanner;
