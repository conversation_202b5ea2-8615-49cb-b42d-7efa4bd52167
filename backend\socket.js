const socketIo = require('socket.io');
const Meeting = require('./Models/Meeting');
const User = require('./Models/user');

// Map to store active users in each room
const rooms = new Map();

function initializeSocket(server) {
  // Define allowed origins
  const allowedOrigins = [
    'http://localhost:3000',
    'https://my-frontend-r5ap.onrender.com',
    'https://pfe-2025.onrender.com',
    'https://my-backend-dwmk.onrender.com'
  ];

  console.log('Initializing Socket.io server with allowed origins:', allowedOrigins);

  // Create a simpler, more reliable Socket.io server configuration
  const io = socketIo(server, {
    path: '/socket.io',
    cors: {
      origin: '*', // Allow all origins for now
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With', 'Connection', 'Upgrade'],
      credentials: false // Set to false to avoid CORS issues
    },
    allowEIO3: true, // Allow Engine.IO v3 clients for backward compatibility
    transports: ['polling', 'websocket'], // Try polling first, then websocket
    pingTimeout: 30000, // 30 seconds ping timeout
    pingInterval: 10000, // 10 seconds ping interval
    connectTimeout: 20000, // 20 seconds connection timeout
    maxHttpBufferSize: 1e6, // 1MB buffer size (more reasonable)
    allowUpgrades: true, // Allow transport upgrades
    cookie: false, // Disable cookies to avoid CORS issues
    serveClient: false // Don't serve client files
  });

  // Log all socket.io events for debugging
  io.engine.on('initial_headers', (headers, req) => {
    console.log('Socket.io initial headers:', headers);
  });

  io.engine.on('headers', (headers, req) => {
    console.log('Socket.io headers:', headers);
  });

  io.engine.on('connection_error', (err) => {
    console.error('Socket.io connection error:', err);
  });

  // Debug socket connections
  io.engine.on('connection', (socket) => {
    console.log(`New transport connection: ${socket.id}`);
  });

  io.engine.on('connection_error', (err) => {
    console.error('Transport connection error:', err);
  });

  io.on('connection', (socket) => {
    console.log(`New socket connection: ${socket.id}`);

    // Debug socket events
    socket.onAny((event, ...args) => {
      console.log(`Socket ${socket.id} event: ${event}`, args);
    });

    // Join a meeting room
    socket.on('join-room', async ({ roomId, userId, userName }) => {
      try {
        console.log(`User ${userName} (${userId}) joining room: ${roomId}`);

        // Try to get the user's full name from the database
        let fullName = userName;
        try {
          const dbUser = await User.findOne({ firebaseUid: userId });
          if (dbUser) {
            fullName = `${dbUser.firstName} ${dbUser.lastName}`.trim();
            console.log(`Found user in database: ${fullName} (${userId})`);
          }
        } catch (dbError) {
          console.error('Error fetching user from database:', dbError);
        }

        // Join the socket room
        socket.join(roomId);
        console.log(`Socket ${socket.id} joined room ${roomId}`);

        // Initialize room if it doesn't exist
        if (!rooms.has(roomId)) {
          rooms.set(roomId, new Map());
          console.log(`Created new room: ${roomId}`);
        }

        // Add user to the room with timestamp and additional metadata
        const roomUsers = rooms.get(roomId);
        roomUsers.set(userId, {
          id: userId,
          name: fullName, // Use the full name from database if available
          socketId: socket.id,
          joinedAt: new Date().toISOString(),
          isMuted: false,
          isVideoOff: false
        });
        console.log(`Added user ${fullName} (${userId}) to room ${roomId}`);

        // Update meeting participants in database
        try {
          const meeting = await Meeting.findOne({ roomId });
          if (meeting) {
            // Add participant if not already in the list
            if (!meeting.participants.includes(userId)) {
              meeting.participants.push(userId);
              await meeting.save();
              console.log(`Updated meeting ${roomId} participants in database`);
            }
          } else {
            console.log(`Meeting ${roomId} not found in database`);
          }
        } catch (error) {
          console.error('Error updating meeting participants:', error);
        }

        // Notify everyone in the room about the new user
        socket.to(roomId).emit('user-connected', {
          userId,
          userName: fullName // Use the full name from database
        });
        console.log(`Notified room ${roomId} about new user ${fullName}`);

        // Send list of existing users to the new participant with more details
        const existingUsers = Array.from(roomUsers.values())
          .filter(user => user.id !== userId)
          .map(user => ({
            id: user.id,
            name: user.name,
            socketId: user.socketId,
            // Add timestamp to help with debugging
            joinedAt: user.joinedAt || new Date().toISOString()
          }));

        // Log each existing user for debugging
        existingUsers.forEach(user => {
          console.log(`Existing user in room ${roomId}: ${user.name} (${user.id}), socket: ${user.socketId}`);
        });

        socket.emit('existing-users', existingUsers);
        console.log(`Sent ${existingUsers.length} existing users to ${fullName} (${userId})`);

        console.log(`Room ${roomId} now has ${roomUsers.size} users`);
      } catch (error) {
        console.error('Error in join-room:', error);
      }
    });

    // Handle WebRTC signaling with improved error handling and logging
    socket.on('signal', ({ userId, targetId, signal }) => {
      // Enhanced logging with signal type
      const signalType = signal.type || (signal.candidate ? 'ICE candidate' : 'unknown');
      console.log(`Signal from ${userId} to ${targetId}`, {
        type: signalType,
        time: new Date().toISOString()
      });

      // Validate input parameters
      if (!userId || !targetId) {
        console.error('Invalid signal parameters:', { userId, targetId });
        return;
      }

      // Check if users are in the same room first (more efficient)
      let targetUser = null;
      let sharedRoomId = null;

      // Find rooms where the sender is present
      for (const [roomId, users] of rooms.entries()) {
        if (users.has(userId)) {
          // Check if target is also in this room
          if (users.has(targetId)) {
            targetUser = users.get(targetId);
            sharedRoomId = roomId;
            break;
          }
        }
      }

      // If not found in shared room, search all rooms (fallback)
      if (!targetUser) {
        for (const [roomId, users] of rooms.entries()) {
          targetUser = users.get(targetId);
          if (targetUser) {
            sharedRoomId = roomId;
            break;
          }
        }
      }

      if (targetUser && targetUser.socketId) {
        console.log(`Found target user ${targetId} in room ${sharedRoomId}, socket ID: ${targetUser.socketId}`);

        // Check if the socket is still connected
        const targetSocket = io.sockets.sockets.get(targetUser.socketId);
        if (targetSocket) {
          // Forward the signal to the target user
          targetSocket.emit('signal', {
            userId,
            signal
          });
          console.log(`Signal forwarded to socket ${targetUser.socketId}`);
        } else {
          console.error(`Target socket ${targetUser.socketId} no longer connected`);

          // Clean up the disconnected user
          const roomUsers = rooms.get(sharedRoomId);
          if (roomUsers) {
            roomUsers.delete(targetId);
            console.log(`Removed disconnected user ${targetId} from room ${sharedRoomId}`);

            // Notify others in the room
            socket.to(sharedRoomId).emit('user-disconnected', targetId);
          }
        }
      } else {
        console.log(`Target user ${targetId} not found in any room`);

        // Log detailed room information for debugging
        console.log('Current rooms:', Array.from(rooms.keys()));
        console.log('Users in rooms:', Array.from(rooms.entries()).map(([roomId, users]) =>
          `Room ${roomId}: ${Array.from(users.keys()).join(', ')}`
        ));

        // Send error back to sender
        socket.emit('signal-error', {
          targetId,
          error: 'User not found in any room'
        });
      }
    });

    // Handle user disconnect
    socket.on('disconnect', () => {
      console.log(`Socket disconnected: ${socket.id}`);

      let userFound = false;

      // Find and remove user from all rooms
      for (const [roomId, users] of rooms.entries()) {
        for (const [userId, user] of users.entries()) {
          if (user.socketId === socket.id) {
            userFound = true;
            const userName = user.name;

            console.log(`Found disconnected user: ${userName} (${userId}) in room ${roomId}`);
            users.delete(userId);
            console.log(`User ${userName} (${userId}) removed from room ${roomId}`);

            // Notify others in the room
            socket.to(roomId).emit('user-disconnected', userId);
            console.log(`Notified room ${roomId} about user ${userName} disconnection`);

            // If room is empty, remove it
            if (users.size === 0) {
              rooms.delete(roomId);
              console.log(`Room ${roomId} removed (empty)`);
            } else {
              console.log(`Room ${roomId} now has ${users.size} users remaining`);
            }

            break;
          }
        }
      }

      if (!userFound) {
        console.log(`Could not find user for disconnected socket ${socket.id}`);
      }
    });

    // Handle explicit leave room
    socket.on('leave-room', ({ roomId, userId }) => {
      console.log(`User ${userId} explicitly leaving room ${roomId}`);

      const roomUsers = rooms.get(roomId);
      if (roomUsers) {
        if (roomUsers.has(userId)) {
          const user = roomUsers.get(userId);
          roomUsers.delete(userId);
          console.log(`User ${user.name} (${userId}) removed from room ${roomId}`);

          // Notify others in the room
          socket.to(roomId).emit('user-disconnected', userId);

          // If room is empty, remove it
          if (roomUsers.size === 0) {
            rooms.delete(roomId);
            console.log(`Room ${roomId} removed (empty)`);
          }
        } else {
          console.log(`User ${userId} not found in room ${roomId}`);
        }
      } else {
        console.log(`Room ${roomId} not found`);
      }
    });

    // Handle mute state changes
    socket.on('user-mute-state', ({ roomId, userId, isMuted }) => {
      console.log(`User ${userId} in room ${roomId} ${isMuted ? 'muted' : 'unmuted'} their microphone`);

      // Update user state in the room
      const roomUsers = rooms.get(roomId);
      if (roomUsers && roomUsers.has(userId)) {
        const user = roomUsers.get(userId);
        user.isMuted = isMuted;

        // Notify others in the room
        socket.to(roomId).emit('user-mute-state-changed', {
          userId,
          isMuted
        });
      }
    });

    // Handle video state changes
    socket.on('user-video-state', ({ roomId, userId, isVideoOff }) => {
      console.log(`User ${userId} in room ${roomId} turned their video ${isVideoOff ? 'off' : 'on'}`);

      // Update user state in the room
      const roomUsers = rooms.get(roomId);
      if (roomUsers && roomUsers.has(userId)) {
        const user = roomUsers.get(userId);
        user.isVideoOff = isVideoOff;

        // Notify others in the room
        socket.to(roomId).emit('user-video-state-changed', {
          userId,
          isVideoOff
        });
      }
    });

    // Handle live transcript sharing
    socket.on('share-transcript', ({ roomId, userId, transcript, userName }) => {
      console.log(`User ${userId} shared transcript in room ${roomId}: "${transcript.substring(0, 50)}..."`);

      // Get the user's name from the room if available
      let speakerName = userName;
      const roomUsers = rooms.get(roomId);
      if (roomUsers && roomUsers.has(userId)) {
        const user = roomUsers.get(userId);
        if (user.name && !user.name.startsWith('User-')) {
          speakerName = user.name;
        }
      }

      // Broadcast the transcript to all users in the room (including the sender)
      io.to(roomId).emit('transcript-update', {
        userId,
        userName: speakerName,
        transcript,
        timestamp: new Date().toISOString()
      });
    });

    // Handle user info requests
    socket.on('get-user-info', async ({ userId }) => {
      console.log(`Received request for user info: ${userId}`);

      try {
        // First, try to find the user in the database
        const dbUser = await User.findOne({ firebaseUid: userId });

        if (dbUser) {
          // User found in database, use their full name
          const fullName = `${dbUser.firstName} ${dbUser.lastName}`.trim();
          console.log(`Found user in database: ${fullName} (${userId})`);

          // Search for the user in rooms to get roomId
          let roomId = null;
          for (const [rid, users] of rooms.entries()) {
            if (users.has(userId)) {
              roomId = rid;
              // Update the user's name in the room
              const user = users.get(userId);
              user.name = fullName;
              break;
            }
          }

          const userInfo = {
            userId: userId,
            name: fullName,
            roomId: roomId,
            fromDatabase: true
          };

          // Send the user info back to the requester
          socket.emit('user-info', userInfo);
          console.log(`Sent user info from database for ${userId} to socket ${socket.id}`);
          return;
        }

        // If not found in database, search in rooms
        let userInfo = null;
        for (const [roomId, users] of rooms.entries()) {
          if (users.has(userId)) {
            const user = users.get(userId);
            userInfo = {
              userId: userId,
              name: user.name,
              roomId: roomId
            };
            console.log(`Found user info in room: ${user.name} (${userId}) in room ${roomId}`);
            break;
          }
        }

        if (userInfo) {
          // Send the user info back to the requester
          socket.emit('user-info', userInfo);
          console.log(`Sent user info for ${userId} to socket ${socket.id}`);
        } else {
          // User not found anywhere
          console.log(`User ${userId} not found in database or any room`);
          socket.emit('user-info', {
            userId: userId,
            name: `User-${userId.substring(0, 5)}`,
            notFound: true
          });
        }
      } catch (error) {
        console.error(`Error getting user info for ${userId}:`, error);
        // Send fallback info
        socket.emit('user-info', {
          userId: userId,
          name: `User-${userId.substring(0, 5)}`,
          notFound: true,
          error: true
        });
      }
    });
  });

  return io;
}

module.exports = { initializeSocket };
