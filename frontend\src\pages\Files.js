import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { FileText, Trash2, ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { meetingFilesAPI } from '../services/mongodb';

const Files = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [meetingFiles, setMeetingFiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchFiles = async () => {
      if (!user) {
        console.log('No user found');
        return;
      }

      try {
        setLoading(true);
        setError('');
        console.log('Fetching files for user:', user.uid);

        const files = await meetingFilesAPI.getUserMeetingFiles(user.uid);
        console.log('Received files:', files);

        if (Array.isArray(files)) {
          setMeetingFiles(files);
        } else {
          console.error('Invalid files response:', files);
          setError('Invalid data received from server');
        }
      } catch (error) {
        console.error('Files fetch error:', error);
        setError(error.message || 'Failed to load files');
      } finally {
        setLoading(false);
      }
    };

    fetchFiles();

    // Refresh files when updated
    window.addEventListener('filesUpdated', fetchFiles);
    return () => window.removeEventListener('filesUpdated', fetchFiles);
  }, [user]);

  const deleteFile = async (id) => {
    try {
      // Delete from MongoDB using the correct method
      await meetingFilesAPI.deleteMeetingFile(id);

      // Update the UI after successful deletion
      setMeetingFiles(files => files.filter(file => file._id !== id));

      // Clear any previous errors
      setError('');
    } catch (error) {
      console.error('Error deleting file:', error);

      // Fallback to local storage if API fails
      const savedFiles = JSON.parse(localStorage.getItem('meetingFiles') || '[]');
      const updatedFiles = savedFiles.filter(file => file.id !== id);
      localStorage.setItem('meetingFiles', JSON.stringify(updatedFiles));

      // Update UI anyway to provide better UX
      setMeetingFiles(files => files.filter(file => file._id !== id || file.id !== id));

      // Show error message
      setError('Failed to delete file from server. Local copy removed.');
    }
  };

  const viewFile = async (file) => {
    try {
      // Create blob from content
      const blob = new Blob([file.content], { type: 'text/plain' });
      // Create download link
      const fileName = `${file.name}${file.type === 'summary' ? '_summary' : '_full'}.txt`;
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;

      // Trigger download
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading file:', error);
      setError('Failed to download file');
    }
  };

  return (
    <div className="min-h-screen theme-page-bg">
      <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <button
              onClick={() => navigate('/home')}
              className="mr-4 theme-text hover:opacity-80"
            >
              <ArrowLeft className="h-6 w-6" />
            </button>
            <h1 className="text-2xl font-bold theme-text">Meeting Files</h1>
          </div>
        </div>

        {error && (
          <div className="mb-6 p-4 theme-container border rounded-lg">
            <p className="theme-text">{error}</p>
          </div>
        )}

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 theme-text">Loading files...</p>
          </div>
        ) : meetingFiles.length === 0 ? (
          <div className="text-center py-16 theme-container rounded-lg shadow-sm border-2 border-dashed">
            <FileText className="mx-auto h-16 w-16 theme-text opacity-40 mb-4" />
            <h3 className="text-lg font-medium theme-text mb-2">No Meeting Files Yet</h3>
            <p className="text-sm theme-label max-w-sm mx-auto mb-6">
              Your meeting recordings and transcripts will appear here. Start a meeting and use the recording feature to create files.
            </p>
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => navigate('/home')}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium theme-button theme-button-primary"
              >
                Start New Meeting
              </button>
              <button
                onClick={() => navigate('/home')}
                className="inline-flex items-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium theme-text theme-container hover:opacity-80"
              >
                View Calendar
              </button>
            </div>
            <div className="mt-8 max-w-sm mx-auto">
              <h4 className="text-sm font-medium theme-text mb-2">Quick Tips:</h4>
              <ul className="text-sm theme-label text-left space-y-2">
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  Click the record button during meetings to save transcripts
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  Get both AI-summarized notes and full transcripts
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  Access your meeting files anytime from here
                </li>
              </ul>
            </div>
          </div>
        ) : (
          <div className="theme-container rounded-lg shadow overflow-hidden">
            <ul className="divide-y">
              {meetingFiles.map((file) => (
                <li
                  key={file._id || file.id}
                  className="px-6 py-4 hover:opacity-90 transition-opacity"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center flex-1">
                      <FileText className={`h-6 w-6 ${file.type === 'transcript' ? 'text-purple-600' : 'text-blue-600'}`} />
                      <div className="ml-3">
                        <p className="text-sm font-medium theme-text">
                          {file.name}
                          <span className="ml-2 text-xs px-2 py-1 rounded-full theme-container">
                            {file.type === 'transcript' ? 'Full Transcript' : 'Summary'}
                          </span>
                        </p>
                        <p className="text-sm theme-label">
                          {format(new Date(file.date), 'PPp')}
                        </p>
                        <div className="flex space-x-2 mt-1">
                          <button
                            onClick={() => viewFile(file)}
                            className="text-xs text-blue-600 hover:underline"
                          >
                            Download
                          </button>
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => deleteFile(file._id || file.id)}
                      className="ml-4 theme-label hover:text-red-600"
                      title="Delete file"
                    >
                      <Trash2 className="h-5 w-5" />
                    </button>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default Files;