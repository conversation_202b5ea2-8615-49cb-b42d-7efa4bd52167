import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { userAPI, enterpriseAPI } from '../services/mongodb';
import { ArrowLeft } from 'lucide-react';
import { getAuth, updatePassword, EmailAuthProvider, reauthenticateWithCredential } from 'firebase/auth';
import EnterpriseInfo from '../components/EnterpriseInfo';

const Profile = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    position: '',
    enterpriseCode: ''
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(true);
  const [enterprise, setEnterprise] = useState(null);
  const [enterpriseLoading, setEnterpriseLoading] = useState(true);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [passwordError, setPasswordError] = useState('');
  const [passwordSuccess, setPasswordSuccess] = useState('');
  const auth = getAuth();

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        if (!user?.uid) {
          console.log('No user ID available');
          return;
        }
        console.log('Fetching user data for:', user.uid);
        const userData = await userAPI.getUserById(user.uid);
        if (!userData) {
          throw new Error('No user data received');
        }
        setFormData(userData);
        setLoading(false);

        // Fetch enterprise data using the user's enterprise code
        if (userData.enterpriseCode) {
          try {
            setEnterpriseLoading(true);
            const enterpriseData = await enterpriseAPI.getEnterpriseByCode(userData.enterpriseCode);
            console.log('Fetched enterprise data:', enterpriseData);
            setEnterprise(enterpriseData);
          } catch (enterpriseError) {
            console.error('Error fetching enterprise data:', enterpriseError);
          } finally {
            setEnterpriseLoading(false);
          }
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        setError(error.message);
        setLoading(false);
      }
    };

    if (user) {
      fetchUserData();
    }
  }, [user]);

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setLoading(true);

    try {
      console.log('Updating user profile:', formData);
      await userAPI.updateUser(user.uid, {
        ...formData,
        updatedAt: new Date().toISOString()
      });

      // Update Firebase display name
      try {
        await user.updateProfile({
          displayName: `${formData.firstName} ${formData.lastName}`
        });
      } catch (firebaseError) {
        console.error('Firebase profile update error:', firebaseError);
      }

      setSuccess('Profile updated successfully');
    } catch (error) {
      console.error('Profile update error:', error);
      setError('Failed to update profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async (e) => {
    e.preventDefault();
    setPasswordError('');
    setPasswordSuccess('');
    setLoading(true);

    try {
      const { currentPassword, newPassword, confirmPassword } = passwordData;

      if (newPassword !== confirmPassword) {
        setPasswordError('New passwords do not match');
        return;
      }

      if (newPassword.length < 6) {
        setPasswordError('Password must be at least 6 characters long');
        return;
      }

      // Re-authenticate user before password change
      const credential = EmailAuthProvider.credential(
        auth.currentUser.email,
        currentPassword
      );
      await reauthenticateWithCredential(auth.currentUser, credential);

      // Update password in Firebase
      await updatePassword(auth.currentUser, newPassword);

      setPasswordSuccess('Password updated successfully');
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (error) {
      console.error('Error updating password:', error);
      if (error.code === 'auth/wrong-password') {
        setPasswordError('Current password is incorrect');
      } else {
        setPasswordError('Failed to update password. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen theme-page-bg flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen theme-page-bg">
      <div className="max-w-2xl mx-auto px-4 py-8">
        <div className="flex items-center mb-6">
          <button
            onClick={() => navigate('/home')}
            className="mr-4 theme-text hover:opacity-80"
          >
            <ArrowLeft className="h-6 w-6" />
          </button>
          <h1 className="text-2xl font-bold theme-text">Manage Profile</h1>
        </div>

        {error && (
          <div className="mb-4 p-4 theme-container border rounded-lg">
            <p className="text-red-600 dark:text-red-400">{error}</p>
          </div>
        )}

        {success && (
          <div className="mb-4 p-4 theme-container border rounded-lg">
            <p className="text-green-600 dark:text-green-400">{success}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="theme-container rounded-lg shadow-sm p-6 space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium theme-label">First Name</label>
              <input
                type="text"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border px-3 py-2 theme-input focus:outline-none focus:ring-2"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium theme-label">Last Name</label>
              <input
                type="text"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border px-3 py-2 theme-input focus:outline-none focus:ring-2"
                required
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium theme-label">Email</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border px-3 py-2 theme-input focus:outline-none focus:ring-2 opacity-70"
              required
              disabled
            />
          </div>
          <div>
            <label className="block text-sm font-medium theme-label">Phone</label>
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border px-3 py-2 theme-input focus:outline-none focus:ring-2"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium theme-label">Position</label>
            <input
              type="text"
              name="position"
              value={formData.position}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border px-3 py-2 theme-input focus:outline-none focus:ring-2"
              required
            />
          </div>
          <button
            type="submit"
            className="w-full theme-button theme-button-primary rounded-md py-2 transition-colors disabled:opacity-50"
            disabled={loading}
          >
            {loading ? 'Updating...' : 'Update Profile'}
          </button>
        </form>
      </div>

      {/* Enterprise Information Section */}
      <div className="max-w-2xl mx-auto px-4 mt-8">
        {enterpriseLoading ? (
          <div className="theme-container rounded-lg shadow-sm p-6 flex justify-center">
            <div className="animate-pulse rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : enterprise ? (
          <EnterpriseInfo enterprise={enterprise} />
        ) : (
          <div className="theme-container rounded-lg shadow-sm p-6">
            <p className="theme-text opacity-70">No enterprise information available</p>
          </div>
        )}
      </div>

      {/* Add Password Update Form */}
      <div className="max-w-2xl mx-auto px-4 mt-8">
        <form onSubmit={handlePasswordChange} className="theme-container rounded-lg shadow-sm p-6 space-y-4">
          <h2 className="text-xl font-semibold theme-text mb-4">Update Password</h2>

          {passwordError && (
            <div className="p-4 theme-container border rounded-lg">
              <p className="text-red-600 dark:text-red-400">{passwordError}</p>
            </div>
          )}

          {passwordSuccess && (
            <div className="p-4 theme-container border rounded-lg">
              <p className="text-green-600 dark:text-green-400">{passwordSuccess}</p>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium theme-label">Current Password</label>
            <input
              type="password"
              value={passwordData.currentPassword}
              onChange={(e) => setPasswordData({
                ...passwordData,
                currentPassword: e.target.value
              })}
              className="mt-1 block w-full rounded-md border px-3 py-2 theme-input focus:outline-none focus:ring-2"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium theme-label">New Password</label>
            <input
              type="password"
              value={passwordData.newPassword}
              onChange={(e) => setPasswordData({
                ...passwordData,
                newPassword: e.target.value
              })}
              className="mt-1 block w-full rounded-md border px-3 py-2 theme-input focus:outline-none focus:ring-2"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium theme-label">Confirm New Password</label>
            <input
              type="password"
              value={passwordData.confirmPassword}
              onChange={(e) => setPasswordData({
                ...passwordData,
                confirmPassword: e.target.value
              })}
              className="mt-1 block w-full rounded-md border px-3 py-2 theme-input focus:outline-none focus:ring-2"
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full theme-button theme-button-primary rounded-md py-2 transition-colors disabled:opacity-50"
          >
            {loading ? 'Updating Password...' : 'Update Password'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default Profile;
