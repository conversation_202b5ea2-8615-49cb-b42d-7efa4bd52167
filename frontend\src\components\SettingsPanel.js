import React, { useState, useEffect } from 'react';
import { X, Mic, Video, Speaker, Monitor, Globe, Volume2, VolumeX, Check } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const SettingsPanel = ({ isOpen, onClose }) => {
  const { t, i18n } = useTranslation();
  const [activeTab, setActiveTab] = useState('audio');
  const [audioDevices, setAudioDevices] = useState([]);
  const [videoDevices, setVideoDevices] = useState([]);
  const [speakerDevices, setSpeakerDevices] = useState([]);
  const [selectedAudio, setSelectedAudio] = useState('');
  const [selectedVideo, setSelectedVideo] = useState('');
  const [selectedSpeaker, setSelectedSpeaker] = useState('');
  const [volume, setVolume] = useState(75);
  const [isMuted, setIsMuted] = useState(false);
  const [language, setLanguage] = useState(i18n.language || 'en');

  // Languages supported by the application
  const languages = [
    { code: 'en', name: 'English' },
    { code: 'fr', name: 'Français' },
    { code: 'ar', name: 'العربية' },
    { code: 'de', name: 'Deutsch' }
  ];

  // Fetch available devices when component mounts
  useEffect(() => {
    const getDevices = async () => {
      try {
        // Request permission to access media devices
        await navigator.mediaDevices.getUserMedia({ audio: true, video: true });

        // Get all media devices
        const devices = await navigator.mediaDevices.enumerateDevices();

        // Filter devices by type
        const audioInputs = devices.filter(device => device.kind === 'audioinput');
        const videoInputs = devices.filter(device => device.kind === 'videoinput');
        const audioOutputs = devices.filter(device => device.kind === 'audiooutput');

        setAudioDevices(audioInputs);
        setVideoDevices(videoInputs);
        setSpeakerDevices(audioOutputs);

        // Set default selected devices
        if (audioInputs.length > 0) setSelectedAudio(audioInputs[0].deviceId);
        if (videoInputs.length > 0) setSelectedVideo(videoInputs[0].deviceId);
        if (audioOutputs.length > 0) setSelectedSpeaker(audioOutputs[0].deviceId);

      } catch (error) {
        console.error('Error accessing media devices:', error);
      }
    };

    getDevices();
  }, []);

  // Handle language change
  const handleLanguageChange = (langCode) => {
    setLanguage(langCode);
    i18n.changeLanguage(langCode);
  };

  // Handle volume change
  const handleVolumeChange = (e) => {
    setVolume(parseInt(e.target.value));
  };

  // Toggle mute
  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  // We'll use CSS to show/hide the panel instead of conditional rendering

  return (
    <div
      className="settings-panel fixed right-0 top-0 h-full w-96 bg-gray-800 shadow-lg flex flex-col z-20 transition-all duration-300 ease-in-out"
      data-open={isOpen ? "true" : "false"}
    >
      {/* Settings Header */}
      <div className="settings-header p-4 border-b border-gray-700 flex justify-between items-center">
        <h3 className="text-white font-medium">{t('settings.title')}</h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white transition-colors"
          aria-label="Close settings"
        >
          <X size={20} />
        </button>
      </div>

      {/* Settings Tabs */}
      <div className="settings-tabs flex border-b border-gray-700">
        <button
          className={`flex-1 py-3 px-4 text-sm font-medium ${activeTab === 'audio' ? 'text-blue-500 border-b-2 border-blue-500' : 'text-gray-400 hover:text-white'}`}
          onClick={() => setActiveTab('audio')}
        >
          {t('settings.audio')}
        </button>
        <button
          className={`flex-1 py-3 px-4 text-sm font-medium ${activeTab === 'video' ? 'text-blue-500 border-b-2 border-blue-500' : 'text-gray-400 hover:text-white'}`}
          onClick={() => setActiveTab('video')}
        >
          {t('settings.video')}
        </button>
        <button
          className={`flex-1 py-3 px-4 text-sm font-medium ${activeTab === 'general' ? 'text-blue-500 border-b-2 border-blue-500' : 'text-gray-400 hover:text-white'}`}
          onClick={() => setActiveTab('general')}
        >
          {t('settings.general')}
        </button>
      </div>

      {/* Settings Content */}
      <div className="settings-content flex-1 overflow-y-auto p-4">
        {/* Audio Settings */}
        {activeTab === 'audio' && (
          <div className="space-y-6">
            {/* Microphone Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <div className="flex items-center">
                  <Mic size={16} className="mr-2" />
                  {t('settings.microphone')}
                </div>
              </label>
              <select
                value={selectedAudio}
                onChange={(e) => setSelectedAudio(e.target.value)}
                className="w-full bg-gray-700 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {audioDevices.map(device => (
                  <option key={device.deviceId} value={device.deviceId}>
                    {device.label || `Microphone ${audioDevices.indexOf(device) + 1}`}
                  </option>
                ))}
              </select>
            </div>

            {/* Speaker Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <div className="flex items-center">
                  <Speaker size={16} className="mr-2" />
                  {t('settings.speaker')}
                </div>
              </label>
              <select
                value={selectedSpeaker}
                onChange={(e) => setSelectedSpeaker(e.target.value)}
                className="w-full bg-gray-700 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {speakerDevices.map(device => (
                  <option key={device.deviceId} value={device.deviceId}>
                    {device.label || `Speaker ${speakerDevices.indexOf(device) + 1}`}
                  </option>
                ))}
              </select>
            </div>

            {/* Volume Control */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {isMuted ? <VolumeX size={16} className="mr-2" /> : <Volume2 size={16} className="mr-2" />}
                    {t('settings.volume')}
                  </div>
                  <button
                    onClick={toggleMute}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    {isMuted ? t('settings.unmute') : t('settings.mute')}
                  </button>
                </div>
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={volume}
                onChange={handleVolumeChange}
                disabled={isMuted}
                className="w-full"
              />
              <div className="text-right text-sm text-gray-400 mt-1">
                {isMuted ? t('settings.muted') : `${volume}%`}
              </div>
            </div>
          </div>
        )}

        {/* Video Settings */}
        {activeTab === 'video' && (
          <div className="space-y-6">
            {/* Camera Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <div className="flex items-center">
                  <Video size={16} className="mr-2" />
                  {t('settings.camera')}
                </div>
              </label>
              <select
                value={selectedVideo}
                onChange={(e) => setSelectedVideo(e.target.value)}
                className="w-full bg-gray-700 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {videoDevices.map(device => (
                  <option key={device.deviceId} value={device.deviceId}>
                    {device.label || `Camera ${videoDevices.indexOf(device) + 1}`}
                  </option>
                ))}
              </select>
            </div>

            {/* Video Preview */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('settings.preview')}
              </label>
              <div className="bg-gray-900 rounded aspect-video flex items-center justify-center">
                <video
                  id="video-preview"
                  className="w-full h-full rounded"
                  autoPlay
                  playsInline
                  muted
                ></video>
              </div>
            </div>
          </div>
        )}

        {/* General Settings */}
        {activeTab === 'general' && (
          <div className="space-y-6">
            {/* Language Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <div className="flex items-center">
                  <Globe size={16} className="mr-2" />
                  {t('settings.language')}
                </div>
              </label>
              <div className="grid grid-cols-2 gap-2">
                {languages.map(lang => (
                  <button
                    key={lang.code}
                    onClick={() => handleLanguageChange(lang.code)}
                    className={`flex items-center justify-between px-3 py-2 rounded ${
                      language === lang.code
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-700 text-white hover:bg-gray-600'
                    }`}
                  >
                    <span>{lang.name}</span>
                    {language === lang.code && <Check size={16} />}
                  </button>
                ))}
              </div>
            </div>

            {/* Screen Sharing */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <div className="flex items-center">
                  <Monitor size={16} className="mr-2" />
                  {t('settings.screen_sharing')}
                </div>
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="share-audio"
                  className="rounded text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="share-audio" className="text-sm text-gray-300">
                  {t('settings.share_audio')}
                </label>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Settings Footer */}
      <div className="settings-footer p-4 border-t border-gray-700">
        <button
          onClick={onClose}
          className="w-full bg-blue-600 text-white rounded py-2 hover:bg-blue-700 transition-colors"
        >
          {t('settings.save_and_close')}
        </button>
      </div>
    </div>
  );
};

export default SettingsPanel;
