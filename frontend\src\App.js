import React, { useEffect } from "react";
import { HashRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { useAuth } from "./contexts/AuthContext";
import { ThemeProvider } from "./contexts/ThemeContext";
import { LanguageProvider } from "./contexts/LanguageContext";
import Welcome from "./pages/Welcome";
import Home from "./pages/Home";
import Meet from "./pages/Meet";
import Files from "./pages/Files";
import Profile from "./pages/Profile";
import ManageEnterprise from "./pages/ManageEnterprise";
import Landing from "./pages/Landing";
import ThemeToggleWrapper from "./components/ThemeToggleWrapper";
import LanguageToggleWrapper from "./components/LanguageToggleWrapper";
import PageTransition from "./components/PageTransition";
import { ToastProvider } from "./components/ToastNotification";
import { getAuth } from "firebase/auth";
import './assets/styles/enhanced-animations.css';

function App() {
  const { user } = useAuth();

  useEffect(() => {
    // Remove any call to requestNotificationPermission
  }, [user]);

  return (
    <LanguageProvider>
      <ThemeProvider>
        <ToastProvider>
          <Router>
            <ThemeToggleWrapper />
            <LanguageToggleWrapper />
            <PageTransition>
              <Routes>
                <Route
                  path="/"
                  element={<Landing />}
                />
                <Route
                  path="/welcome"
                  element={user ? <Navigate to="/home" /> : <Welcome />}
                />
                <Route
                  path="/home"
                  element={user ? <Home /> : <Navigate to="/welcome" />}
                />
                <Route
                  path="/meet/:roomId"
                  element={user ? <Meet /> : <Navigate to="/welcome" />}
                />
                <Route
                  path="/files"
                  element={user ? <Files /> : <Navigate to="/welcome" />}
                />
                <Route
                  path="/profile"
                  element={user ? <Profile /> : <Navigate to="/welcome" />}
                />
                <Route
                  path="/manage-enterprise"
                  element={user ? <ManageEnterprise /> : <Navigate to="/welcome" />}
                />
              </Routes>
            </PageTransition>
          </Router>
        </ToastProvider>
      </ThemeProvider>
    </LanguageProvider>
  );
}

export default App;