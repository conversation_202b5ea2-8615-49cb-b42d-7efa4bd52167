const mongoose = require('mongoose');

const MeetingSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: false, // Make description optional
    default: 'No description provided'
  },
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  participants: [{
    type: String,
    ref: 'User',
  }],
  roomId: {
    type: String,
    required: true,
    unique: true // Ensure roomId is unique
  },
  createdBy: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['scheduled', 'in-progress', 'completed', 'cancelled'],
    default: 'in-progress'
  },
  enterpriseCode: {
    type: String,
    required: true,
    default: 'default'
  }
});

const Meeting = mongoose.model('Meeting', MeetingSchema);
module.exports = Meeting;