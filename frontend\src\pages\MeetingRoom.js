import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { meetingAPI } from '../services/mongodb';

const MeetingRoom = () => {
  const { roomId } = useParams();
  const navigate = useNavigate();
  const [meetingData, setMeetingData] = useState(null);
  const [error, setError] = useState('');

  useEffect(() => {
    const loadMeeting = async () => {
      try {
        const response = await meetingAPI.getMeetingById(roomId);

        if (!response) {
          setError('Meeting not found');
          return;
        }

        // Use accessStatus from backend
        if (!response.accessStatus?.isAccessible) {
          setError(response.accessStatus?.message || 'Access denied');
          return;
        }

        setMeetingData(response);
      } catch (error) {
        console.error('Error loading meeting:', error);
        setError(error.message || 'Failed to load meeting');
      }
    };

    loadMeeting();

    // Check access every 30 seconds
    const accessTimer = setInterval(async () => {
      try {
        const response = await meetingAPI.getMeetingById(roomId);
        if (!response?.accessStatus?.isAccessible) {
          navigate('/home');
        }
      } catch (error) {
        navigate('/home');
      }
    }, 30000);

    return () => clearInterval(accessTimer);
  }, [roomId, navigate]);

  if (error) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-md p-6 max-w-md w-full">
          <h2 className="text-2xl font-semibold text-red-600 mb-4">Access Denied</h2>
          <p className="text-gray-700">{error}</p>
          <button
            onClick={() => navigate('/home')}
            className="mt-4 w-full bg-blue-600 text-white rounded-md py-2 hover:bg-blue-700"
          >
            Return to Home
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <h1>Welcome to the Meeting Room</h1>
      {/* Add your meeting room content here */}
    </div>
  );
};

export default MeetingRoom;