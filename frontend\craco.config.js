const webpack = require('webpack');

module.exports = {
  webpack: {
    configure: {
      resolve: {
        fallback: {
          crypto: require.resolve('crypto-browserify'),
          stream: require.resolve('stream-browserify'),
          buffer: require.resolve('buffer/'),
          path: require.resolve('path-browserify'),
          process: require.resolve('process/browser.js'),
          fs: false,
          os: false,
          child_process: false
        }
      },
      plugins: [
        new webpack.ProvidePlugin({
          process: 'process/browser.js',
          Buffer: ['buffer', 'Buffer']
        })
      ],
      module: {
        rules: [
          {
            test: /\.onnx$/,
            type: 'asset/resource'
          }
        ]
      }
    }
  },
  devServer: {
    headers: {
      "Cross-Origin-Embedder-Policy": "require-corp",
      "Cross-Origin-Opener-Policy": "same-origin",
      "Cross-Origin-Resource-Policy": "cross-origin",
      "Content-Security-Policy": "default-src * 'unsafe-inline' 'unsafe-eval' data: blob:; connect-src * 'unsafe-inline' 'unsafe-eval' data: blob:; script-src * 'unsafe-inline' 'unsafe-eval' data: blob:; frame-src * data: blob:; img-src * data: blob:; style-src * 'unsafe-inline' data: blob:; font-src * data: blob:; media-src * data: blob:; worker-src * data: blob:; object-src 'none';",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
      "Access-Control-Allow-Headers": "X-Requested-With, content-type, Authorization, Range, Cache-Control",
      "Access-Control-Expose-Headers": "Content-Length, Content-Range"
    },
    proxy: {
      '/models': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        pathRewrite: { '^/models': '/models' },
        onProxyReq: (proxyReq) => {
          proxyReq.setHeader('Origin', 'http://localhost:8000');
        }
      }
    }
  }
};
