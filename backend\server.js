const dotenv = require('dotenv');
dotenv.config();

const express = require('express');
const helmet = require('helmet');
const mongoose = require('mongoose');
const cors = require('cors');
const admin = require('./firebaseConfig');
const axios = require('axios');

const app = express();

// Add this at the top of your server.js file, right after creating the Express app
app.use((req, res, next) => {
  // Remove any existing CSP headers
  res.removeHeader('Content-Security-Policy');
  res.removeHeader('Content-Security-Policy-Report-Only');

  // Set permissive headers for CORS
  const allowedOrigins = ['http://localhost:3000', 'https://my-frontend-r5ap.onrender.com'];
  const origin = req.headers.origin;

  if (allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  } else {
    // For development or unknown origins
    res.setHeader('Access-Control-Allow-Origin', '*');
  }

  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
  res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  next();
});

// Completely disable helmet's CSP
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'", "*.googleapis.com", "*.google.com", "*.firebaseio.com", "*.firebaseapp.com", "*.microsoft.com", "*.onrender.com"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "*.googleapis.com", "*.google.com", "apis.google.com", "*.firebaseio.com", "*.firebaseapp.com", "*.onrender.com", "data:", "blob:"],
      connectSrc: [
        "'self'",
        "*.googleapis.com",
        "*.google.com",
        "*.firebaseio.com",
        "*.firebaseapp.com",
        "securetoken.googleapis.com",
        "identitytoolkit.googleapis.com",
        "*.cloudfunctions.net",
        "*.speech.microsoft.com",
        "ws://localhost:*",
        "wss://localhost:*",
        "*.onrender.com",
        "https://my-backend-dwmk.onrender.com",
        "wss://my-backend-dwmk.onrender.com",
        "wss://my-backend-dwmk.onrender.com/socket.io/",
        "wss://my-backend-dwmk.onrender.com/socket.io/*",

        "https://my-frontend-r5ap.onrender.com",
        "wss://my-frontend-r5ap.onrender.com",
        "wss://my-frontend-r5ap.onrender.com/socket.io/",
        "wss://my-frontend-r5ap.onrender.com/socket.io/*",
        "blob:",
        "data:",
        "ws://my-backend-dwmk.onrender.com",
        "ws://my-backend-dwmk.onrender.com/socket.io/",
        "ws://my-backend-dwmk.onrender.com/socket.io/*",

      ],
      styleSrc: ["'self'", "'unsafe-inline'", "fonts.googleapis.com"],
      fontSrc: ["'self'", "fonts.gstatic.com", "data:"],
      imgSrc: ["'self'", "blob:", "data:", "*.googleapis.com", "*.gstatic.com", "localhost:*", "*.onrender.com"],
      frameSrc: ["'self'", "*.firebaseapp.com", "*.google.com", "accounts.google.com", "*.onrender.com"],
      mediaSrc: ["'self'", "blob:", "data:"],
      workerSrc: ["'self'", "blob:", "data:"],
      objectSrc: ["'none'"]
    }
  },
  crossOriginEmbedderPolicy: false,
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// Add or update CORS configuration with fully permissive settings for WebSockets
app.use(cors({
  origin: '*', // Allow all origins for now to troubleshoot the WebSocket issue
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin', 'Connection', 'Upgrade', 'Sec-WebSocket-Key', 'Sec-WebSocket-Version', 'Sec-WebSocket-Extensions'],
  exposedHeaders: ['Content-Length', 'X-Requested-With', 'Access-Control-Allow-Origin', 'Origin', 'Authorization']
}));

app.use(express.json());

// Add more detailed error logging
app.use((req, res, next) => {
  console.log('\n--- Incoming Request ---');
  console.log('Method:', req.method);
  console.log('URL:', req.originalUrl);
  console.log('Body:', req.body);
  console.log('Params:', req.params);
  console.log('Query:', req.query);
  console.log('Headers:', req.headers);
  console.log('----------------------\n');
  next();
});

// Detailed request logging middleware
app.use((req, res, next) => {
  console.log('\n--- Incoming Request ---');
  console.log('Method:', req.method);
  console.log('URL:', req.url);
  console.log('Body:', req.body);
  console.log('Headers:', req.headers);
  console.log('----------------------\n');
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// CORS test endpoint
app.get('/cors-test', (req, res) => {
  res.status(200).json({
    status: 'ok',
    message: 'CORS is working correctly',
    origin: req.headers.origin || 'No origin header',
    headers: req.headers
  });
});

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${req.method} ${req.url}`, {
    body: req.body,
    headers: req.headers
  });
  next();
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({
    message: 'Server error',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Connect to MongoDB with retry logic
const connectDB = async () => {
  let retries = 5;
  while (retries > 0) {
    try {
      await mongoose.connect(process.env.MONGO_URI);
      console.log('MongoDB connected successfully');
      return;
    } catch (err) {
      console.error('MongoDB connection error:', err);
      retries -= 1;
      if (retries === 0) throw err;
      await new Promise(resolve => setTimeout(resolve, 5000));
      console.log('Retrying connection...');
    }
  }
};

connectDB().catch(err => {
  console.error('Failed to connect to MongoDB:', err);
  process.exit(1);
});

// Define routes with explicit logging
const userRoutes = require('./Routes/userRoutes');
app.use('/api/users', userRoutes);
console.log('Registering enterprise routes at /api/enterprises');
app.use('/api/enterprises', require('./Routes/enterpriseRoutes'));
console.log('Registering meeting routes at /api/meetings');
app.use('/api/meetings', require('./Routes/meetingRoutes'));
console.log('Registering meeting file routes at /api/meeting-files');
app.use('/api/meeting-files', require('./Routes/meetingFileRoutes'));

// Add route debugging
app.use((req, res, next) => {
  console.log(`${req.method} ${req.originalUrl}`, {
    body: req.body,
    query: req.query,
    params: req.params
  });
  next();
});

// Add API health check route
app.get('/api/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Add Socket.io test route
app.get('/api/socket-test', (req, res) => {
  res.status(200).json({
    status: 'ok',
    message: 'Socket.io server is running',
    socketEnabled: true,
    serverTime: new Date().toISOString(),
    cors: {
      origin: '*',
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      headers: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With', 'Connection', 'Upgrade']
    }
  });
});

// Add proxy route for model files
app.get('/models/:provider/:model/onnx/:file', async (req, res) => {
  const { provider, model, file } = req.params;
  const modelUrl = `https://huggingface.co/${provider}/${model}/resolve/main/onnx/${file}`;

  try {
    console.log('Fetching model file:', modelUrl);
    const response = await fetch(modelUrl);

    if (!response.ok) {
      console.error('Model fetch error:', response.status, response.statusText);
      throw new Error(`Failed to fetch model: ${response.statusText}`);
    }

    // Set appropriate headers
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Cache-Control', 'public, max-age=31536000');

    // Pipe the model file to response
    response.body.pipe(res);
  } catch (error) {
    console.error('Error proxying model file:', error);
    res.status(500).json({
      error: 'Error loading model file',
      details: error.message,
      url: modelUrl
    });
  }
});

// Add detailed error handling for undefined routes
app.use((req, res) => {
  console.log('404 error for path:', req.path);
  res.status(404).json({
    status: 'error',
    message: 'Not Found',
    path: req.path,
    method: req.method
  });
});

// Add Cohere proxy endpoint
app.post('/api/cohere/chat', async (req, res) => {
  try {
    const response = await axios.post('https://api.cohere.ai/v1/chat', req.body, {
      headers: {
        'Authorization': `Bearer ${process.env.COHERE_API_KEY}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    res.json(response.data);
  } catch (error) {
    console.error('Cohere API error:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: 'Cohere API error',
      details: error.response?.data || error.message
    });
  }
});

const PORT = process.env.PORT || 8000;
const server = require('http').createServer(app);

// Add middleware to handle WebSocket upgrade requests
server.on('upgrade', (request, socket, head) => {
  console.log('WebSocket upgrade request received');

  // Extract origin from headers
  const origin = request.headers.origin;
  console.log('WebSocket upgrade request from origin:', origin);

  // Allow the upgrade to proceed
  // The actual WebSocket handling will be done by Socket.io
});

// Initialize Socket.io
const { initializeSocket } = require('./socket');
const io = initializeSocket(server);

server.listen(PORT, '0.0.0.0', () => console.log(`Server running on port ${PORT}`));

