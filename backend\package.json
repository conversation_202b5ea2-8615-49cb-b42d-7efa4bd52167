{"name": "backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.9.0", "cohere-ai": "^7.17.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "firebase-admin": "^12.7.0", "helmet": "^8.1.0", "mongoose": "^8.12.0", "nodemailer": "^6.10.1", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.0"}}