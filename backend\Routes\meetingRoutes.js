const express = require('express');
const Meeting = require('../Models/Meeting');
const Enterprise = require('../Models/Enterprise');
const EnterpriseEvent = require('../Models/EnterpriseEvent');
const User = require('../Models/user');
const admin = require('../firebaseConfig');
const nodemailer = require('nodemailer');
const { generateSummary } = require('../services/cohereService');
    const axios = require('axios'); // Add at top of file

const router = express.Router();

// Admin check middleware
const isAdmin = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) {
      return res.status(401).json({ message: 'No token provided' });
    }

    const decodedToken = await admin.auth().verifyIdToken(token);
    
    // Check if user is admin of ANY enterprise
    const adminEnterprise = await Enterprise.findOne({ adminEmail: decodedToken.email });
    
    if (!adminEnterprise) {
      return res.status(403).json({ message: 'Not authorized as admin' });
    }
    
    // Grant admin access
    req.enterprise = adminEnterprise;
    req.decodedToken = decodedToken;
    next();
  } catch (error) {
    console.error('Admin check error:', error);
    res.status(401).json({ message: 'Invalid token' });
  }
};

const canManageEvent = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) {
      return res.status(401).json({ message: 'No token provided' });
    }

    const decodedToken = await admin.auth().verifyIdToken(token);
    console.log('Checking permissions for:', decodedToken.email);

    // First check if user is an admin of ANY enterprise
    const adminEnterprise = await Enterprise.findOne({ adminEmail: decodedToken.email });

    // If user is an admin, grant full access without further checks
    if (adminEnterprise) {
      const event = await EnterpriseEvent.findById(req.params.id);
      if (!event) {
        return res.status(404).json({ message: 'Event not found' });
      }
      req.isAdmin = true;
      req.event = event;
      req.decodedToken = decodedToken;
      req.adminEnterprise = adminEnterprise;
      return next();
    }

    // If not admin, then check if user is the creator
    const event = await EnterpriseEvent.findById(req.params.id);
    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }

    // Only allow if the user is the creator
    if (event.createdBy === decodedToken.uid) {
      req.event = event;
      req.decodedToken = decodedToken;
      return next();
    }

    // Deny access for participants and others
    return res.status(403).json({
      message: 'Not authorized to manage this event. Only the creator or an admin can update or delete.',
      isAdmin: false,
      userEmail: decodedToken.email
    });

  } catch (error) {
    console.error('Authorization error:', error);
    res.status(401).json({ message: 'Authorization error', error: error.message });
  }
};

// Create meeting (admin only)
router.post('/', isAdmin, async (req, res) => {
  try {
    console.log('Creating meeting with data:', req.body);
    const newMeeting = new Meeting(req.body);
    const savedMeeting = await newMeeting.save();
    console.log('Meeting created:', savedMeeting);
    res.status(201).json(savedMeeting);
  } catch (err) {
    console.error('Error creating meeting:', err);
    res.status(500).json({ 
      message: 'Error creating meeting', 
      error: err.message,
      details: err.errors 
    });
  }
});

// Notify users (admin only)
router.post('/notify', isAdmin, async (req, res) => {
  try {
    const { title, body, enterpriseCode } = req.body;

    // Get all users from this enterprise
    const users = await admin.auth().listUsers();
    const enterpriseUsers = users.users.filter(user => 
      user.customClaims?.enterpriseCode === enterpriseCode
    );

    // Send notifications using Firebase Cloud Messaging
    const messages = enterpriseUsers.map(user => ({
      notification: { title, body },
      token: user.fcmToken // Assuming FCM tokens are stored
    }));

    await admin.messaging().sendAll(messages);
    
    res.json({ message: 'Notifications sent successfully' });
  } catch (error) {
    console.error('Error sending notifications:', error);
    res.status(500).json({ message: 'Error sending notifications' });
  }
});

// Get meeting or enterprise event by roomId
router.get('/room/:roomId', async (req, res) => {
  try {
    console.log('Finding meeting with roomId:', req.params.roomId);
    
    const [meeting, enterpriseEvent] = await Promise.all([
      Meeting.findOne({ roomId: req.params.roomId }),
      EnterpriseEvent.findOne({ roomId: req.params.roomId })
    ]);

    const event = meeting || enterpriseEvent;
    if (!event) {
      return res.status(404).json({ message: 'Meeting/Event not found' });
    }

    const now = new Date();
    const startTime = new Date(event.startTime || event.date);
    const endTime = new Date(event.endTime || startTime.getTime() + 3600000);

    // Reset time components for date comparison
    const todayDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const meetingDate = new Date(startTime.getFullYear(), startTime.getMonth(), startTime.getDate());

    const accessStatus = {
      isAccessible: false,
      message: '',
      meetingDate: startTime.toLocaleDateString(),
      startTime: startTime.toLocaleTimeString(),
      endTime: endTime.toLocaleTimeString()
    };

    // Strict date comparison using timestamps
    if (todayDate.getTime() !== meetingDate.getTime()) {
      accessStatus.message = `Meeting is scheduled for ${startTime.toLocaleDateString()}`;
      return res.status(403).json({ ...event.toObject(), accessStatus });
    }

    // Only allow access within 15 minutes before the meeting start
    const accessStartTime = new Date(startTime);
    accessStartTime.setMinutes(startTime.getMinutes() - 15); // 15 min before

    if (now < accessStartTime) {
      accessStatus.message = `Meeting will be accessible from ${accessStartTime.toLocaleTimeString()} (15 minutes before start)`;
      return res.status(403).json({ ...event.toObject(), accessStatus });
    }

    if (now > endTime) {
      accessStatus.message = 'Meeting has ended';
      return res.status(403).json({ ...event.toObject(), accessStatus });
    }

    // Access allowed only if same day and within 15 min before start and until end
    accessStatus.isAccessible = true;
    accessStatus.message = 'Meeting is accessible';

    res.json({ ...event.toObject(), accessStatus });
  } catch (err) {
    console.error('Error getting meeting:', err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

// Get enterprise event by roomId
router.get('/enterprise-event/:roomId', async (req, res) => {
  try {
    const event = await EnterpriseEvent.findOne({ roomId: req.params.roomId });
    if (!event) {
      return res.status(404).json({ message: 'Enterprise event not found' });
    }
    res.json(event);
  } catch (err) {
    console.error('Error getting enterprise event:', err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

// Add this new route before the other routes
router.get('/event/:id', async (req, res) => {
  try {
    const event = await EnterpriseEvent.findById(req.params.id);
    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }
    res.json(event);
  } catch (error) {
    console.error('Error getting event:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get meetings for a user
router.get('/user/:userId', async (req, res) => {
  try {
    const meetings = await Meeting.find({ participants: req.params.userId });
    res.json(meetings);
  } catch (err) {
    console.error('Error getting user meetings:', err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

// Get meetings for a user including enterprise events
router.get('/enterprise/:enterpriseCode/user/:userId', async (req, res) => {
  try {
    const { enterpriseCode } = req.params;

    // Return all events for the enterprise that are not cancelled
    const events = await EnterpriseEvent.find({
      enterpriseCode,
      status: { $ne: 'cancelled' }
    }).sort({ createdAt: -1 });

    const mappedEvents = events.map(event => ({
      _id: event._id,
      title: event.title,
      startTime: event.startTime,
      endTime: event.endTime,
      description: event.description,
      roomId: event.roomId,
      participants: event.participants || [],
      isAccessible: true,
      enterpriseCode: event.enterpriseCode,
      createdBy: event.createdBy,
      status: event.status
    }));

    res.json(mappedEvents);
  } catch (error) {
    console.error('Error fetching events:', error);
    res.status(500).json({ message: 'Error fetching events', error: error.message });
  }
});

// Create enterprise event endpoint
router.post('/enterpriseevents', async (req, res) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) {
      return res.status(401).json({ message: 'No token provided' });
    }

    const decodedToken = await admin.auth().verifyIdToken(token);
    const eventData = {
      ...req.body,
      createdBy: decodedToken.uid,
      type: 'enterprise_event',
      status: 'scheduled'
    };
    
    const newEvent = new EnterpriseEvent(eventData);
    const savedEvent = await newEvent.save();
    res.status(201).json(savedEvent);
  } catch (error) {
    console.error('Error creating enterprise event:', error);
    res.status(500).json({ message: 'Error creating event', error: error.message });
  }
});

// Update calendar event - allow both admin and creator
router.put('/calendar/:id', canManageEvent, async (req, res) => {
  try {
    const updatedEvent = await EnterpriseEvent.findByIdAndUpdate(
      req.params.id,
      {
        ...req.body,
        // Preserve original enterprise code and creator if not admin
        enterpriseCode: req.event.enterpriseCode,
        createdBy: req.event.createdBy,
        updatedAt: new Date(),
        updatedBy: req.decodedToken.uid
      },
      { new: true }
    );

    res.json(updatedEvent);
  } catch (error) {
    console.error('Error updating event:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Delete calendar event - allow both admin and creator
router.delete('/calendar/:id', canManageEvent, async (req, res) => {
  try {
    // Instead of deleting, mark as cancelled to keep history
    const updatedEvent = await EnterpriseEvent.findByIdAndUpdate(
      req.params.id,
      {
        status: 'cancelled',
        updatedAt: new Date(),
        updatedBy: req.decodedToken.uid
      },
      { new: true }
    );

    if (!updatedEvent) {
      return res.status(404).json({ message: 'Event not found' });
    }

    res.json({ message: 'Event cancelled successfully', event: updatedEvent });
  } catch (error) {
    console.error('Error cancelling event:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Update participation status
router.post('/participation/:eventId', async (req, res) => {
  try {
    const { eventId } = req.params;
    const { userId, status, responseTime } = req.body;

    const event = await EnterpriseEvent.findById(eventId);
    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }

    const participantIndex = event.participants.findIndex(p => p.userId === userId);
    if (participantIndex === -1) {
      event.participants.push({ userId, status, responseTime });
    } else {
      event.participants[participantIndex] = { userId, status, responseTime };
    }

    await event.save();
    res.json(event);
  } catch (error) {
    console.error('Error updating participation:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get all enterprise events for a specific enterprise
router.get('/enterprise-events/:enterpriseCode', async (req, res) => {
  try {
    const events = await EnterpriseEvent.find({ 
      enterpriseCode: req.params.enterpriseCode,
      status: { $ne: 'cancelled' }
    }).sort({ startTime: 1 });
    
    res.json(events);
  } catch (err) {
    res.status(500).json({ message: 'Error fetching enterprise events', error: err.message });
  }
});

// Configure Nodemailer transporter (replace with your SMTP credentials)
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL_USER, // Set in your .env file
    pass: process.env.EMAIL_PASS  // Set in your .env file
  }
});

// Utility to send email
async function sendEmail(to, subject, text) {
  const mailOptions = {
    from: '<EMAIL>', // Sender address (must match the user above)
    to, // Recipient address (can be any participant)
    subject,
    text
  };
  try {
    await transporter.sendMail(mailOptions);
    console.log('Email sent to', to);
  } catch (err) {
    console.error('Email send error:', err);
  }
}

// Endpoint to send email reminders to all participants 24 hours before event start
router.post('/send-email-reminders', async (req, res) => {
  try {
    const now = new Date();
    const in24h = new Date(now.getTime() + 24 * 60 * 60 * 1000);

    // Find events starting between now and 24 hours from now (not cancelled)
    const upcomingEvents = await EnterpriseEvent.find({
      startTime: { $gte: now, $lte: in24h },
      status: { $ne: 'cancelled' }
    });

    let emailsSent = 0;

    for (const event of upcomingEvents) {
      // Collect all participant user IDs (from event.participants array)
      const participantIds = (event.participants || [])
        .map(p => (typeof p === 'string' ? p : p.userId))
        .filter(Boolean);

      // Optionally add the creator if not already in participants
      if (event.createdBy && !participantIds.includes(event.createdBy)) {
        participantIds.push(event.createdBy);
      }

      // Get unique user IDs
      const uniqueUserIds = [...new Set(participantIds)];

      // Fetch user emails from your User model
      const users = await User.find({ firebaseUid: { $in: uniqueUserIds }, email: { $exists: true, $ne: null } });

      for (const user of users) {
        if (user.email) {
          await sendEmail(
            user.email,
            `Reminder: Upcoming Event "${event.title}" in 24 hours`,
            `Dear ${user.firstName || ''},\n\nYou are a participant in an event scheduled to start within the next 24 hours:\n\nTitle: ${event.title}\nDate: ${new Date(event.startTime).toLocaleDateString()}\nTime: ${new Date(event.startTime).toLocaleTimeString()}\nDescription: ${event.description || ''}\n\nBest regards,\nYour Team`
          );
          emailsSent++;
        }
      }
    }

    res.json({ message: `Email reminders sent: ${emailsSent}` });
  } catch (error) {
    console.error('Error sending email reminders:', error);
    res.status(500).json({ message: 'Error sending email reminders', error: error.message });
  }
});

// Add Cohere chat endpoint
router.post('/cohere/summarize', async (req, res) => {
  try {
    console.log('Received summarization request');
    const { transcript, model } = req.body;

    if (!transcript) {
      return res.status(400).json({ error: 'Transcript is required' });
    }

    console.log('Sending request to Cohere API');
    const cohereResponse = await axios.post('https://api.cohere.ai/v1/chat', {
      message: `Please summarize this meeting transcript in bullet points:\n${transcript}`,
      model: model || 'command',
      temperature: 0.3,
      chat_history: [],
      prompt_truncation: 'AUTO',
      stream: false,
      citation_quality: 'accurate'
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.COHERE_API_KEY}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    console.log('Received response from Cohere');
    res.json({ summary: cohereResponse.data.text });
  } catch (error) {
    console.error('Summarization error:', error.response?.data || error.message);
    res.status(500).json({
      error: 'Summarization failed',
      details: error.response?.data || error.message
    });
  }
});

module.exports = router;