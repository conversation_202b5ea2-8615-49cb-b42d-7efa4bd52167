import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Calendar as BigCalenda<PERSON>, momentLocalizer } from 'react-big-calendar';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import moment from 'moment';
import { useNavigate } from 'react-router-dom';
import { Video, User as UserIcon, Mic, AlertCircle, FileText, X, Briefcase, Plus, UserPlus, Bell, Users, UserX, Check, LogOut } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import UnifiedVoiceService from '../services/unifiedVoiceService';
import DownloadService from '../services/downloadService';
import { auth } from '../services/firebase';
import { meetingAPI, enterpriseAPI, userAPI, meetingFilesAPI } from '../services/mongodb';
import '../assets/styles/modern-background.css';
import '../assets/styles/arabic-calendar.css'; // Import Arabic calendar styles
import NotificationDropdown from '../components/NotificationDropdown';
import EnterpriseBanner from '../components/EnterpriseBanner';
import AnimatedButton from '../components/AnimatedButton';
import LoadingSpinner from '../components/LoadingSpinner';
import { useToast } from '../components/ToastNotification';

const Calendar = React.forwardRef(({ events, onSelectEvent, eventPropGetter, messages }, ref) => {
  const { t, i18n } = useTranslation();
  const { darkMode } = useTheme();

  const localizer = React.useMemo(() => {
    // Set moment locale based on current language
    const currentLang = i18n.language || 'en';
    const localeMap = {
      'en': 'en-GB',
      'fr': 'fr',
      'ar': 'ar',
      'de': 'de'
    };
    const locale = localeMap[currentLang] || 'en-GB';
    moment.locale(locale);
    return momentLocalizer(moment);
  }, [i18n.language]);

  // Create dynamic formats based on current locale
  const formats = React.useMemo(() => {
    // Custom formatter for month names
    const getMonthName = (date) => {
      const monthIndex = date.getMonth();
      const months = [
        'january', 'february', 'march', 'april', 'may', 'june',
        'july', 'august', 'september', 'october', 'november', 'december'
      ];

      // For Arabic, use the hardcoded month names to ensure consistency
      if (i18n.language === 'ar') {
        const arabicMonthNames = [
          'جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي', 'جوان',
          'جويلية', 'أوت', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];

        // Debug log for Arabic month names
        console.log(`Month ${monthIndex + 1} (${months[monthIndex]}) using: ${arabicMonthNames[monthIndex]}`);

        return arabicMonthNames[monthIndex];
      }

      // For other languages, use the translated month name from the i18n system
      const translatedMonth = t(`calendar.months.${months[monthIndex]}`);
      return translatedMonth;
    };

    // Custom formatter for day names
    const getDayName = (date, abbreviated = false) => {
      const dayIndex = date.getDay();
      const fullDays = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      const shortDays = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];

      // For Arabic, always use full day names
      if (i18n.language === 'ar' || !abbreviated) {
        return t(`calendar.days.${fullDays[dayIndex]}`);
      } else {
        return t(`calendar.days.${shortDays[dayIndex]}`);
      }
    };

    return {
      // Basic formats
      dateFormat: 'DD',
      dayFormat: (date) => {
        // For day cells in month view
        return `${getDayName(date, true)} ${date.getDate()}`;
      },

      // Week view formats
      weekdayFormat: (date) => {
        // For day headers in week view
        return getDayName(date, false);
      },

      // Time slot formats in week/day view
      timeGutterFormat: (date) => {
        // Format the time slots on the left side of week/day view
        const hours = date.getHours();
        const minutes = date.getMinutes();
        const ampm = hours >= 12 ? t('pm') : t('am');
        const hour12 = hours % 12 || 12;
        return `${hour12}:${minutes.toString().padStart(2, '0')} ${ampm}`;
      },

      // Header formats
      monthHeaderFormat: (date) => `${getMonthName(date)} ${date.getFullYear()}`,

      dayHeaderFormat: (date) => {
        // For day view header
        return `${getDayName(date, false)} ${getMonthName(date)} ${date.getDate()}, ${date.getFullYear()}`;
      },

      dayRangeHeaderFormat: ({ start, end }) => {
        // For week view header
        const startMonth = getMonthName(start);
        const endMonth = getMonthName(end);

        if (startMonth === endMonth) {
          return `${startMonth} ${start.getDate()} - ${end.getDate()}, ${end.getFullYear()}`;
        } else {
          return `${startMonth} ${start.getDate()} - ${endMonth} ${end.getDate()}, ${end.getFullYear()}`;
        }
      }
    };
  }, [t, i18n.language]);

  // Custom components for the calendar
  const components = React.useMemo(() => {
    return {
      event: ({ event }) => (
        <div className="event-content">
          <div className="event-title">{event.title}</div>
        </div>
      ),
      month: {
        header: ({ date }) => {
          // Use the getDayName function from formats
          const dayIndex = date.getDay();
          const fullDays = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
          const shortDays = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];

          // For Arabic, always use full day names
          if (i18n.language === 'ar') {
            return t(`calendar.days.${fullDays[dayIndex]}`);
          } else {
            return t(`calendar.days.${shortDays[dayIndex]}`);
          }
        }
      },
      week: {
        header: ({ date }) => {
          // Custom header for week view
          const dayIndex = date.getDay();
          const fullDays = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
          return t(`calendar.days.${fullDays[dayIndex]}`);
        }
      },
      timeGutterHeader: () => {
        // Custom header for the time gutter
        return i18n.language === 'ar' ? t('time') : '';
      }
    };
  }, [t, i18n.language]);

  // Use a simple style object without CSS selectors
  const calendarStyle = React.useMemo(() => {
    return { height: '100%' };
  }, []);

  // Add a class for Arabic calendar to apply additional CSS
  const calendarClass = i18n.language === 'ar' ? 'arabic-calendar' : '';

  // Force re-render when language changes by using a key
  const calendarKey = `calendar-${i18n.language}`;

  return (
    <div className={`h-[600px] w-full ${calendarClass}`} ref={ref}>
      <BigCalendar
        key={calendarKey} // Force re-render when language changes
        localizer={localizer}
        events={events}
        startAccessor="start"
        endAccessor="end"
        titleAccessor="title"
        defaultView="month"
        views={['month', 'week', 'day']}
        onSelectEvent={onSelectEvent}
        eventPropGetter={eventPropGetter}
        formats={formats}
        messages={messages} // Use messages passed from parent
        popup
        style={calendarStyle}
        selectable={false}
        resizable={false}
        defaultDate={new Date()}
        tooltipAccessor={event => event.title}
        components={components}
        className={calendarClass} // Apply the Arabic calendar class
        rtl={i18n.language === 'ar'} // Enable RTL for Arabic
      />
    </div>
  );
});

const Home = () => {
  const navigate = useNavigate();
  const { signOut, user } = useAuth();
  const { t, i18n } = useTranslation();
  const { darkMode } = useTheme();
  const { success: showSuccess, error: showError, info: showInfo } = useToast();
  const [userDisplayName, setUserDisplayName] = useState('');
  const [events, setEvents] = useState([]);
  const [isListening, setIsListening] = useState(false);
  // const [showProfileMenu, setShowProfileMenu] = useState(false); // Unused, commented out
  const profileMenuRef = useRef(null);
  const notificationRef = useRef(null);
  const [unifiedVoiceService, setUnifiedVoiceService] = useState(null);
  const [voiceResponse, setVoiceResponse] = useState('');
  const [voiceError, setVoiceError] = useState('');
  const [showJoinModal, setShowJoinModal] = useState(false);
  const [meetCode, setMeetCode] = useState('');
  const [joinError, setJoinError] = useState('');
  const [loadingProgress, setLoadingProgress] = useState(0); // Used for voice assistant loading progress
  const [isModelLoading, setIsModelLoading] = useState(true);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [enterprise, setEnterprise] = useState(null);
  const [enterpriseCode, setEnterpriseCode] = useState(null);
  const [showEventModal, setShowEventModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [eventFormData, setEventFormData] = useState({
    title: '',
    start: new Date(),
    end: new Date(),
    description: '',
    participants: [],
    roomId: '',
    isAccessible: false,
    accessMessage: ''
  });
  const [error, setError] = useState(null);
  // Success message state for notifications
  const [success, setSuccess] = useState(null);
  const [pendingUsers, setPendingUsers] = useState([]);
  const [showPendingUsersModal, setShowPendingUsersModal] = useState(false);
  const [availableParticipants, setAvailableParticipants] = useState([]);

  const [meetingFiles, setMeetingFiles] = useState([]);
  const [liveTranscript, setLiveTranscript] = useState('');
  const [notifications, setNotifications] = useState([]);
  const [showNotifications, setShowNotifications] = useState(false);
  const [creatorDisplayNames, setCreatorDisplayNames] = useState({});
  const [downloadService] = useState(() => new DownloadService());

  // Translate calendar labels
  const [messages, setMessages] = useState({
    month: t('calendar.month'),
    week: t('calendar.week'),
    day: t('calendar.day'),
    today: t('calendar.today'),
    previous: t('calendar.previous'),
    next: t('calendar.next'),
    noEventsInRange: t('calendar.noEventsInRange'),
    allDay: t('calendar.allDay'),
    date: t('calendar.date'),
    time: t('calendar.time'),
    event: t('calendar.event'),
    work_week: t('calendar.work_week'),
    agenda: t('calendar.agenda'),
    showMore: () => t('calendar.showMore')
  });

  // Update messages when language changes
  useEffect(() => {
    setMessages({
      month: t('calendar.month'),
      week: t('calendar.week'),
      day: t('calendar.day'),
      today: t('calendar.today'),
      previous: t('calendar.previous'),
      next: t('calendar.next'),
      noEventsInRange: t('calendar.noEventsInRange'),
      allDay: t('calendar.allDay'),
      date: t('calendar.date'),
      time: t('calendar.time'),
      event: t('calendar.event'),
      work_week: t('calendar.work_week'),
      agenda: t('calendar.agenda'),
      showMore: () => t('calendar.showMore')
    });

    // Debug log for Arabic month names when language changes
    if (i18n.language === 'ar') {
      console.log('Arabic month names:');
      const months = [
        'january', 'february', 'march', 'april', 'may', 'june',
        'july', 'august', 'september', 'october', 'november', 'december'
      ];

      // Force update Arabic month names to match the required format
      if (i18n.language === 'ar') {
        const arabicMonthNames = [
          'جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي', 'جوان',
          'جويلية', 'أوت', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];

        // Override the translations in memory for immediate effect
        months.forEach((month, index) => {
          i18n.addResource('ar', 'translation', `calendar.months.${month}`, arabicMonthNames[index]);
        });
      }

      // Log the updated month names
      months.forEach((month, index) => {
        console.log(`${index + 1}. ${month}: ${t(`calendar.months.${month}`)}`);
      });
    }
  }, [t, i18n.language]);

  const refreshEvents = useCallback(async () => {
    if (!user) return;
    try {
      setLoading(true);
      console.log('Fetching meetings for user:', user.uid);

      // Check if user exists in MongoDB first
      try {
        // Try to get user data first to ensure the user exists in MongoDB
        await userAPI.getUserById(user.uid);
      } catch (userError) {
        console.error('User not found in MongoDB, redirecting to Welcome page:', userError);
        // If user doesn't exist in MongoDB, sign out and redirect to Welcome page
        await auth.signOut();
        navigate('/welcome');
        return;
      }

      // Get meetings for the user's enterprise
      const meetings = await meetingAPI.getUserMeetings(user.uid);

      console.log('Raw meetings data:', meetings);

      if (!Array.isArray(meetings)) {
        console.error('Meetings data is not an array:', meetings);
        setEvents([]);
        return;
      }

      const now = new Date();

      const calendarEvents = meetings
        .filter(meeting => meeting && meeting.startTime)
        // Filter out events that have ended
        .filter(meeting => {
          const endTime = meeting.endTime ? new Date(meeting.endTime) : (meeting.startTime ? new Date(meeting.startTime) : new Date());
          return endTime > now;
        })
        .map(meeting => {
          console.log('Processing meeting:', meeting);

          // Use the actual scheduled start and end times
          const startTime = meeting.startTime ? new Date(meeting.startTime) : (meeting.date ? new Date(meeting.date) : new Date());
          const endTime = meeting.endTime ? new Date(meeting.endTime) : moment(startTime).add(1, 'hour').toDate();

          return {
            id: meeting._id,
            title: meeting.title || 'Untitled Meeting',
            start: startTime,
            end: endTime,
            allDay: false,
            resource: {
              ...meeting,
              isAccessible: true,
              participants: meeting.participants || []
            }
          };
        });

      console.log('Final calendar events:', calendarEvents);
      setEvents(calendarEvents);
    } catch (error) {
      console.error('Error refreshing events:', error);
      setEvents([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  }, [user]);

  const fetchMeetingFiles = useCallback(async () => {
    if (!user) return;
    try {
      const files = await meetingFilesAPI.getMeetingFilesForUser(user.uid);
      setMeetingFiles(files || []);
    } catch (err) {
      setMeetingFiles([]);
    }
  }, [user]);

  useEffect(() => {
    console.log('Events state updated:', events);
  }, [events]);

  const checkAdminStatus = useCallback(async () => {
    if (!user?.email) return;

    try {
      const enterpriseData = await enterpriseAPI.getEnterpriseByAdminEmail(user.email);
      setIsAdmin(!!enterpriseData);
      if (enterpriseData) {
        setEnterprise(enterpriseData);
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);
    }
  }, [user?.email]);

  useEffect(() => {
    let mounted = true;

    const initApp = async () => {
      if (!user) return;

      try {
        // Check if user is an admin and get enterprise data
        await checkAdminStatus();
        if (!mounted) return;

        await refreshEvents();
        if (!mounted) return;

        try {
          // Get user data to display name and get enterprise code for non-admins
          const userData = await userAPI.getUserById(user.uid);
          if (!mounted) return;

          setUserDisplayName(userData.firstName && userData.lastName
            ? `${userData.firstName} ${userData.lastName}`
            : user.email);

          // Set enterprise code and fetch enterprise data for non-admin users
          if (!isAdmin && userData.enterpriseCode) {
            setEnterpriseCode(userData.enterpriseCode);

            // Fetch enterprise data for regular users
            try {
              console.log('Fetching enterprise data for regular user with code:', userData.enterpriseCode);
              const enterpriseData = await enterpriseAPI.getEnterpriseByCode(userData.enterpriseCode);
              console.log('Fetched enterprise data for regular user:', enterpriseData);
              if (enterpriseData) {
                setEnterprise(enterpriseData);
              }
            } catch (enterpriseError) {
              console.error('Error fetching enterprise data for regular user:', enterpriseError);
            }
          }
        } catch (userError) {
          console.error('User not found in MongoDB, redirecting to Welcome page:', userError);
          // If user doesn't exist in MongoDB, sign out and redirect to Welcome page
          await auth.signOut();
          navigate('/welcome');
          return;
        }
      } catch (error) {
        console.error('Error initializing app:', error);
      }
    };

    initApp();

    return () => {
      mounted = false;
      if (user?.email) {
        enterpriseAPI.clearAdminCache(user.email);
      }
    };
  }, [user, checkAdminStatus, refreshEvents, isAdmin]);

  useEffect(() => {
    const initUnifiedVoiceService = async () => {
      try {
        setIsModelLoading(true);
        setVoiceError('Chargement du modèle de reconnaissance vocale...');
        const service = new UnifiedVoiceService();
        await service.initWhisper((progress) => {
          setLoadingProgress(progress);
          if (progress === 100) {
            setVoiceError('');
            setIsModelLoading(false);
          } else {
            setVoiceError(`Chargement du modèle: ${progress}%`);
          }
        });
        setUnifiedVoiceService(service);
      } catch (error) {
        setVoiceError(error instanceof Error ? error.message : 'Échec de l\'initialisation de l\'assistant vocal');
        setIsModelLoading(false);
      }
    };

    const checkPendingUsers = async () => {
      if (isAdmin && enterprise?.code) {
        try {
          console.log('Fetching pending users for enterprise code:', enterprise.code); // Debug log
          const users = await userAPI.getPendingUsers(enterprise.code);
          console.log('Pending users fetched:', users); // Debug log
          setPendingUsers(users || []);
        } catch (error) {
          console.error('Error checking pending users:', error);
        }
      }
    };

    const loadParticipants = async () => {
      // Always get the user's enterprise code, even for non-admins
      let enterpriseCode = enterprise?.code;
      if (!enterpriseCode && user) {
        try {
          // fallback: get user's enterprise code from user profile
          const userData = await userAPI.getUserById(user.uid);
          enterpriseCode = userData.enterpriseCode;
        } catch (userError) {
          console.error('User not found in MongoDB, redirecting to Welcome page:', userError);
          // If user doesn't exist in MongoDB, sign out and redirect to Welcome page
          await auth.signOut();
          navigate('/welcome');
          return;
        }
      }
      if (user && enterpriseCode) {
        try {
          const users = await meetingAPI.getEnterpriseUsers(enterpriseCode);
          // Store all users including the current user for reference
          setAvailableParticipants(users);

          // Create a mapping of user IDs to display names for creators
          const displayNameMap = {};
          users.forEach(user => {
            const displayName = user.firstName && user.lastName
              ? `${user.firstName} ${user.lastName}`
              : user.email;
            displayNameMap[user._id] = displayName;
          });
          setCreatorDisplayNames(displayNameMap);
        } catch (error) {
          console.error('Error loading participants:', error);
        }
      }
    };

    initUnifiedVoiceService();

    if (user) {
      loadParticipants();

      let pendingUsersInterval;
      if (isAdmin) {
        checkPendingUsers();
        pendingUsersInterval = setInterval(checkPendingUsers, 30000);
      }

      return () => {
        if (pendingUsersInterval) {
          clearInterval(pendingUsersInterval);
        }
      };
    }

    return () => {
      if (unifiedVoiceService) {
        unifiedVoiceService.stopListening();
      }
    };
  }, [user, isAdmin, enterprise]);

  useEffect(() => {
    const handleEventsUpdate = () => {
      refreshEvents();
    };

    window.addEventListener('eventsUpdated', handleEventsUpdate);

    if (user) {
      console.log('Initial events refresh');
      refreshEvents();
    }

    return () => {
      window.removeEventListener('eventsUpdated', handleEventsUpdate);
    };
  }, [user, refreshEvents]);

  useEffect(() => {
    if (user) fetchMeetingFiles();
  }, [user, fetchMeetingFiles]);

  useEffect(() => {
    const handleFilesUpdated = () => {
      fetchMeetingFiles();
    };
    window.addEventListener('filesUpdated', handleFilesUpdated);
    return () => window.removeEventListener('filesUpdated', handleFilesUpdated);
  }, [fetchMeetingFiles]);

  useEffect(() => {
    // Listen for navigation to /files or after meeting ends
    const handleFilesRefresh = () => {
      // Optionally, you can trigger a refresh of files here if you have a files state/API
      // For example, if you have a files list: refreshFiles();
      // If your files are shown in a separate page/component, ensure that page fetches latest files on mount.
      // If you want to show a notification here, you can do so.
    };

    window.addEventListener('focus', handleFilesRefresh);
    return () => window.removeEventListener('focus', handleFilesRefresh);
  }, []);

  // Handle click outside to close menus
  useEffect(() => {
    function handleClickOutside(event) {
      // Profile menu reference is kept for potential future use
      if (profileMenuRef.current && !profileMenuRef.current.contains(event.target)) {
        // We're not using showProfileMenu anymore, but keeping the structure for future use
        // setShowProfileMenu(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    // Listen for transcript updates from Meet.js via a custom event
    const handleTranscriptUpdate = (e) => {
      setLiveTranscript(e.detail || '');
    };
    window.addEventListener('meetingTranscriptUpdate', handleTranscriptUpdate);
    return () => window.removeEventListener('meetingTranscriptUpdate', handleTranscriptUpdate);
  }, []);

  // Notification logic
  useEffect(() => {
    if (!events || events.length === 0) {
      setNotifications([]);
      return;
    }
    const now = new Date();
    const notifs = [];
    events.forEach(ev => {
      const start = new Date(ev.start);
      const diffMs = start - now;
      const diffMin = diffMs / (60 * 1000);
      if (diffMin > 0) {
        if (diffMin <= 30) {
          notifs.push({
            id: ev.id,
            message: t('meeting_starts_30min', { title: ev.title }),
            event: ev,
            time: start
          });
        } else if (diffMin <= 120) {
          notifs.push({
            id: ev.id + '_2h',
            message: t('meeting_starts_2h', { title: ev.title }),
            event: ev,
            time: start
          });
        } else if (diffMin <= 1440) {
          notifs.push({
            id: ev.id + '_24h',
            message: t('meeting_starts_24h', { title: ev.title }),
            event: ev,
            time: start
          });
        }
      }
    });
    setNotifications(notifs);
  }, [events]);

  const handleCreateMeet = async () => {
    const roomId = Math.random().toString(36).substring(7);

    if (!user) {
      console.error('No user found');
      return;
    }

    try {
      // Create the meeting in the database
      await meetingAPI.createMeeting({
        roomId,
        title: 'New Meeting',
        description: `Meeting created on ${new Date().toLocaleString()}`,
        date: new Date(),
        createdBy: user.uid,
        participants: [user.uid], // Creator is the only participant initially
        status: 'in-progress',
        enterpriseCode: enterprise?.code || 'default'
      });

      // Navigate to the meeting room
      navigate(`/meet/${roomId}`);
    } catch (error) {
      console.error('Error creating meeting:', error);
      navigate(`/meet/${roomId}`);
    }
  };

  const handleJoinMeet = (e) => {
    e.preventDefault();
    if (!meetCode.trim()) {
      setJoinError(t('please_enter_meeting_code'));
      return;
    }
    setJoinError('');
    navigate(`/meet/${meetCode.trim()}`);
  };

  // Fonction pour gérer le téléchargement direct
  const handleDirectDownload = useCallback(async (filters, message) => {
    try {
      console.log('handleDirectDownload called with:', { filters, message });

      // Obtenir le token d'authentification
      const token = await user.getIdToken();

      // Obtenir le code d'entreprise
      const userEnterpriseCode = enterpriseCode || enterprise?.code;

      if (!userEnterpriseCode) {
        throw new Error('Code d\'entreprise non trouvé');
      }

      // Lancer le téléchargement
      await downloadService.downloadFileByFilters(filters, token, userEnterpriseCode);

      // Message de succès
      const currentLang = i18n.language || 'fr';
      let successMessage = '';

      if (currentLang.startsWith('en')) {
        successMessage = 'File downloaded successfully!';
      } else if (currentLang.startsWith('ar')) {
        successMessage = 'تم تحميل الملف بنجاح!';
      } else {
        successMessage = 'Fichier téléchargé avec succès !';
      }

      setVoiceResponse(successMessage);
      showSuccess(successMessage);

    } catch (error) {
      console.error('Direct download failed:', error);

      // Message d'erreur
      const currentLang = i18n.language || 'fr';
      let errorMessage = '';

      if (currentLang.startsWith('en')) {
        errorMessage = `Download failed: ${error.message}`;
      } else if (currentLang.startsWith('ar')) {
        errorMessage = `فشل التحميل: ${error.message}`;
      } else {
        errorMessage = `Échec du téléchargement : ${error.message}`;
      }

      setVoiceResponse(errorMessage);
      showError(errorMessage);
    }
  }, [downloadService, user, enterpriseCode, enterprise, i18n.language, showSuccess, showError]);

  const handleVoiceAssistant = async () => {
    if (!unifiedVoiceService) {
      setVoiceError(t('voice_assistant_not_initialized'));
      return;
    }
    try {
      if (!isListening) {
        setVoiceResponse(t('voice_assistant_listening'));
        setVoiceError('');
        setIsListening(true);
        await unifiedVoiceService.startListening(async (text, command) => {
          setVoiceResponse(`Traitement: "${text}"`);
          try {
            const lower = text ? text.toLowerCase() : '';
            // Diviser sur "et" pour l'accès double
            const commands = lower.split(/\s+et\s+/);

            for (let cmd of commands) {
              cmd = cmd.trim();
              // Direct icon/button actions
              if (cmd.includes('go to my profile') || cmd === 'profile') {
                setVoiceResponse('Navigating to your profile...');
                navigate('/profile');
                continue;
              }
              if (cmd.includes('files') || cmd.includes('documents')) {
                setVoiceResponse('Opening files...');
                navigate('/files');
                continue;
              }
              if (cmd.includes('manage enterprise')) {
                setVoiceResponse('Opening enterprise management...');
                navigate('/manage-enterprise', { state: { enterprise } });
                continue;
              }
              if (cmd.includes('pending users') || cmd.includes('approve users')) {
                setVoiceResponse('Showing pending user requests...');
                setShowPendingUsersModal(true);
                continue;
              }
              if (cmd.includes('join meet') || cmd.includes('join meeting')) {
                setVoiceResponse('Opening join meeting dialog...');
                setShowJoinModal(true);
                continue;
              }
              if (cmd.includes('schedule meeting') || cmd.includes('create event')) {
                setVoiceResponse('Opening schedule meeting dialog...');
                handleCreateEvent();
                continue;
              }
              if (cmd.includes('create meet') || cmd.includes('create meeting') || cmd.includes('new meeting')) {
                setVoiceResponse('Creating a new meeting...');
                await handleCreateMeet();
                continue;
              }
              if (cmd.includes('manage user') || cmd.includes('users management')) {
                setVoiceResponse('Navigating to manage users...');
                navigate('/manage-users');
                continue;
              }
              // --- New event commands ---
              if (cmd.includes('create new event')) {
                setVoiceResponse('Opening create event dialog...');
                handleCreateEvent();
                continue;
              }
              // Update event on [date]
              if (cmd.match(/update event (on|at|for)?\s*(.+)/)) {
                const match = cmd.match(/update event (on|at|for)?\s*(.+)/);
                const dateStr = match[2];
                const date = new Date(dateStr);
                if (isNaN(date.getTime())) {
                  setVoiceResponse('Could not understand the date for updating event.');
                  continue;
                }
                // Find event by date
                const eventToUpdate = events.find(ev =>
                  moment(ev.start).isSame(date, 'day')
                );
                if (eventToUpdate) {
                  setVoiceResponse(`Opening update dialog for event on ${date.toLocaleDateString()}.`);
                  setEventFormData({
                    ...eventFormData,
                    ...eventToUpdate,
                    start: new Date(eventToUpdate.start),
                    end: new Date(eventToUpdate.end),
                    participants: eventToUpdate.resource?.participants || [],
                    id: eventToUpdate.resource?._id,
                  });
                  setShowEventModal(true);
                } else {
                  setVoiceResponse(`No event found on ${date.toLocaleDateString()}.`);
                }
                continue;
              }
              // Delete event on [date]
              if (cmd.match(/delete event (on|at|for)?\s*(.+)/)) {
                const match = cmd.match(/delete event (on|at|for)?\s*(.+)/);
                const dateStr = match[2];
                const date = new Date(dateStr);
                if (isNaN(date.getTime())) {
                  setVoiceResponse('Could not understand the date for deleting event.');
                  continue;
                }
                // Find event by date
                const eventToDelete = events.find(ev =>
                  moment(ev.start).isSame(date, 'day')
                );
                if (eventToDelete) {
                  setVoiceResponse(`Deleting event on ${date.toLocaleDateString()}...`);
                  await handleEventDelete(eventToDelete.resource?._id);
                } else {
                  setVoiceResponse(`No event found on ${date.toLocaleDateString()}.`);
                }
                continue;
              }
              // Join event on [date]
              if (cmd.match(/join event (on|at|for)?\s*(.+)/)) {
                const match = cmd.match(/join event (on|at|for)?\s*(.+)/);
                const dateStr = match[2];
                const date = new Date(dateStr);
                if (isNaN(date.getTime())) {
                  setVoiceResponse('Could not understand the date for joining event.');
                  continue;
                }
                // Find event by date
                const eventToJoin = events.find(ev =>
                  moment(ev.start).isSame(date, 'day')
                );
                if (eventToJoin) {
                  setVoiceResponse(`Joining event on ${date.toLocaleDateString()}...`);
                  navigate(`/meet/${eventToJoin.resource?.roomId}`);
                } else {
                  setVoiceResponse(`No event found on ${date.toLocaleDateString()}.`);
                }
                continue;
              }
              // --- End new event commands ---

              // Définir la langue du service vocal selon l'interface
              unifiedVoiceService.setLanguage(i18n.language);

              // Utiliser l'analyse de commande intégrée du service unifié
              const parsedCommand = command || unifiedVoiceService.parseCommand(cmd);
              const result = await unifiedVoiceService.executeCommand(parsedCommand);

              console.log('Voice command result:', result);

              if (result.success) {
                switch (result.action) {
                  case 'navigate':
                    navigate(result.route);
                    setVoiceResponse(result.message);
                    break;
                  case 'redirect':
                    navigate(result.url);
                    setVoiceResponse(result.message);
                    break;
                  case 'viewFiles':
                    navigate('/files');
                    setVoiceResponse(result.message);
                    break;
                  case 'search':
                    navigate('/files', { state: { searchQuery: result.query } });
                    setVoiceResponse(result.message);
                    break;
                  case 'help':
                    setVoiceResponse(result.message);
                    break;
                  case 'downloadFile':
                    console.log('downloadFile case - result.directDownload:', result.directDownload);
                    console.log('downloadFile case - full result:', result);

                    if (result.directDownload) {
                      // Téléchargement direct sans navigation
                      console.log('Starting direct download with filters:', result.filters);
                      handleDirectDownload(result.filters, result.message);
                    } else {
                      // Naviguer vers les fichiers avec les filtres de téléchargement
                      console.log('Navigating to files with download filters:', result.filters);
                      navigate('/files', {
                        state: {
                          downloadRequest: true,
                          filters: result.filters,
                          autoDownload: result.filters.specificDownload || false
                        }
                      });
                    }
                    setVoiceResponse(result.message);
                    break;
                  default:
                    setVoiceResponse(result.message);
                }
              } else {
                // Utiliser les suggestions du service vocal unifié si disponibles
                const suggestions = result.suggestions || [];

                // Utiliser la langue de l'interface utilisateur au lieu de détecter la langue de la commande
                const currentLanguage = i18n.language || 'fr';
                const isEnglish = currentLanguage.startsWith('en');
                const isFrench = currentLanguage.startsWith('fr');
                const isArabic = currentLanguage.startsWith('ar');

                console.log('Voice response language debug:', {
                  cmd,
                  currentLanguage,
                  isEnglish,
                  isFrench,
                  isArabic,
                  suggestions
                });

                let responseMessage;

                if (isEnglish) {
                  responseMessage = `I heard: "${cmd}" but I'm not sure what to do. Try saying:`;
                  if (suggestions.length > 0) {
                    responseMessage += '\n' + suggestions.map(s => `- "${s}"`).join('\n');
                  } else {
                    responseMessage += '\n- "Create a meeting"\n- "Go to calendar"\n- "View my profile"\n- "Manage enterprise"\n- "View files"\n- "Download transcript"\n- "Help"';
                  }
                } else if (isArabic) {
                  responseMessage = `سمعت: "${cmd}" لكنني لست متأكداً مما يجب فعله. جرب قول:`;
                  if (suggestions.length > 0) {
                    responseMessage += '\n' + suggestions.map(s => `- "${s}"`).join('\n');
                  } else {
                    responseMessage += '\n- "إنشاء اجتماع"\n- "الذهاب إلى التقويم"\n- "عرض ملفي الشخصي"\n- "إدارة المؤسسة"\n- "عرض الملفات"\n- "تحميل النسخة"\n- "مساعدة"';
                  }
                } else {
                  // Français par défaut
                  responseMessage = `J'ai entendu: "${cmd}" mais je ne suis pas sûr de quoi faire. Essayez de dire:`;
                  if (suggestions.length > 0) {
                    responseMessage += '\n' + suggestions.map(s => `- "${s}"`).join('\n');
                  } else {
                    responseMessage += '\n- "Créer une réunion"\n- "Aller au calendrier"\n- "Voir mon profil"\n- "Gérer l\'entreprise"\n- "Voir les fichiers"\n- "Télécharger transcription"\n- "Aide"';
                  }
                }

                setVoiceResponse(responseMessage);
              }
            }
            setIsListening(false);
            unifiedVoiceService.stopListening();
          } catch (error) {
            console.error('Erreur lors du traitement de la commande:', error);
            setVoiceError('Erreur lors du traitement de la commande vocale');
            setIsListening(false);
            unifiedVoiceService.stopListening();
          }
        });
      } else {
        setIsListening(false);
        unifiedVoiceService.stopListening();
        setVoiceResponse('Assistant vocal arrêté');
      }
    } catch (error) {
      console.error('Erreur de l\'assistant vocal:', error);
      setVoiceError(error.message || 'Erreur avec l\'assistant vocal');
      setIsListening(false);
      unifiedVoiceService.stopListening();
    }
  };

  const handleManageEnterprise = () => {
    navigate('/manage-enterprise', { state: { enterprise } });
  };

  const handleSelectEvent = useCallback((event) => {
    console.log('Event clicked:', event);

    // Always allow opening the event modal for update/delete
    // Only restrict the "Join" button inside the modal

    // Normalize participants to array of user IDs
    let participants = [];
    if (Array.isArray(event.resource?.participants)) {
      participants = event.resource.participants.map(
        p => typeof p === 'string' ? p : (p.userId || p._id || p)
      );
    }

    // Get the creator ID from the event
    const creatorId = event.resource?.createdBy || user.uid;

    // If we don't have the creator's display name in our mapping, try to fetch it
    if (creatorId !== user.uid && !creatorDisplayNames[creatorId]) {
      // Try to find the creator in the available participants
      const creator = availableParticipants.find(p => p._id === creatorId);
      if (creator) {
        const displayName = creator.firstName && creator.lastName
          ? `${creator.firstName} ${creator.lastName}`
          : creator.email;

        // Update the creator display names mapping
        setCreatorDisplayNames(prev => ({
          ...prev,
          [creatorId]: displayName
        }));
      } else {
        // If creator is not in available participants, fetch their info from the server
        (async () => {
          try {
            console.log('Fetching creator info for ID:', creatorId);
            const creatorData = await userAPI.getUserById(creatorId);
            if (creatorData) {
              const displayName = creatorData.firstName && creatorData.lastName
                ? `${creatorData.firstName} ${creatorData.lastName}`
                : creatorData.email;

              console.log('Found creator:', displayName);
              // Update the creator display names mapping
              setCreatorDisplayNames(prev => ({
                ...prev,
                [creatorId]: displayName
              }));

              // Update the event form data with the creator's name
              setEventFormData(prev => ({
                ...prev,
                creatorDisplayName: displayName
              }));
            }
          } catch (error) {
            console.error('Error fetching creator info:', error);
          }
        })();
      }
    }

    const formData = {
      id: event.resource._id,
      title: event.title || 'Untitled Meeting',
      start: new Date(event.start),
      end: new Date(event.end),
      description: event.resource?.description || '',
      roomId: event.resource?.roomId,
      isAccessible: true,
      participants: participants.filter(id => id !== creatorId), // Remove creator from participants list
      createdBy: creatorId, // Explicitly set the creator
      enterpriseCode: event.resource?.enterpriseCode,
      creatorDisplayName: creatorId === user.uid
        ? userDisplayName
        : (creatorDisplayNames[creatorId] || `${t('creator')} (${creatorId.substring(0, 8)}...)`)
    };

    setEventFormData(formData);
    setShowEventModal(true);
  }, [user, creatorDisplayNames, availableParticipants, userDisplayName, t]);

  const eventStyleGetter = useCallback((event) => {
    return {
      style: {
        backgroundColor: '#3B82F6',
        color: 'white',
        borderRadius: '3px',
        border: 'none',
        display: 'block',
        padding: '2px 5px',
        fontSize: '14px',
        opacity: event.resource?.isAccessible ? 1 : 0.7
      }
    };
  }, []);

  const handleEventDelete = async (eventId) => {
    try {
      console.log('Starting delete for event:', eventId);
      if (!eventId) {
        setError('Cannot delete: Event ID not found');
        return;
      }

      setLoading(true);
      setError(null);

      const token = await user.getIdToken(true);

      const eventToDelete = events.find(event => event.resource._id === eventId);

      if (!eventToDelete) {
        console.error('Event not found:', eventId);
        throw new Error('Event not found in calendar');
      }

      console.log('Deleting event with ID:', eventId);

      await meetingAPI.deleteCalendarEvent(eventId, {
        token,
        enterpriseCode: enterprise?.code
      });

      await refreshEvents();
      setShowEventModal(false);

    } catch (error) {
      console.error('Error deleting event:', error);
      setError(error.message || 'Failed to delete event');
    } finally {
      setLoading(false);
    }
  };

  const handleEventSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      const token = await user.getIdToken(true);
      if (!token) {
        throw new Error('Authentication required');
      }

      // Convert local dates to UTC for storage
      const fixedDates = {
        ...eventFormData,
        startTime: moment(eventFormData.start).utc().format(),
        endTime: moment(eventFormData.end).utc().format(),
        date: moment(eventFormData.start).utc().format()
      };

      // Ensure participants is an array of user IDs (strings)
      const normalizedParticipants = eventFormData.participants.map(p =>
        typeof p === 'string' ? p : (p.userId || p._id || p)
      );

      // Always include creator as the first participant, but only once
      // This ensures the creator is saved in the database but not shown in the selection list
      let allParticipantIds = [user.uid];

      // Add all other participants (excluding the creator if they somehow got added)
      normalizedParticipants.forEach(id => {
        if (id !== user.uid && !allParticipantIds.includes(id)) {
          allParticipantIds.push(id);
        }
      });
      // Map to array of objects for backend
      const participantsForBackend = allParticipantIds.map(id => ({ userId: id }));

      const eventWithEnterprise = {
        ...fixedDates,
        enterpriseCode: enterprise?.code || user.enterpriseCode,
        token,
        createdBy: user.uid,
        participants: participantsForBackend
      };

      let updatedEvent;
      if (showEventModal) {
        if (!eventFormData.id) {
          throw new Error('No event ID provided for update');
        }
        console.log('Updating event:', eventFormData.id);
        updatedEvent = await meetingAPI.updateCalendarEvent(eventFormData.id, eventWithEnterprise);
      } else {
        console.log('Creating new event with dates:', {
          start: fixedDates.startTime,
          end: fixedDates.endTime
        });
        if (!eventFormData.roomId) {
          eventFormData.roomId = Math.random().toString(36).substring(7);
        }
        updatedEvent = await meetingAPI.createEnterpriseEvent(eventWithEnterprise);
      }

      // After create/update, update eventFormData with normalized participants
      if (updatedEvent && updatedEvent.participants) {
        // Get all participants except the creator
        const participantsWithoutCreator = updatedEvent.participants
          .map(p => typeof p === 'string' ? p : (p.userId || p._id || p))
          .filter(id => id !== user.uid); // Exclude the creator

        setEventFormData(prev => ({
          ...prev,
          participants: participantsWithoutCreator
        }));
      }

      await refreshEvents();
      setShowEventModal(false);
      setShowCreateModal(false);
    } catch (error) {
      console.error('Event operation error:', error);
      setError(error.message || 'Failed to save event');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateEvent = () => {
    setEventFormData({
      title: '',
      start: new Date(),
      end: new Date(new Date().setHours(new Date().getHours() + 1)),
      description: '',
      participants: [], // Creator will be added automatically when saving
      roomId: Math.random().toString(36).substring(7),
      isAccessible: true,
      createdBy: user.uid, // Explicitly set the creator
    });
    setShowCreateModal(true);
  };

  const handleApproveUser = async (userId) => {
    try {
      await userAPI.approveUser(userId);
      setPendingUsers(pendingUsers.filter(user => user._id !== userId));
      setSuccess('User approved successfully');
    } catch (error) {
      setError(error.message || 'Failed to approve user');
    }
  };

  const handleRejectUser = async (userId) => {
    try {
      await userAPI.rejectUser(userId);
      setPendingUsers(pendingUsers.filter(user => user._id !== userId));
      setSuccess('User rejected successfully');
    } catch (error) {
      setError(error.message || 'Failed to reject user');
    }
  };

  // Add a helper to check if user can manage the event
  const canManageEvent = (event = eventFormData) => {
    // The createdBy field may be a UID or email, user.uid is UID
    const creatorId = event?.createdBy || user.uid;
    return isAdmin || creatorId === user.uid;
  };

  // Removed unused CalendarEventButtons component

  // Removed unused CreateEventButtons component

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Modern background with dark blue gradient image */}
      <div className="modern-background"></div>
      <div className="content-overlay"></div>

      <header className="modern-header shadow-sm relative z-10">
        <div className="max-w-7xl mx-auto px-4 py-3 sm:px-6 lg:px-8 flex justify-center items-center">
          <div className="flex items-center space-x-8">
            <div
              className="flex items-center mr-8 cursor-pointer hover:opacity-80 transition-opacity"
              onClick={() => {
                // Refresh the home page
                window.location.reload();
              }}
              title="Refresh Home Page"
            >
              <div className="bg-blue-500 p-1.5 rounded-md">
                <Video className="h-5 w-5 text-white" />
              </div>
              <h1 className="ml-2 text-xl font-semibold logo-text text-title">Neeting</h1>
            </div>
            {/* Notifications Icon */}
            <div className="relative">
              <button
                ref={notificationRef}
                onClick={() => setShowNotifications(!showNotifications)}
                className="navbar-text flex items-center space-x-2 transition-colors"
                aria-label="Notifications"
              >
                <Bell className="h-5 w-5" />
                {notifications.length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {notifications.length}
                  </span>
                )}
              </button>

              {/* Portal-based Notifications Dropdown */}
              <NotificationDropdown
                notifications={notifications}
                isOpen={showNotifications}
                onClose={() => setShowNotifications(false)}
                onJoinMeeting={(roomId) => {
                  if (!roomId) {
                    setError('Meeting room not found');
                    return;
                  }
                  navigate(`/meet/${roomId}`);
                }}
                buttonRef={notificationRef}
              />
            </div>
            {/* ...existing header buttons... */}
            <button
              onClick={() => navigate('/files')}
              className="navbar-text flex items-center space-x-2 transition-colors"
            >
              <FileText className="h-5 w-5" />
              <span className="text-button">{t('files')}</span>
            </button>
            {isAdmin && (
              <button
                onClick={handleManageEnterprise}
                className="navbar-text flex items-center space-x-2 transition-colors"
              >
                <Briefcase className="h-5 w-5" />
                <span className="text-button">{t('manage_enterprise')}</span>
              </button>
            )}
            {isAdmin && (
              <button
                onClick={() => setShowPendingUsersModal(true)}
                className="relative navbar-text flex items-center space-x-2 transition-colors"
              >
                <UserPlus className="h-5 w-5" />
                {pendingUsers.length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {pendingUsers.length}
                  </span>
                )}
              </button>
            )}
            {/* Profile button */}
            <button
              onClick={() => navigate('/profile')}
              className="navbar-text flex items-center space-x-2 transition-colors"
            >
              <UserIcon className="h-5 w-5" />
              <span className="text-button">{userDisplayName || t('profile')}</span>
            </button>

            {/* Direct Logout Button */}
            <button
              onClick={() => {
                console.log("Logout clicked");
                try {
                  signOut();
                  console.log("Signed out successfully");
                  window.location.href = '/';
                } catch (error) {
                  console.error("Logout error:", error);
                }
              }}
              className="flex items-center space-x-2 text-white hover:text-red-300 transition-colors bg-slate-800 px-3 py-1 rounded-md logout-button"
            >
              <LogOut className="h-5 w-5" />
              <span className="text-button">{t('log_out')}</span>
            </button>
          </div>
        </div>
      </header>

      <main className="content-container max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8 relative z-10">
        <div className="mb-6 flex flex-wrap gap-3 justify-center sm:justify-start">
          <AnimatedButton
            onClick={() => setShowJoinModal(true)}
            variant="primary"
            icon={Video}
            className=""
          >
            {t('join_meet')}
          </AnimatedButton>

          <AnimatedButton
            onClick={handleVoiceAssistant}
            variant={isListening ? "primary" : "secondary"}
            icon={Mic}
            disabled={isModelLoading || !unifiedVoiceService}
            loading={isModelLoading}
            className=""
          >
            {isModelLoading
              ? loadingProgress > 0 && loadingProgress < 100
                ? `${t('loading_ai')} ${loadingProgress}%`
                : t('loading_ai')
              : isListening
                ? t('listening')
                : t('ai_assistant_button')
            }
          </AnimatedButton>

          <AnimatedButton
            onClick={handleCreateEvent}
            variant="success"
            icon={Plus}
            className=""
          >
            {t('schedule_meeting')}
          </AnimatedButton>
        </div>

        {voiceError && (
          <div className="modern-card mb-5 p-3 border border-red-800 flex items-start bg-opacity-20 bg-red-900">
            <AlertCircle className="h-4 w-4 text-red-400 mr-2 flex-shrink-0 mt-0.5" />
            <p className="text-red-300 text-sm text-body">{voiceError}</p>
          </div>
        )}

        {voiceResponse && (
          <div className="modern-card mb-5 p-3 border border-blue-800 bg-opacity-20 bg-blue-900">
            <p className="text-blue-300 text-sm text-body">{voiceResponse}</p>
          </div>
        )}

        {success && (
          <div className="modern-card mb-5 p-3 border border-green-800 bg-opacity-20 bg-green-900">
            <p className="text-green-300 text-sm text-body">{success}</p>
          </div>
        )}

        {/* Realtime Transcript Section */}
        {liveTranscript && (
          <div className="modern-card p-4 mb-5">
            <h2 className="text-base font-medium mb-2 text-blue-300 text-subtitle">{t('live_meeting_transcript')}</h2>
            <div className="whitespace-pre-line text-gray-300 text-sm max-h-40 overflow-y-auto bg-slate-900 bg-opacity-50 p-3 rounded border border-slate-700">
              <span className="text-body">{liveTranscript}</span>
            </div>
          </div>
        )}

        {/* Enterprise Banner - Simplified version for home page */}
        {(enterprise || enterpriseCode) && (
          <EnterpriseBanner
            enterprise={enterprise}
            enterpriseCode={enterpriseCode}
          />
        )}

        <div className="modern-calendar p-4 min-h-[600px]">
          {loading ? (
            <div className="flex justify-center items-center h-[600px]">
              <LoadingSpinner
                size="lg"
                variant="ring"
                text={t('loading_calendar')}
                color="blue"
              />
            </div>
          ) : (
            <Calendar
              events={events}
              onSelectEvent={handleSelectEvent}
              eventPropGetter={eventStyleGetter}
              defaultDate={new Date()}
              toolbar={true}
              messages={messages}
              ref={(calendarRef) => {}} // Empty ref function to avoid warnings
            />
          )}
        </div>

        {meetingFiles.length > 0 && (
          <div className="modern-card p-4 mt-5">
            <h2 className="text-base font-medium mb-3 text-blue-300 text-subtitle">{t('meeting_files')}</h2>
            <ul className="divide-y divide-slate-700">
              {meetingFiles.map((file, index) => (
                <li key={file._id || file.name} className="flex items-center justify-between py-2 hover:bg-slate-800 hover:bg-opacity-50 px-2 rounded transition-colors">
                    <span className="truncate text-gray-300 text-body">{file.name}</span>
                    <a
                      href={file.url || '#'}
                      download={file.name}
                      onClick={async (e) => {
                        if (!file.url && file.content) {
                          e.preventDefault();
                          const blob = new Blob([file.content], { type: 'text/plain;charset=utf-8' });
                          const url = URL.createObjectURL(blob);
                          const link = document.createElement('a');
                          link.href = url;
                          link.download = file.name;
                          document.body.appendChild(link);
                          link.click();
                          document.body.removeChild(link);
                          URL.revokeObjectURL(url);
                        }
                      }}
                      className="text-blue-400 hover:text-blue-300 hover:underline ml-4 text-sm flex items-center text-button"
                    >
                      <FileText className="h-3.5 w-3.5 mr-1" />
                      {t('download')}
                    </a>
                </li>
              ))}
            </ul>
          </div>
        )}
      </main>

      {/* Join Meeting Modal */}
      {showJoinModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white/95 rounded-2xl shadow-2xl max-w-md w-full p-6 border border-gray-100">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-indigo-900 flex items-center">
                <Video className="h-6 w-6 text-blue-600 mr-2" />
                {t('join_meeting')}
              </h2>
              <button
                onClick={() => {
                  setShowJoinModal(false);
                  setMeetCode('');
                  setJoinError('');
                }}
                className="text-gray-400 hover:text-gray-600 hover:bg-gray-100 p-1 rounded-full transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <form onSubmit={handleJoinMeet}>
              <div className="mb-6">
                <label htmlFor="meetCode" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('meeting_code')}
                </label>
                <input
                  type="text"
                  id="meetCode"
                  value={meetCode}
                  onChange={(e) => setMeetCode(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white/80"
                  placeholder={t('enter_meeting_code')}
                />
                {joinError && (
                  <p className="mt-2 text-sm text-red-600 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {joinError}
                  </p>
                )}
              </div>

              <div className="flex space-x-3">
                <button
                  type="submit"
                  className="flex-1 bg-gradient-to-r from-blue-500 to-blue-700 text-white px-4 py-3 rounded-xl hover:shadow-lg transition-all duration-300 font-medium"
                >
                  {t('join')}
                </button>
                <button
                  type="button"
                  onClick={handleCreateMeet}
                  className="flex-1 bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 px-4 py-3 rounded-xl hover:shadow-md transition-all duration-300 font-medium"
                >
                  {t('create_new')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Create Event Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="theme-modal rounded-2xl shadow-2xl max-w-md w-full p-6 border max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold theme-modal-text flex items-center">
                <Plus className="h-6 w-6 text-green-600 mr-2" />
                {t('schedule_new_meeting')}
              </h2>
              <button
                onClick={() => setShowCreateModal(false)}
                className="theme-modal-close p-1 rounded-full transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <form onSubmit={handleEventSubmit} className="space-y-5">
              {error && (
                <div className="p-4 bg-red-50/80 backdrop-blur-sm border border-red-200 rounded-xl text-red-700 flex items-center">
                  <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0" />
                  <p>{error}</p>
                </div>
              )}
              <div>
                <label className="block text-sm font-medium theme-modal-label mb-1">{t('title')}</label>
                <input
                  type="text"
                  value={eventFormData.title}
                  onChange={(e) => setEventFormData({...eventFormData, title: e.target.value})}
                  className="block w-full rounded-xl border px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors theme-modal-input"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium theme-modal-label mb-1">{t('start')}</label>
                  <input
                    type="datetime-local"
                    value={moment(eventFormData.start).format('YYYY-MM-DDTHH:mm')}
                    onChange={(e) => setEventFormData({...eventFormData, start: new Date(e.target.value)})}
                    className="block w-full rounded-xl border px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors theme-modal-input"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium theme-modal-label mb-1">{t('end')}</label>
                  <input
                    type="datetime-local"
                    value={moment(eventFormData.end).format('YYYY-MM-DDTHH:mm')}
                    onChange={(e) => setEventFormData({...eventFormData, end: new Date(e.target.value)})}
                    className="block w-full rounded-xl border px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors theme-modal-input"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium theme-modal-label mb-1">{t('description_label')}</label>
                <textarea
                  value={eventFormData.description}
                  onChange={(e) => setEventFormData({...eventFormData, description: e.target.value})}
                  className="block w-full rounded-xl border px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors theme-modal-input"
                  rows="3"
                />
              </div>

              <div className="space-y-4">
                <label className="block text-sm font-medium theme-modal-label mb-1">
                  {t('select_participants')}
                </label>
                <select
                  multiple
                  value={eventFormData.participants}
                  onChange={(e) => {
                    const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
                    const updatedParticipants = [...eventFormData.participants];

                    selectedOptions.forEach(option => {
                      if (!updatedParticipants.includes(option)) {
                        updatedParticipants.push(option);
                      }
                    });

                    setEventFormData({
                      ...eventFormData,
                      participants: updatedParticipants
                    });
                  }}
                  className="block w-full rounded-xl border px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors theme-modal-input min-h-[150px]"
                >
                  {availableParticipants
                    .filter(u => u._id !== user.uid) // Exclude the creator from the selection list
                    .map(user => (
                      <option key={user._id} value={user._id}>
                        {user.firstName} {user.lastName} ({user.position})
                      </option>
                    ))}
                </select>

                <div className="mt-4">
                  <h4 className="font-medium theme-modal-label mb-2">{t('selected_participants')}:</h4>
                  <div className="flex flex-wrap gap-2 p-3 border rounded-xl min-h-[60px] theme-modal-input">
                    {/* Always show the creator with a special green badge */}
                    <div className="flex items-center bg-gradient-to-r from-green-100 to-green-200 px-3 py-1.5 rounded-full shadow-sm">
                      <span>{userDisplayName} ({t('creator')})</span>
                    </div>

                    {/* Show all other selected participants */}
                    {eventFormData.participants
                      .filter(userId => userId !== user.uid) // Ensure creator isn't duplicated
                      .map(userId => {
                        const participant = availableParticipants.find(u => u._id === userId);
                        return participant ? (
                          <div key={participant._id} className="flex items-center bg-gradient-to-r from-blue-100 to-blue-200 px-3 py-1.5 rounded-full shadow-sm">
                            <span>{participant.firstName} {participant.lastName}</span>
                            <button
                              type="button"
                              onClick={() => {
                                setEventFormData({
                                  ...eventFormData,
                                  participants: eventFormData.participants.filter(id => id !== userId)
                                });
                              }}
                              className="ml-2 text-gray-500 hover:text-red-500 hover:bg-red-50 rounded-full h-5 w-5 flex items-center justify-center transition-colors"
                            >
                              ×
                            </button>
                          </div>
                        ) : null;
                      })}
                  </div>
                </div>
              </div>

              <div className="flex space-x-3 pt-2">
                <button
                  type="submit"
                  className="w-full bg-gradient-to-r from-green-500 to-green-700 text-white px-4 py-3 rounded-xl hover:shadow-lg transition-all duration-300 flex items-center justify-center font-medium"
                >
                  <Plus className="h-5 w-5 mr-2" />
                  {t('create_meeting')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Event Management Modal */}
      {showEventModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="theme-modal rounded-2xl shadow-2xl max-w-md w-full p-6 border max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold theme-modal-text flex items-center">
                <Video className="h-6 w-6 text-blue-600 mr-2" />
                {t('meeting_details')}
              </h2>
              <button
                onClick={() => setShowEventModal(false)}
                className="theme-modal-close p-1 rounded-full transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <form onSubmit={handleEventSubmit} className="space-y-5">
              {error && (
                <div className="p-4 bg-red-50/80 backdrop-blur-sm border border-red-200 rounded-xl text-red-700 flex items-center">
                  <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0" />
                  <p>{error}</p>
                </div>
              )}
              <div>
                <label className="block text-sm font-medium theme-modal-label mb-1">{t('title')}</label>
                <input
                  type="text"
                  value={eventFormData.title}
                  onChange={(e) => setEventFormData({...eventFormData, title: e.target.value})}
                  className="block w-full rounded-xl border px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors theme-modal-input"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium theme-modal-label mb-1">{t('start')}</label>
                  <input
                    type="datetime-local"
                    value={moment(eventFormData.start).format('YYYY-MM-DDTHH:mm')}
                    onChange={(e) => setEventFormData({...eventFormData, start: new Date(e.target.value)})}
                    className="block w-full rounded-xl border px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors theme-modal-input"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium theme-modal-label mb-1">{t('end')}</label>
                  <input
                    type="datetime-local"
                    value={moment(eventFormData.end).format('YYYY-MM-DDTHH:mm')}
                    onChange={(e) => setEventFormData({...eventFormData, end: new Date(e.target.value)})}
                    className="block w-full rounded-xl border px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors theme-modal-input"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium theme-modal-label mb-1">{t('description_label')}</label>
                <textarea
                  value={eventFormData.description}
                  onChange={(e) => setEventFormData({...eventFormData, description: e.target.value})}
                  className="block w-full rounded-xl border px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors theme-modal-input"
                  rows="3"
                />
              </div>

              <div className="space-y-4">
                <label className="block text-sm font-medium theme-modal-label mb-1">
                  {t('select_participants')}
                </label>
                <select
                  multiple
                  value={eventFormData.participants}
                  onChange={(e) => {
                    const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
                    const updatedParticipants = [...eventFormData.participants];

                    selectedOptions.forEach(option => {
                      if (!updatedParticipants.includes(option)) {
                        updatedParticipants.push(option);
                      }
                    });

                    setEventFormData({
                      ...eventFormData,
                      participants: updatedParticipants
                    });
                  }}
                  className="block w-full rounded-xl border px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors theme-modal-input min-h-[150px]"
                >
                  {availableParticipants
                    .filter(u => u._id !== user.uid) // Exclude the creator from the selection list
                    .map(user => (
                      <option key={user._id} value={user._id}>
                        {user.firstName} {user.lastName} ({user.position})
                      </option>
                    ))}
                </select>

                <div className="mt-4">
                  <h4 className="font-medium theme-modal-label mb-2">{t('selected_participants')}:</h4>
                  <div className="flex flex-wrap gap-2 p-3 border rounded-xl min-h-[60px] theme-modal-input">
                    {/* Show the actual creator with a special green badge */}
                    <div className="flex items-center bg-gradient-to-r from-green-100 to-green-200 px-3 py-1.5 rounded-full shadow-sm">
                      <span>{eventFormData.creatorDisplayName || userDisplayName} ({t('creator')})</span>
                    </div>

                    {/* Show all other selected participants */}
                    {eventFormData.participants
                      .filter(userId => userId !== user.uid) // Ensure creator isn't duplicated
                      .map(userId => {
                        const participant = availableParticipants.find(u => u._id === userId);
                        return participant ? (
                          <div key={participant._id} className="flex items-center bg-gradient-to-r from-blue-100 to-blue-200 px-3 py-1.5 rounded-full shadow-sm">
                            <span>{participant.firstName} {participant.lastName}</span>
                            <button
                              type="button"
                              onClick={() => {
                                setEventFormData({
                                  ...eventFormData,
                                  participants: eventFormData.participants.filter(id => id !== userId)
                                });
                              }}
                              className="ml-2 text-gray-500 hover:text-red-500 hover:bg-red-50 rounded-full h-5 w-5 flex items-center justify-center transition-colors"
                            >
                              ×
                            </button>
                          </div>
                        ) : null;
                      })}
                  </div>
                </div>
              </div>

              <div className="flex space-x-3 pt-2">
                <div className="flex space-x-3 w-full">
                  <button
                    type="button"
                    onClick={() => {
                      const now = new Date();
                      const start = new Date(eventFormData.start);
                      const diffMin = (start - now) / (60 * 1000);
                      const canJoin = diffMin <= 15 && diffMin >= -60; // Allow joining 15 min before and up to 60 min after start

                      if (!canJoin) {
                        setError(t('meeting_join_time_error_extended', 'You can only join the meeting 15 minutes before it starts or up to 60 minutes after it started.'));
                        return;
                      }
                      navigate(`/meet/${eventFormData.roomId}`);
                    }}
                    className="flex-1 bg-gradient-to-r from-green-500 to-green-700 text-white px-4 py-3 rounded-xl hover:shadow-lg transition-all duration-300 font-medium"
                  >
                    {t('join_meeting')}
                  </button>

                  {/* Only show Update/Delete if user is admin or creator */}
                  {canManageEvent(eventFormData) && (
                    <>
                      <button
                        type="submit"
                        className="flex-1 bg-gradient-to-r from-blue-500 to-blue-700 text-white px-4 py-3 rounded-xl hover:shadow-lg transition-all duration-300 font-medium"
                      >
                        {t('update', 'Update')}
                      </button>
                      <button
                        type="button"
                        onClick={() => handleEventDelete(eventFormData?.id)}
                        className="flex-1 bg-gradient-to-r from-red-500 to-red-700 text-white px-4 py-3 rounded-xl hover:shadow-lg transition-all duration-300 font-medium"
                      >
                        {t('delete', 'Delete')}
                      </button>
                    </>
                  )}
                </div>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Pending Users Modal */}
      {showPendingUsersModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="theme-modal rounded-2xl shadow-2xl max-w-4xl w-full p-6 border max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold theme-modal-text flex items-center">
                <Users className="h-6 w-6 text-blue-600 mr-2" />
                {t('pending_user_requests', 'Pending User Requests')}
              </h2>
              <button
                onClick={() => setShowPendingUsersModal(false)}
                className="theme-modal-close p-1 rounded-full transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="space-y-4">
              {pendingUsers.map(user => (
                <div key={user._id} className="border rounded-xl p-5 theme-modal-input hover:shadow-md transition-all duration-300">
                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <p className="font-semibold theme-modal-label mb-1">{t('name', 'Name')}</p>
                      <p className="theme-modal-text">{user.firstName} {user.lastName}</p>
                    </div>
                    <div>
                      <p className="font-semibold theme-modal-label mb-1">{t('email', 'Email')}</p>
                      <p className="theme-modal-text">{user.email}</p>
                    </div>
                    <div>
                      <p className="font-semibold theme-modal-label mb-1">{t('phone', 'Phone')}</p>
                      <p className="theme-modal-text">{user.phone}</p>
                    </div>
                    <div>
                      <p className="font-semibold theme-modal-label mb-1">{t('position', 'Position')}</p>
                      <p className="theme-modal-text">{user.position}</p>
                    </div>
                  </div>
                  <div className="flex justify-end space-x-3 mt-5">
                    <button
                      onClick={() => handleApproveUser(user._id)}
                      className="bg-gradient-to-r from-green-500 to-green-700 text-white px-5 py-2.5 rounded-xl hover:shadow-lg transition-all duration-300 font-medium flex items-center"
                    >
                      <Check className="h-4 w-4 mr-1.5" />
                      {t('approve', 'Approve')}
                    </button>
                    <button
                      onClick={() => handleRejectUser(user._id)}
                      className="bg-gradient-to-r from-red-500 to-red-700 text-white px-5 py-2.5 rounded-xl hover:shadow-lg transition-all duration-300 font-medium flex items-center"
                    >
                      <X className="h-4 w-4 mr-1.5" />
                      {t('reject', 'Reject')}
                    </button>
                  </div>
                </div>
              ))}
              {pendingUsers.length === 0 && (
                <div className="text-center p-10 rounded-xl border theme-modal-input">
                  <UserX className="h-12 w-12 mx-auto mb-3 theme-modal-label" />
                  <p className="text-lg theme-modal-text">{t('no_pending_user_requests', 'No pending user requests.')}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}


    </div>
  );
};

export default Home;