import React, { useState, useEffect, useRef } from 'react';
import { Send, X, Paperclip, Smile, Image } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const ChatPanel = ({ isOpen, onClose, roomId, user }) => {
  const { t } = useTranslation();
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);

  // Sample initial messages for demonstration
  useEffect(() => {
    const initialMessages = [
      {
        id: 1,
        sender: 'system',
        text: t('chat.welcome_message'),
        timestamp: new Date(),
        isSystem: true
      }
    ];

    setMessages(initialMessages);
  }, [t]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = (e) => {
    e.preventDefault();

    if (!newMessage.trim()) return;

    const message = {
      id: Date.now(),
      sender: {
        id: user?.uid || 'current-user',
        name: user?.displayName || 'You',
        avatar: user?.photoURL || null
      },
      text: newMessage,
      timestamp: new Date(),
      isSystem: false
    };

    setMessages([...messages, message]);
    setNewMessage('');

    // Simulate response after a short delay
    setIsTyping(true);
    setTimeout(() => {
      const response = {
        id: Date.now() + 1,
        sender: {
          id: 'assistant',
          name: 'Meeting Assistant',
          avatar: null
        },
        text: 'This is a simulated response. In a real implementation, this would be a message from another participant.',
        timestamp: new Date(),
        isSystem: false
      };

      setMessages(prev => [...prev, response]);
      setIsTyping(false);
    }, 2000);
  };

  const formatTime = (date) => {
    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // We'll use CSS to show/hide the panel instead of conditional rendering

  return (
    <div
      className="chat-panel fixed right-0 top-0 h-full w-80 bg-gray-800 shadow-lg flex flex-col z-20 transition-all duration-300 ease-in-out"
      data-open={isOpen ? "true" : "false"}
    >
      {/* Chat Header */}
      <div className="chat-header p-4 border-b border-gray-700 flex justify-between items-center">
        <h3 className="text-white font-medium">{t('chat.title')}</h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white transition-colors"
          aria-label="Close chat"
        >
          <X size={20} />
        </button>
      </div>

      {/* Chat Messages */}
      <div className="chat-messages flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`message flex ${message.sender.id === (user?.uid || 'current-user') ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg p-3 ${
                message.isSystem
                  ? 'bg-gray-700 text-gray-300'
                  : message.sender.id === (user?.uid || 'current-user')
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 text-white'
              }`}
            >
              {!message.isSystem && message.sender.id !== (user?.uid || 'current-user') && (
                <div className="font-medium text-xs text-gray-300 mb-1">
                  {message.sender.name}
                </div>
              )}
              <p className="text-sm">{message.text}</p>
              <div className="text-xs text-right mt-1 opacity-70">
                {formatTime(message.timestamp)}
              </div>
            </div>
          </div>
        ))}

        {isTyping && (
          <div className="message flex justify-start">
            <div className="max-w-[80%] rounded-lg p-3 bg-gray-700 text-white">
              <div className="typing-indicator flex space-x-1">
                <span className="dot animate-bounce">•</span>
                <span className="dot animate-bounce" style={{ animationDelay: '0.2s' }}>•</span>
                <span className="dot animate-bounce" style={{ animationDelay: '0.4s' }}>•</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Chat Input */}
      <form onSubmit={handleSendMessage} className="chat-input p-4 border-t border-gray-700">
        <div className="flex items-center space-x-2">
          <button
            type="button"
            className="text-gray-400 hover:text-white transition-colors"
            aria-label="Attach file"
          >
            <Paperclip size={20} />
          </button>
          <button
            type="button"
            className="text-gray-400 hover:text-white transition-colors"
            aria-label="Insert emoji"
          >
            <Smile size={20} />
          </button>
          <button
            type="button"
            className="text-gray-400 hover:text-white transition-colors"
            aria-label="Upload image"
          >
            <Image size={20} />
          </button>
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder={t('chat.type_message')}
            className="flex-1 bg-gray-700 text-white rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            type="submit"
            className="bg-blue-600 text-white rounded-full p-2 hover:bg-blue-700 transition-colors"
            aria-label="Send message"
            disabled={!newMessage.trim()}
          >
            <Send size={20} />
          </button>
        </div>
      </form>
    </div>
  );
};

export default ChatPanel;
