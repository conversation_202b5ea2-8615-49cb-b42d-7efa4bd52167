const dotenv = require('dotenv');
dotenv.config();

const mongoose = require('mongoose');

// Test de connexion MongoDB
async function testMongoConnection() {
  try {
    console.log('🔄 Test de connexion à MongoDB...');
    console.log('URI:', process.env.MONGO_URI ? 'URI configurée ✅' : 'URI manquante ❌');
    
    // Connexion à MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ MongoDB connecté avec succès !');
    
    // Test d'une opération simple
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('📊 Collections disponibles:', collections.map(c => c.name));
    
    // Test de création d'un document simple
    const TestSchema = new mongoose.Schema({
      name: String,
      timestamp: { type: Date, default: Date.now }
    });
    
    const TestModel = mongoose.model('Test', TestSchema);
    
    // Créer un document de test
    const testDoc = new TestModel({ name: 'Test de connexion' });
    await testDoc.save();
    console.log('✅ Document de test créé avec succès !');
    
    // Lire le document
    const foundDoc = await TestModel.findOne({ name: 'Test de connexion' });
    console.log('✅ Document de test lu avec succès:', foundDoc);
    
    // Supprimer le document de test
    await TestModel.deleteOne({ name: 'Test de connexion' });
    console.log('✅ Document de test supprimé avec succès !');
    
    console.log('🎉 Tous les tests MongoDB ont réussi !');
    
  } catch (error) {
    console.error('❌ Erreur de connexion MongoDB:', error.message);
    console.error('Détails:', error);
  } finally {
    // Fermer la connexion
    await mongoose.connection.close();
    console.log('🔒 Connexion MongoDB fermée');
    process.exit(0);
  }
}

// Exécuter le test
testMongoConnection();
