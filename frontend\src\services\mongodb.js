import axios from 'axios';
import moment from 'moment';
import { getAuth } from 'firebase/auth';

// MongoDB API endpoint (this would be your backend server)
// Force localhost for development
const baseUrl = 'http://localhost:8000';
const API_URL = baseUrl.endsWith('/api') ? baseUrl : baseUrl + '/api';

// Helper function to ensure URLs have a protocol
const ensureProtocol = (url) => {
  if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
    return `http://${url}`;
  }
  return url;
};

// Helper function to get the API URL with protocol
const getApiUrl = () => {
  const url = ensureProtocol(API_URL);
  console.log('Using API URL:', url);
  return url;
};

// Add this function before getAuthHeader
const getCurrentUser = () => {
  const auth = getAuth();
  const user = auth.currentUser;
  if (user) {
    return user;
  }
  // Fallback to localStorage
  const storedUser = localStorage.getItem('firebase:authUser:AIzaSyDDaWeUTzAxwVYAoo1hMks0MdNetbMXGjo:[DEFAULT]');
  return storedUser ? JSON.parse(storedUser) : null;
};

// Update the getAuthHeader function to handle Firebase auth properly
const getAuthHeader = async () => {
  try {
    const user = getCurrentUser();
    if (!user) return {};

    const token = await user.getIdToken(true);
    console.log('Auth token obtained successfully');
    return {
      Authorization: `Bearer ${token}`
    };
  } catch (error) {
    console.error('Error getting auth token:', error);
    return {};
  }
};

// Add error handling helper
const handleApiError = (error, defaultMessage = 'Operation failed') => {
  console.error('API Error:', {
    status: error.response?.status,
    data: error.response?.data,
    message: error.message
  });

  if (error.response?.status === 401) {
    throw new Error('Authentication required');
  }
  throw new Error(error.response?.data?.message || defaultMessage);
};

// User API
export const userAPI = {
  // Create a new user
  createUser: async (userData) => {
    try {
      // Ensure all required fields are present for the old structure
      const defaultUser = {
        firebaseUid: userData.firebaseUid,
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        email: userData.email || '',
        phone: userData.phone || '',
        position: userData.position || 'Member',
        enterpriseCode: userData.enterpriseCode || 'default',
        status: userData.status || 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      const response = await axios.post(`${getApiUrl()}/users/register`, defaultUser);
      return response.data;
    } catch (error) {
      console.error('Error creating user in MongoDB:', error.response?.data || error.message);
      throw new Error(error.response?.data?.message || 'Failed to create user profile');
    }
  },

  getUserById: async (userId) => {
    try {
      console.log('Getting user by ID:', userId);
      const response = await axios.get(`${getApiUrl()}/users/${userId}`, {
        headers: await getAuthHeader()
      });
      return response.data;
    } catch (error) {
      if (error.response?.status === 404) {
        throw new Error('User not found');
      }
      console.error('Error getting user:', error.response?.data || error);
      throw new Error(error.response?.data?.message || 'Failed to load user data');
    }
  },

  updateUser: async (userId, userData) => {
    try {
      const response = await axios.put(`${getApiUrl()}/users/${userId}`, userData, {
        headers: {
          ...(await getAuthHeader()),
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error updating user:', error.response?.data || error);
      throw new Error(error.response?.data?.message || 'Failed to update user profile');
    }
  },

  updatePassword: async (userId, currentPassword, newPassword) => {
    try {
      const response = await axios.put(`${getApiUrl()}/users/${userId}/password`, {
        currentPassword,
        newPassword
      }, {
        headers: {
          ...(await getAuthHeader()),
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error updating password:', error.response?.data || error);
      throw new Error(error.response?.data?.message || 'Failed to update password');
    }
  },

  getPendingUsers: async (enterpriseCode) => {
    try {
      const response = await axios.get(`${getApiUrl()}/users/pending/${enterpriseCode}`, {
        headers: await getAuthHeader()
      });
      console.log('Pending users response:', response.data); // Debug log
      return response.data;
    } catch (error) {
      console.error('Error getting pending users:', error);
      return []; // Return empty array instead of throwing
    }
  },

  approveUser: async (userId) => {
    try {
      const response = await axios.post(`${getApiUrl()}/users/approve/${userId}`, {}, {
        headers: await getAuthHeader()
      });
      return response.data;
    } catch (error) {
      handleApiError(error, 'Error approving user');
    }
  },

  rejectUser: async (userId) => {
    try {
      const response = await axios.post(`${getApiUrl()}/users/reject/${userId}`, {}, {
        headers: await getAuthHeader()
      });
      return response.data;
    } catch (error) {
      handleApiError(error, 'Error rejecting user');
    }
  },

  loginUser: async (email) => {
    try {
      const response = await axios.post(`${getApiUrl()}/users/login`, { email });
      return response.data;
    } catch (error) {
      if (error.response?.status === 403) {
        throw new Error('Account not approved by admin yet.');
      }
      if (error.response?.status === 404) {
        throw new Error('User not found');
      }
      throw new Error(error.response?.data?.message || 'Login failed');
    }
  },

  // Save FCM token for a user
  // saveFcmToken: async (userId, fcmToken) => {
  //   try {
  //     const response = await axios.post(`${API_URL}/users/save-fcm-token`, { userId, fcmToken });
  //     return response.data;
  //   } catch (error) {
  //     console.error('Failed to save FCM token:', error);
  //     throw new Error(error.response?.data?.message || 'Failed to save FCM token');
  //   }
  // }
};

// Enterprise API
export const enterpriseAPI = {
  // Create a new enterprise
  createEnterprise: async (enterpriseData) => {
    try {
      const response = await axios.post(`${getApiUrl()}/enterprises`, enterpriseData, {
        headers: await getAuthHeader()
      });
      return response.data;
    } catch (error) {
      handleApiError(error, 'Error creating enterprise');
    }
  },

  // Get enterprise by code
  getEnterpriseByCode: async (code) => {
    try {
      const response = await axios.get(`${getApiUrl()}/enterprises/${code}`, {
        headers: await getAuthHeader()
      });
      return response.data;
    } catch (error) {
      console.error('Error getting enterprise:', error);
      throw error;
    }
  },

  // Improved admin check with caching
  getEnterpriseByAdminEmail: async (email) => {
    try {
      // Check cache first
      const cachedData = sessionStorage.getItem(`adminStatus_${email}`);
      if (cachedData) {
        return JSON.parse(cachedData);
      }

      const response = await axios.get(`${getApiUrl()}/enterprises/admin/${email}`, {
        headers: await getAuthHeader()
      });

      if (response.data) {
        // Cache the result
        sessionStorage.setItem(`adminStatus_${email}`, JSON.stringify(response.data));
        return response.data;
      }

      return null;
    } catch (error) {
      if (error.response?.status === 404) {
        // Cache negative result too
        sessionStorage.setItem(`adminStatus_${email}`, 'null');
        return null;
      }
      console.error('Error checking admin status:', error);
      return null;
    }
  },

  // Add method to clear admin cache
  clearAdminCache: (email) => {
    sessionStorage.removeItem(`adminStatus_${email}`);
  },

  // Update enterprise
  updateEnterprise: async (code, enterpriseData) => {
    try {
      const response = await axios.put(`${getApiUrl()}/enterprises/${code}`, enterpriseData, {
        headers: await getAuthHeader()
      });
      return response.data;
    } catch (error) {
      handleApiError(error, 'Error updating enterprise');
    }
  }
};

export const enterpriseUsersAPI = {
  getUsers: async (enterpriseCode) => {
    try {
      if (!enterpriseCode) {
        console.warn('No enterprise code provided', { enterpriseCode });
        return [];
      }

      // Get users from correct endpoint
      const response = await axios.get(`${getApiUrl()}/enterprises/${enterpriseCode}/users/active`, {
        headers: await getAuthHeader()
      });

      console.log('DEBUG - MongoDB Response:', response.data);

      // Map MongoDB documents to user objects
      const users = (response.data || []).map(user => ({
        _id: user._id, // MongoDB _id
        userId: user._id, // Add this for compatibility
        firebaseUid: user.firebaseUid,
        email: user.email || '',
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        name: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email,
        position: user.position || 'Member',
        phone: user.phone,
        enterpriseCode: enterpriseCode,
        status: 'active'
      }));

      console.log('DEBUG - Processed users:', users);
      return users;

    } catch (error) {
      console.error('MongoDB Users Error:', error);
      return [];
    }
  }
};

// Meeting API
export const meetingAPI = {
  // Create a new meeting
  createMeeting: async (meetingData) => {
    try {
      // Ensure all required fields are present
      const meeting = {
        ...meetingData,
        description: meetingData.description || `Meeting created on ${new Date().toLocaleDateString()}`,
        date: meetingData.date || new Date(),
        enterpriseCode: meetingData.enterpriseCode || 'default',
        participants: meetingData.participants || []
      };

      const response = await axios.post(`${getApiUrl()}/meetings`, meeting, {
        headers: {
          ...(await getAuthHeader()),
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error creating meeting:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });
      throw new Error(error.response?.data?.message || 'Failed to create meeting');
    }
  },

  // Get meetings for a user
  getUserMeetings: async (userId) => {
    try {
      // Get user data first
      const userData = await userAPI.getUserById(userId);
      console.log('Getting meetings for user:', { userId, enterpriseCode: userData.enterpriseCode });

      const response = await axios.get(
        `${getApiUrl()}/meetings/enterprise/${userData.enterpriseCode}/user/${userId}`,
        {
          headers: await getAuthHeader()
        }
      );

      console.log('Meeting API response:', response.data);

      if (!response.data || !Array.isArray(response.data)) {
        console.error('Invalid meetings data format:', response.data);
        return [];
      }

      return response.data;
    } catch (error) {
      console.error('Error fetching meetings:', error);
      throw error;
    }
  },

  // Get meeting by ID with better error handling
  getMeetingById: async (roomId) => {
    try {
      // Try to find in regular meetings first
      const meetingResponse = await axios.get(`${getApiUrl()}/meetings/room/${roomId}`, {
        headers: await getAuthHeader()
      });

      // If backend returns 403, treat as access denied with message
      if (meetingResponse.status === 403 || meetingResponse.data?.accessStatus?.isAccessible === false) {
        return meetingResponse.data;
      }

      // If not found in meetings, try enterprise events
      if (!meetingResponse.data) {
        const eventResponse = await axios.get(`${getApiUrl()}/meetings/enterprise-event/${roomId}`, {
          headers: await getAuthHeader()
        });

        if (!eventResponse.data) {
          return null;
        }

        // For legacy, wrap in accessStatus if not present
        return {
          ...eventResponse.data,
          accessStatus: eventResponse.data.accessStatus || { isAccessible: true, message: 'Meeting is accessible' }
        };
      }

      // Always return with accessStatus
      return {
        ...meetingResponse.data,
        accessStatus: meetingResponse.data.accessStatus || { isAccessible: true, message: 'Meeting is accessible' }
      };
    } catch (error) {
      // If backend returns 403 with data, pass it through
      if (error.response && error.response.status === 403 && error.response.data?.accessStatus) {
        return error.response.data;
      }
      console.error('Error getting meeting/event:', error.response?.data || error.message);
      throw new Error('Failed to get meeting/event');
    }
  },

  // Notify users about new meeting
  notifyUsers: async (notification) => {
    try {
      const response = await axios.post(`${getApiUrl()}/meetings/notify`, notification, {
        headers: await getAuthHeader()
      });
      return response.data;
    } catch (error) {
      handleApiError(error, 'Error sending notifications');
    }
  },

  // Check if user is admin
  isUserAdmin: async (email) => {
    try {
      const response = await axios.get(`${getApiUrl()}/enterprises/admin/${email}`, {
        headers: await getAuthHeader()
      });
      return response.data !== null;
    } catch (error) {
      return false;
    }
  },

  // Calendar event management functions
  createCalendarEvent: async (eventData) => {
    try {
      const user = JSON.parse(localStorage.getItem('firebase:authUser:AIzaSyDDaWeUTzAxwVYAoo1hMks0MdNetbMXGjo:[DEFAULT]'));
      if (!user || !user.email) {
        throw new Error('User not authenticated');
      }

      // Get fresh auth headers
      const headers = await getAuthHeader();
      if (!headers.Authorization) {
        throw new Error('No valid auth token');
      }

      const roomId = Math.random().toString(36).substring(7);

      const event = {
        roomId,
        title: eventData.title,
        startTime: moment(eventData.start).toISOString(),
        endTime: moment(eventData.end).toISOString(),
        description: eventData.description || '',
        createdBy: user.email, // Use email instead of UID
        enterpriseCode: eventData.enterpriseCode,
        type: 'enterprise_event',
        status: 'scheduled',
        participants: eventData.participants || []
      };

      console.log('Creating event with:', event);

      const response = await axios.post(
        `${getApiUrl()}/meetings/enterprise-events`,
        event,
        {
          headers: {
            ...headers,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Event created:', response.data);
      return response.data;
    } catch (error) {
      console.error('Event Creation Error:', error);
      throw new Error(error.response?.data?.message || 'Failed to create event');
    }
  },

  // Create enterprise event with participants
  createEnterpriseEvent: async (eventData) => {
    try {
      const headers = await getAuthHeader();
      if (!headers.Authorization) {
        throw new Error('Authentication required - No valid token found');
      }

      const user = getCurrentUser();
      if (!user || !user.uid) {
        throw new Error('User not authenticated');
      }

      // Get user's enterprise code if not provided
      let enterpriseCode = eventData.enterpriseCode;
      if (!enterpriseCode) {
        const userDetails = await userAPI.getUserById(user.uid);
        enterpriseCode = userDetails.enterpriseCode;
      }

      if (!enterpriseCode) {
        throw new Error('Enterprise code is required');
      }

      // Use the participants provided in the event data
      let formattedParticipants = [];

      // Check if we have participants in the event data
      if (eventData.participants && Array.isArray(eventData.participants)) {
        console.log('Using provided participants:', eventData.participants);

        // Get all enterprise users to look up participant details
        const allUsers = await enterpriseUsersAPI.getUsers(enterpriseCode);
        const userMap = {};

        // Create a map of user IDs to user objects for quick lookup
        allUsers.forEach(user => {
          const userId = user.userId || user._id || user.id || user.firebaseUid || user.email;
          userMap[userId] = user;
        });

        // Format the provided participants
        formattedParticipants = eventData.participants.map(participant => {
          // Get the participant ID
          const participantId = typeof participant === 'string'
            ? participant
            : (participant.userId || participant._id || participant.id || participant.firebaseUid || participant.email);

          // Look up the participant in our user map
          const userDetails = userMap[participantId] || {};

          return {
            userId: participantId,
            email: userDetails.email || (typeof participant === 'object' ? participant.email : ''),
            status: userDetails.email === user.email ? 'accepted' : 'pending',
            responseTime: userDetails.email === user.email ? new Date().toISOString() : null,
            name: userDetails.name || userDetails.displayName ||
                  `${userDetails.firstName || ''} ${userDetails.lastName || ''}`.trim() ||
                  userDetails.email || participantId,
            isAdmin: userDetails.isAdmin || false
          };
        });
      } else {
        console.log('No participants provided, using creator only');
      }

      // Always add the creator as a participant if not already included
      const creatorAlreadyIncluded = formattedParticipants.some(p => p.userId === user.uid);

      if (!creatorAlreadyIncluded) {
        formattedParticipants.push({
          userId: user.uid,
          email: user.email,
          status: 'accepted',
          responseTime: new Date().toISOString(),
          name: user.displayName || user.email,
          isAdmin: false // This will be updated if the user is actually an admin
        });
      }

      const event = {
        roomId: `evt-${Date.now()}-${Math.random().toString(36).substring(2)}`,
        title: eventData.title,
        description: eventData.description || '',
        startTime: moment(eventData.start || eventData.startTime).toISOString(),
        endTime: moment(eventData.end || eventData.endTime).toISOString(),
        type: 'enterprise_event',
        status: 'scheduled',
        createdBy: user.uid,
        enterpriseCode: enterpriseCode, // Use the validated enterprise code
        participants: formattedParticipants
      };

      console.log('Creating enterprise event with participants:', event);

      const response = await axios.post(
        `${getApiUrl()}/meetings/enterpriseevents`,
        event,
        { headers }
      );

      console.log('Enterprise event created:', response.data);
      window.dispatchEvent(new Event('eventsUpdated'));
      return response.data;
    } catch (error) {
      console.error('Enterprise Event Creation Error:', error);
      throw new Error('Failed to create enterprise event: ' + error.message);
    }
  },

  updateCalendarEvent: async (eventId, eventData) => {
    try {
      const headers = await getAuthHeader();
      const user = getCurrentUser();

      if (!headers.Authorization || !user) {
        throw new Error('Authentication required');
      }

      if (!eventId) {
        throw new Error('Event ID is required for update');
      }

      const calendarEvent = {
        title: eventData.title,
        startTime: moment(eventData.start).toISOString(),
        endTime: moment(eventData.end).toISOString(),
        description: eventData.description || '',
        participants: eventData.participants || [],
        status: 'scheduled',
        updatedAt: new Date().toISOString(),
        updatedBy: user.uid
      };

      console.log('Updating event:', { eventId, data: calendarEvent });

      const response = await axios.put(
        `${getApiUrl()}/meetings/calendar/${eventId}`,
        calendarEvent,
        { headers }
      );

      console.log('Event updated successfully:', response.data);
      window.dispatchEvent(new Event('eventsUpdated'));
      return response.data;

    } catch (error) {
      console.error('Update error:', error);
      if (error.response?.status === 404) {
        throw new Error('Event not found');
      }
      throw new Error(error.response?.data?.message || 'Failed to update event');
    }
  },

  deleteCalendarEvent: async (eventId, options = {}) => {
    try {
      const headers = await getAuthHeader();
      if (!headers.Authorization) {
        throw new Error('Authentication required');
      }

      if (!eventId) {
        throw new Error('Event ID is required for deletion');
      }

      const user = getCurrentUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      console.log('Attempting to delete event:', { eventId });

      const response = await axios.delete(
        `${getApiUrl()}/meetings/calendar/${eventId}`,
        {
          headers,
          data: {
            userEmail: user.email,
            userId: user.uid,
            enterpriseCode: options.enterpriseCode
          }
        }
      );

      console.log('Delete response:', response);
      window.dispatchEvent(new Event('eventsUpdated'));
      return true;
    } catch (error) {
      console.error('Delete error:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete event');
    }
  },

  // Add method to check if user can access event
  canAccessEvent: (event, userId) => {
    if (!event) return false;

    const now = new Date();
    const startTime = new Date(event.startTime || event.date);
    const endTime = new Date(event.endTime || startTime.getTime() + 3600000);

    // Check if user is participant or creator
    const hasAccess = event.participants?.some(p => p.userId === userId) ||
                     event.createdBy === userId;

    if (!hasAccess) {
      return { canAccess: false, message: 'Not authorized to access this event' };
    }

    const accessStartTime = new Date(startTime);
    accessStartTime.setMinutes(startTime.getMinutes() - 30);

    return {
      canAccess: now >= accessStartTime && now <= endTime,
      message: now < accessStartTime ?
        `Meeting will be accessible from ${accessStartTime.toLocaleTimeString()}` :
        now > endTime ?
          'Meeting has ended' :
          'Meeting is accessible'
    };
  },

  // Add this new method to clean up orphaned events
  cleanupOrphanedEvents: async () => {
    try {
      // Clear all cached data
      localStorage.removeItem('calendarEvents');
      sessionStorage.removeItem('calendarEvents');
      localStorage.removeItem('lastEventSync');

      // Force refresh from server
      window.dispatchEvent(new Event('eventsUpdated'));

      console.log('Cleaned up all cached calendar data');
    } catch (error) {
      console.error('Error cleaning up events:', error);
    }
  },

  // Update getEnterpriseUsers to use single source of truth
  getEnterpriseUsers: async (enterpriseCode) => {
    return await enterpriseUsersAPI.getUsers(enterpriseCode);
  },

  // Add new method for updating participation status
  updateParticipationStatus: async (eventId, userId, status) => {
    try {
      const response = await axios.post(
        `${getApiUrl()}/meetings/participation/${eventId}`,
        {
          userId,
          status,
          responseTime: new Date()
        },
        { headers: await getAuthHeader() }
      );
      return response.data;
    } catch (error) {
      handleApiError(error, 'Error updating participation status');
    }
  },

  // Add method to get available participants for any user
  getAvailableParticipants: async (enterpriseCode) => {
    return await enterpriseUsersAPI.getUsers(enterpriseCode);
  },

  // Update to check admin status before event operations
  canManageEvent: async (eventId, user) => {
    try {
      const event = await axios.get(`${getApiUrl()}/meetings/event/${eventId}`, {
        headers: await getAuthHeader()
      });

      // Check if user is admin
      const enterpriseData = await enterpriseAPI.getEnterpriseByAdminEmail(user.email);
      if (enterpriseData?.code === event.data.enterpriseCode) {
        return true;
      }

      // Check if user is creator
      return event.data.createdBy === user.uid;
    } catch (error) {
      console.error('Error checking event management permission:', error);
      return false;
    }
  },
};

// Meeting files API
export const meetingFilesAPI = {
  // Save meeting file to MongoDB
  saveMeetingFile: async (fileData) => {
    try {
      if (!fileData || !fileData.content) {
        throw new Error('Invalid file data');
      }

      const response = await axios.post(`${getApiUrl()}/meeting-files`, fileData, {
        headers: {
          ...(await getAuthHeader()),
          'Content-Type': 'application/json'
        }
      });

      if (!response.data || !response.data._id) {
        console.error('Invalid response from server:', response.data);
        throw new Error('Server returned invalid file data');
      }

      console.log('File saved with ID:', response.data._id);
      return response.data;
    } catch (error) {
      console.error('Save file error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
      throw new Error(error.response?.data?.message || 'Failed to save file');
    }
  },

  // Update this method to get all files for a user
  getUserMeetingFiles: async (userId) => {
    try {
      const response = await axios.get(`${getApiUrl()}/meeting-files/user/${userId}`, {
        headers: await getAuthHeader()
      });

      if (!Array.isArray(response.data)) {
        console.error('Invalid files response:', response.data);
        return [];
      }

      // Ensure all files have required fields
      return response.data.filter(file => file._id && file.name && file.content);
    } catch (error) {
      console.error('Get files error:', error);
      throw error;
    }
  },

  // Delete a meeting file by ID
  deleteMeetingFile: async (fileId) => {
    try {
      if (!fileId) {
        throw new Error('File ID is required for deletion');
      }

      const response = await axios.delete(`${getApiUrl()}/meeting-files/${fileId}`, {
        headers: await getAuthHeader()
      });

      console.log('File deleted successfully:', fileId);
      return response.data;
    } catch (error) {
      console.error('Delete file error:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete file');
    }
  }
};
