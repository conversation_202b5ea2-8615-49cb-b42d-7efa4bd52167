<?xml version="1.0" encoding="UTF-8"?>
<svg width="1440" height="900" viewBox="0 0 1440 900" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <rect width="1440" height="900" fill="url(#paint0_linear)"/>
  
  <!-- Geometric pattern -->
  <g opacity="0.15">
    <!-- Horizontal lines -->
    <path d="M0 150H1440" stroke="#3B82F6" stroke-width="1"/>
    <path d="M0 300H1440" stroke="#3B82F6" stroke-width="1"/>
    <path d="M0 450H1440" stroke="#3B82F6" stroke-width="1"/>
    <path d="M0 600H1440" stroke="#3B82F6" stroke-width="1"/>
    <path d="M0 750H1440" stroke="#3B82F6" stroke-width="1"/>
    
    <!-- Vertical lines -->
    <path d="M180 0V900" stroke="#3B82F6" stroke-width="1"/>
    <path d="M360 0V900" stroke="#3B82F6" stroke-width="1"/>
    <path d="M540 0V900" stroke="#3B82F6" stroke-width="1"/>
    <path d="M720 0V900" stroke="#3B82F6" stroke-width="1"/>
    <path d="M900 0V900" stroke="#3B82F6" stroke-width="1"/>
    <path d="M1080 0V900" stroke="#3B82F6" stroke-width="1"/>
    <path d="M1260 0V900" stroke="#3B82F6" stroke-width="1"/>
  </g>
  
  <!-- Circles -->
  <g opacity="0.2">
    <circle cx="180" cy="150" r="15" fill="#3B82F6"/>
    <circle cx="540" cy="150" r="15" fill="#3B82F6"/>
    <circle cx="900" cy="150" r="15" fill="#3B82F6"/>
    <circle cx="1260" cy="150" r="15" fill="#3B82F6"/>
    
    <circle cx="360" cy="300" r="15" fill="#3B82F6"/>
    <circle cx="720" cy="300" r="15" fill="#3B82F6"/>
    <circle cx="1080" cy="300" r="15" fill="#3B82F6"/>
    
    <circle cx="180" cy="450" r="15" fill="#3B82F6"/>
    <circle cx="540" cy="450" r="15" fill="#3B82F6"/>
    <circle cx="900" cy="450" r="15" fill="#3B82F6"/>
    <circle cx="1260" cy="450" r="15" fill="#3B82F6"/>
    
    <circle cx="360" cy="600" r="15" fill="#3B82F6"/>
    <circle cx="720" cy="600" r="15" fill="#3B82F6"/>
    <circle cx="1080" cy="600" r="15" fill="#3B82F6"/>
    
    <circle cx="180" cy="750" r="15" fill="#3B82F6"/>
    <circle cx="540" cy="750" r="15" fill="#3B82F6"/>
    <circle cx="900" cy="750" r="15" fill="#3B82F6"/>
    <circle cx="1260" cy="750" r="15" fill="#3B82F6"/>
  </g>
  
  <!-- Diagonal lines -->
  <g opacity="0.1">
    <path d="M0 0L1440 900" stroke="#3B82F6" stroke-width="1"/>
    <path d="M0 300L1140 900" stroke="#3B82F6" stroke-width="1"/>
    <path d="M0 600L540 900" stroke="#3B82F6" stroke-width="1"/>
    <path d="M300 0L1440 600" stroke="#3B82F6" stroke-width="1"/>
    <path d="M600 0L1440 300" stroke="#3B82F6" stroke-width="1"/>
    <path d="M900 0L1440 0" stroke="#3B82F6" stroke-width="1"/>
  </g>
  
  <!-- Accent shapes -->
  <g opacity="0.25">
    <!-- Top left -->
    <rect x="50" y="50" width="80" height="80" rx="10" fill="#60A5FA"/>
    <!-- Top right -->
    <rect x="1310" y="50" width="80" height="80" rx="10" fill="#60A5FA"/>
    <!-- Bottom left -->
    <rect x="50" y="770" width="80" height="80" rx="10" fill="#60A5FA"/>
    <!-- Bottom right -->
    <rect x="1310" y="770" width="80" height="80" rx="10" fill="#60A5FA"/>
  </g>
  
  <!-- Subtle glow effects -->
  <g opacity="0.15">
    <circle cx="720" cy="450" r="300" fill="url(#paint1_radial)"/>
  </g>
  
  <!-- Definitions -->
  <defs>
    <!-- Background gradient -->
    <linearGradient id="paint0_linear" x1="0" y1="0" x2="1440" y2="900" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#EFF6FF"/>
      <stop offset="0.5" stop-color="#DBEAFE"/>
      <stop offset="1" stop-color="#BFDBFE"/>
    </linearGradient>
    
    <!-- Radial gradient for glow -->
    <radialGradient id="paint1_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(720 450) rotate(90) scale(300)">
      <stop offset="0" stop-color="#3B82F6"/>
      <stop offset="1" stop-color="#3B82F6" stop-opacity="0"/>
    </radialGradient>
  </defs>
</svg>
