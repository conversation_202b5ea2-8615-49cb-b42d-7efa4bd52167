import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  sendPasswordResetEmail,
  updateProfile
} from 'firebase/auth';
import { doc, setDoc, getDoc } from 'firebase/firestore';
import { auth } from '../services/firebase';
import axios from 'axios';

const AuthContext = createContext({});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  const isConfigured = Boolean(
    process.env.REACT_APP_FIREBASE_API_KEY &&
    process.env.REACT_APP_FIREBASE_AUTH_DOMAIN &&
    process.env.REACT_APP_FIREBASE_PROJECT_ID &&
    process.env.REACT_APP_FIREBASE_STORAGE_BUCKET &&
    process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID &&
    process.env.REACT_APP_FIREBASE_APP_ID &&
    process.env.REACT_APP_FIREBASE_MEASUREMENT_ID
    // Removed VAPID_KEY from required, as it's not always needed for auth
  );

  console.log('Firebase Configured:', isConfigured); // Add this line to verify
  console.log('REACT_APP_FIREBASE_API_KEY:', process.env.REACT_APP_FIREBASE_API_KEY);
  console.log('REACT_APP_FIREBASE_AUTH_DOMAIN:', process.env.REACT_APP_FIREBASE_AUTH_DOMAIN);
  console.log('REACT_APP_FIREBASE_PROJECT_ID:', process.env.REACT_APP_FIREBASE_PROJECT_ID);
  console.log('REACT_APP_FIREBASE_STORAGE_BUCKET:', process.env.REACT_APP_FIREBASE_STORAGE_BUCKET);
  console.log('REACT_APP_FIREBASE_MESSAGING_SENDER_ID:', process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID);
  console.log('REACT_APP_FIREBASE_APP_ID:', process.env.REACT_APP_FIREBASE_APP_ID);
  console.log('REACT_APP_FIREBASE_MEASUREMENT_ID:', process.env.REACT_APP_FIREBASE_MEASUREMENT_ID);
  console.log('REACT_APP_FIREBASE_VAPID_KEY:', process.env.REACT_APP_FIREBASE_VAPID_KEY);

  useEffect(() => {
    if (!isConfigured) {
      console.error('Firebase is not configured. Please set up your environment variables.');
      return;
    }



    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        // Get fresh token and store user data
        const token = await user.getIdToken();
        const userData = {
          ...user,
          stsTokenManager: {
            ...user.stsTokenManager,
            accessToken: token
          }
        };
        localStorage.setItem(
          'firebase:authUser:AIzaSyDDaWeUTzAxwVYAoo1hMks0MdNetbMXGjo:[DEFAULT]',
          JSON.stringify(userData)
        );
      } else {
        localStorage.removeItem('firebase:authUser:AIzaSyDDaWeUTzAxwVYAoo1hMks0MdNetbMXGjo:[DEFAULT]');
      }
      setUser(user);
      setLoading(false);
    });

    return () => unsubscribe();
  }, [isConfigured]);

  const signIn = async (email, password) => {
    if (!isConfigured) {
      throw new Error('Firebase is not configured. Please set up your environment variables.');
    }
    try {
      const result = await signInWithEmailAndPassword(auth, email, password);
      return result; // Return the full credential object
    } catch (error) {
      console.error('Sign in error:', error.code, error.message);
      throw error;
    }
  };

  const signUp = async (userData) => {
    if (!isConfigured) {
      throw new Error('Firebase is not configured. Please set up your environment variables.');
    }

    const { email, password, firstName, lastName, phone, position, enterpriseCode } = userData;

    try {
      // Create user in Firebase Authentication
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);

      // Update profile with display name
      await updateProfile(userCredential.user, {
        displayName: `${firstName} ${lastName}`
      });

      // Create user in MongoDB (or your backend) using the mongodb service (or your backend) API endpoint:
      try {
        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:8000';
        const apiUrl = baseUrl.endsWith('/api') ? baseUrl : baseUrl + '/api';
        console.log('AuthContext - Using API URL for user registration:', apiUrl);

        await axios.post(`${apiUrl}/users/register`, {
          firebaseUid: userCredential.user.uid,
          firstName,
          lastName,
          email,
          phone,
          position,
          enterpriseCode,
          status: 'pending' // Let the backend determine if this should be 'active' for admins
        });
      } catch (mongoError) {
        console.error('MongoDB user creation error:', mongoError);
        // Optionally delete the Firebase user if MongoDB creation fails
        await userCredential.user.delete();
        throw new Error('Failed to create user profile. Please try again.');
      }

      return userCredential.user;
    } catch (error) {
      console.error('Error during sign up:', error);
      throw error;
    }
  };

  const resetPassword = async (email) => {
    if (!isConfigured) {
      throw new Error('Firebase is not configured. Please set up your environment variables.');
    }
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error) {
      console.error('Password reset error:', error);
      switch (error.code) {
        case 'auth/user-not-found':
          throw new Error('No user found with this email address.');
        case 'auth/invalid-email':
          throw new Error('Please enter a valid email address.');
        default:
          throw new Error('Failed to send password reset email. Please try again.');
      }
    }
  };

  const signOut = async () => {
    if (!isConfigured) {
      throw new Error('Firebase is not configured. Please set up your environment variables.');
    }
    await firebaseSignOut(auth);
  };

  const getIdToken = async (forceRefresh = false) => {
    if (!user) throw new Error('No user logged in');
    try {
      return await user.getIdToken(forceRefresh);
    } catch (error) {
      console.error('Error getting token:', error);
      throw new Error('Failed to get authentication token');
    }
  };



  const value = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    isConfigured,
    getIdToken
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && (
        <>
          {!isConfigured && (
            <div style={{
              background: '#fff3cd',
              color: '#856404',
              padding: '16px',
              border: '1px solid #ffeeba',
              borderRadius: '4px',
              margin: '16px 0',
              textAlign: 'center',
              fontWeight: 'bold'
            }}>
              Firebase configuration is missing or invalid. Please set all REACT_APP_FIREBASE_* environment variables in Azure Static Web App configuration.
            </div>
          )}
          {children}
        </>
      )}
    </AuthContext.Provider>
  );
};