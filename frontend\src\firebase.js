import { initializeApp, getApps } from "firebase/app";
import { getMessaging, getToken, onMessage } from "firebase/messaging";
import axios from "axios";

const firebaseConfig = {
  // ...your config...
};

// Prevent duplicate initialization
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];
const messaging = getMessaging(app);

// Send FCM token to backend
export const saveFcmTokenToBackend = async (token, userId) => {
  try {
    const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:8000';
    const apiUrl = baseUrl.endsWith('/api') ? baseUrl : baseUrl + '/api';
    console.log('Using API URL for FCM token:', apiUrl);

    await axios.post(`${apiUrl}/users/save-fcm-token`, {
      userId,
      fcmToken: token
    });
  } catch (err) {
    console.error('Failed to save FCM token to backend:', err);
  }
};

const VAPID_KEY = 'BNR0q5IyZ1r5NcKbxxMEP7Rzlrniv0EgRPEHkGFX941QZoyiUxmcDNLzw-9AZM1wG10rje5Phq0PAGooGuCuL2w';

export const requestNotificationPermission = async (userId) => {
  try {
    const token = await getToken(messaging, { vapidKey: VAPID_KEY });
    if (token && userId) {
      await saveFcmTokenToBackend(token, userId);
    }
    return token;
  } catch (err) {
    console.error('FCM token error:', err);
    return null;
  }
};

// Listen for foreground messages
onMessage(messaging, (payload) => {
  // Show notification or update UI
  console.log('Message received. ', payload);
  if (payload?.notification) {
    new window.Notification(payload.notification.title, {
      body: payload.notification.body
    });
  }
});