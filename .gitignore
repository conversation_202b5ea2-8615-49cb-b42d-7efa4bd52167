# Neeting Project - Global .gitignore

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log

# Build directories
build/
dist/
.cache/

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*.sublime-project
*.sublime-workspace

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Coverage and testing
coverage/
.nyc_output
.jest/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Firebase
.firebase/
firebase-debug.log

# Azure
.azure/

# Local development
.local
*.local

# Documentation builds
docs/build/

# Deployment files
.deployment
.zipignore
.azureignore

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/
.turbo/

# Miscellaneous
*.json.cache
*.md.backup
