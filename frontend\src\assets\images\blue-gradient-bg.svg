<?xml version="1.0" encoding="UTF-8"?>
<svg width="1440" height="900" viewBox="0 0 1440 900" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <rect width="1440" height="900" fill="url(#paint0_linear)"/>
  
  <!-- Rounded shapes similar to the image -->
  <path d="M1440 0C1440 0 1200 150 1200 450C1200 750 1440 900 1440 900V0Z" fill="url(#paint1_linear)" fill-opacity="0.4"/>
  <path d="M0 0C0 0 240 150 240 450C240 750 0 900 0 900V0Z" fill="url(#paint2_linear)" fill-opacity="0.4"/>
  
  <!-- Large rounded corner shape -->
  <path d="M0 0H720C720 0 720 450 360 450C0 450 0 0 0 0Z" fill="url(#paint3_linear)" fill-opacity="0.3"/>
  <path d="M1440 900H720C720 900 720 450 1080 450C1440 450 1440 900 1440 900Z" fill="url(#paint4_linear)" fill-opacity="0.3"/>
  
  <!-- Subtle glow effects -->
  <circle cx="720" cy="450" r="400" fill="url(#paint5_radial)" fill-opacity="0.15"/>
  
  <!-- Subtle overlay pattern -->
  <rect width="1440" height="900" fill="url(#paint6_radial)" fill-opacity="0.1"/>
  
  <!-- Definitions -->
  <defs>
    <!-- Main background gradient - dark blue to deeper blue -->
    <linearGradient id="paint0_linear" x1="0" y1="0" x2="1440" y2="900" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#050e29"/>
      <stop offset="0.5" stop-color="#0a1942"/>
      <stop offset="1" stop-color="#0f2257"/>
    </linearGradient>
    
    <!-- Right side shape gradient -->
    <linearGradient id="paint1_linear" x1="1440" y1="0" x2="1200" y2="450" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#1e3a8a"/>
      <stop offset="1" stop-color="#1e40af" stop-opacity="0.5"/>
    </linearGradient>
    
    <!-- Left side shape gradient -->
    <linearGradient id="paint2_linear" x1="0" y1="0" x2="240" y2="450" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#1e3a8a"/>
      <stop offset="1" stop-color="#1e40af" stop-opacity="0.5"/>
    </linearGradient>
    
    <!-- Top corner shape gradient -->
    <linearGradient id="paint3_linear" x1="0" y1="0" x2="360" y2="450" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#1e3a8a"/>
      <stop offset="1" stop-color="#1e40af" stop-opacity="0"/>
    </linearGradient>
    
    <!-- Bottom corner shape gradient -->
    <linearGradient id="paint4_linear" x1="1440" y1="900" x2="1080" y2="450" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#1e3a8a"/>
      <stop offset="1" stop-color="#1e40af" stop-opacity="0"/>
    </linearGradient>
    
    <!-- Center glow -->
    <radialGradient id="paint5_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(720 450) rotate(90) scale(400)">
      <stop offset="0" stop-color="#3b82f6"/>
      <stop offset="1" stop-color="#1e3a8a" stop-opacity="0"/>
    </radialGradient>
    
    <!-- Subtle overlay pattern -->
    <radialGradient id="paint6_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(720 450) rotate(90) scale(900)">
      <stop offset="0" stop-color="#3b82f6" stop-opacity="0.05"/>
      <stop offset="1" stop-color="#1e3a8a" stop-opacity="0"/>
    </radialGradient>
  </defs>
</svg>
