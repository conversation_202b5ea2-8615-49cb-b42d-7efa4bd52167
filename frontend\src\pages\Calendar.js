import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { meetingAPI } from '../services/mongodb';

const Calendar = () => {
  const { user } = useAuth();
  const [calendarEvents, setCalendarEvents] = useState([]);

  useEffect(() => {
    const loadEvents = async () => {
      try {
        // Clear local storage completely
        localStorage.removeItem('calendarEvents');
        sessionStorage.removeItem('calendarEvents');
        
        // Force clear any cached events
        setCalendarEvents([]);
        
        // Remove any stale entries from DB
        await meetingAPI.cleanupOrphanedEvents();
        
        // Fetch fresh events from server
        const freshEvents = await meetingAPI.getUserMeetings(user.uid);
        console.log('Fetched fresh events:', freshEvents);
        
        // Only show events that have valid data
        const validEvents = freshEvents.filter(event => 
          event._id && 
          event.startTime && 
          event.title &&
          event.type // Must have a type (meeting or enterprise_event)
        );

        console.log('Filtered valid events:', validEvents);
        setCalendarEvents(validEvents);
      } catch (error) {
        console.error('Error loading events:', error);
        setCalendarEvents([]); // Reset on error
      }
    };

    if (user) {
      loadEvents();
    }

    // Cleanup function
    return () => {
      localStorage.removeItem('calendarEvents');
      sessionStorage.removeItem('calendarEvents');
    };
  }, [user]);

  return (
    <div>
      <h1>Calendar</h1>
      <ul>
        {calendarEvents.map(event => (
          <li key={event._id}>
            {event.title} - {new Date(event.startTime).toLocaleString()}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Calendar;