/* Modern background styles */

/* Font styles */
:root {
  --font-primary: '<PERSON><PERSON><PERSON>', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-secondary: '<PERSON><PERSON><PERSON>', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  /* Dark theme colors */
  --dark-bg-primary: #050e29;
  --dark-bg-secondary: #0a1942;
  --dark-bg-tertiary: #0f2257;
  --dark-text-primary: #f8fafc;
  --dark-text-secondary: #e2e8f0;
  --dark-text-muted: #94a3b8;
  --dark-border: rgba(51, 65, 85, 0.5);
  --dark-card-bg: rgba(15, 23, 42, 0.95);
  --dark-header-bg: rgba(15, 23, 42, 0.95);
  --dark-accent: #3b82f6;
  --dark-accent-hover: #2563eb;

  /* Light theme colors */
  --light-bg-primary: #f8fafc;
  --light-bg-secondary: #f1f5f9;
  --light-bg-tertiary: #e2e8f0;
  --light-text-primary: #0f172a;
  --light-text-secondary: #1e293b;
  --light-text-muted: #64748b;
  --light-border: rgba(203, 213, 225, 0.8);
  --light-card-bg: rgba(255, 255, 255, 0.9);
  --light-header-bg: rgba(255, 255, 255, 0.9);
  --light-accent: #2563eb;
  --light-accent-hover: #1d4ed8;
}

body {
  font-family: var(--font-primary);
  letter-spacing: 0.015em;
  transition: background-color 0.3s ease, color 0.3s ease;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-secondary);
  font-weight: 600;
  letter-spacing: -0.01em;
}

/* Dark mode (default) */
body.dark-mode {
  color: var(--dark-text-primary);
  background-color: var(--dark-bg-primary);
}

/* Light mode */
body.light-mode {
  color: var(--light-text-primary);
  background-color: var(--light-bg-primary);
}

/* Main background container */
.modern-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow: hidden;
  transition: background-image 0.5s ease, background-color 0.5s ease;
}

/* Dark theme background */
body.dark-mode .modern-background {
  background-color: var(--dark-bg-primary); /* Simple solid color */
}

/* Light theme background */
body.light-mode .modern-background {
  background-color: var(--light-bg-primary); /* Simple solid color */
}

/* Content overlay removed for normal background */

/* Content container */
.content-container {
  position: relative;
  z-index: 1;
  transition: color 0.3s ease;
}

body.dark-mode .content-container {
  color: var(--dark-text-primary);
}

body.light-mode .content-container {
  color: var(--light-text-primary);
}

/* Card styles */
.modern-card {
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease, background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
  font-family: var(--font-primary);
}

body.dark-mode .modern-card {
  background-color: var(--dark-card-bg);
  border: 1px solid var(--dark-border);
  color: var(--dark-text-primary);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

body.light-mode .modern-card {
  background-color: var(--light-card-bg);
  border: 1px solid var(--light-border);
  color: var(--light-text-primary);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.modern-card:hover {
  transform: translateY(-2px);
}

body.dark-mode .modern-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(59, 130, 246, 0.5); /* Subtle blue border on hover */
}

body.light-mode .modern-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(37, 99, 235, 0.5); /* Subtle blue border on hover */
}

/* Header styles */
.modern-header {
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
  font-family: var(--font-secondary);
  font-weight: 500;
  padding: 0.5rem 0;
  height: 60px; /* Fixed height for header */
  display: flex;
  align-items: center;
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

body.dark-mode .modern-header {
  background-color: var(--dark-header-bg);
  border-bottom: 1px solid var(--dark-border);
  color: var(--dark-text-primary);
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
}

body.light-mode .modern-header {
  background-color: var(--light-header-bg);
  border-bottom: 1px solid var(--light-border);
  color: var(--light-text-primary);
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05);
}

/* Button styles */
.modern-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  font-family: var(--font-primary);
  font-weight: 500;
  letter-spacing: 0.02em;
}

body.dark-mode .modern-button {
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  color: var(--dark-text-primary);
}

body.light-mode .modern-button {
  border: 1px solid rgba(37, 99, 235, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  color: var(--light-text-primary);
}

.modern-button:hover {
  transform: translateY(-2px);
}

body.dark-mode .modern-button:hover {
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(59, 130, 246, 0.6);
}

body.light-mode .modern-button:hover {
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(37, 99, 235, 0.6);
}

.modern-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

body.dark-mode .modern-button::after {
  background: rgba(59, 130, 246, 0.7);
}

body.light-mode .modern-button::after {
  background: rgba(37, 99, 235, 0.7);
}

.modern-button:hover::after {
  animation: ripple 1s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.7;
  }
  100% {
    transform: scale(20, 20);
    opacity: 0;
  }
}

/* Calendar container */
.modern-calendar {
  border-radius: 8px;
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

body.dark-mode .modern-calendar {
  background-color: rgba(15, 23, 42, 0.95);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(51, 65, 85, 0.5);
  color: var(--dark-text-primary);
}

body.light-mode .modern-calendar {
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--light-border);
  color: var(--light-text-primary);
}

/* Calendar styling */
.rbc-calendar {
  font-family: var(--font-primary);
  letter-spacing: 0.01em;
  transition: color 0.3s ease;
}

body.dark-mode .rbc-calendar {
  color: var(--dark-text-primary);
}

body.light-mode .rbc-calendar {
  color: var(--light-text-primary);
}

.rbc-header {
  padding: 10px 0;
  font-weight: 500;
  transition: background-color 0.3s ease, color 0.3s ease;
}

body.dark-mode .rbc-header {
  background-color: rgba(30, 41, 59, 0.7);
  color: var(--dark-text-secondary);
}

body.light-mode .rbc-header {
  background-color: rgba(241, 245, 249, 0.7);
  color: var(--light-text-secondary);
}

.rbc-event {
  border-radius: 4px !important;
  transition: all 0.2s ease;
}

body.dark-mode .rbc-event {
  background-color: rgba(59, 130, 246, 0.85) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

body.light-mode .rbc-event {
  background-color: rgba(37, 99, 235, 0.85) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.rbc-event:hover {
  transform: translateY(-1px);
}

body.dark-mode .rbc-event:hover {
  background-color: rgba(59, 130, 246, 0.95) !important;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.4);
}

body.light-mode .rbc-event:hover {
  background-color: rgba(37, 99, 235, 0.95) !important;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
}

/* Today highlight */
body.dark-mode .rbc-today {
  background-color: rgba(30, 58, 138, 0.4) !important;
}

body.light-mode .rbc-today {
  background-color: rgba(219, 234, 254, 0.7) !important;
}

/* Calendar theme-specific overrides */
body.dark-mode .rbc-off-range-bg {
  background-color: rgba(15, 23, 42, 0.4) !important;
}

body.light-mode .rbc-off-range-bg {
  background-color: rgba(241, 245, 249, 0.5) !important;
}

body.dark-mode .rbc-day-bg {
  border-color: rgba(51, 65, 85, 0.5) !important;
}

body.light-mode .rbc-day-bg {
  border-color: rgba(203, 213, 225, 0.5) !important;
}

body.dark-mode .rbc-month-row,
body.dark-mode .rbc-month-view,
body.dark-mode .rbc-time-view {
  border-color: rgba(51, 65, 85, 0.5) !important;
}

body.light-mode .rbc-month-row,
body.light-mode .rbc-month-view,
body.light-mode .rbc-time-view {
  border-color: rgba(203, 213, 225, 0.5) !important;
}

/* Toolbar buttons */
.rbc-toolbar button {
  font-family: var(--font-primary);
  font-weight: 500;
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

body.dark-mode .rbc-toolbar button {
  color: var(--dark-text-secondary) !important;
  border-color: rgba(51, 65, 85, 0.5) !important;
}

body.light-mode .rbc-toolbar button {
  color: var(--light-text-secondary) !important;
  border-color: rgba(203, 213, 225, 0.8) !important;
}

body.dark-mode .rbc-toolbar button:hover,
body.dark-mode .rbc-toolbar button.rbc-active {
  background-color: rgba(59, 130, 246, 0.3) !important;
  border-color: rgba(59, 130, 246, 0.5) !important;
}

body.light-mode .rbc-toolbar button:hover,
body.light-mode .rbc-toolbar button.rbc-active {
  background-color: rgba(37, 99, 235, 0.15) !important;
  border-color: rgba(37, 99, 235, 0.5) !important;
}

.rbc-toolbar-label {
  font-family: var(--font-secondary);
  font-weight: 600;
  letter-spacing: -0.01em;
}

body.dark-mode .rbc-toolbar-label {
  color: var(--dark-text-primary) !important;
}

body.light-mode .rbc-toolbar-label {
  color: var(--light-text-primary) !important;
}

/* Additional typography styles */
.text-title {
  font-family: var(--font-secondary);
  font-weight: 600;
  letter-spacing: -0.01em;
}

.text-subtitle {
  font-family: var(--font-secondary);
  font-weight: 500;
  letter-spacing: -0.005em;
}

.text-body {
  font-family: var(--font-primary);
  font-weight: 400;
  letter-spacing: 0.01em;
}

.text-button {
  font-family: var(--font-primary);
  font-weight: 500;
  letter-spacing: 0.02em;
}

/* Navbar text styles */
.navbar-text {
  transition: color 0.3s ease;
}

body.dark-mode .navbar-text {
  color: var(--dark-text-primary);
}

body.dark-mode .navbar-text:hover {
  color: var(--dark-accent);
}

body.light-mode .navbar-text {
  color: var(--light-text-primary);
}

body.light-mode .navbar-text:hover {
  color: var(--light-accent);
}

/* Logo text styles */
.logo-text {
  transition: color 0.3s ease;
}

body.dark-mode .logo-text {
  color: var(--dark-text-primary);
}

body.light-mode .logo-text {
  color: var(--light-text-primary);
}

/* Logout button styles */
.logout-button {
  transition: background-color 0.3s ease, color 0.3s ease;
}

body.dark-mode .logout-button {
  background-color: var(--dark-bg-tertiary);
  color: var(--dark-text-primary);
}

body.light-mode .logout-button {
  background-color: var(--dark-bg-tertiary);
  color: white !important; /* Force white text in light mode */
}

body.light-mode .logout-button .text-button {
  color: white !important; /* Ensure the text inside the button is white in light mode */
}

/* Modal styles for both themes */
.theme-modal {
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

body.dark-mode .theme-modal {
  background-color: rgba(15, 23, 42, 0.95);
  border-color: rgba(51, 65, 85, 0.5);
}

body.light-mode .theme-modal {
  background-color: rgba(255, 255, 255, 0.95);
  border-color: rgba(203, 213, 225, 0.8);
}

/* Modal text styles */
.theme-modal-text {
  transition: color 0.3s ease;
}

body.dark-mode .theme-modal-text {
  color: var(--dark-text-primary);
}

body.light-mode .theme-modal-text {
  color: var(--light-text-primary);
}

/* Modal label styles */
.theme-modal-label {
  transition: color 0.3s ease;
}

body.dark-mode .theme-modal-label {
  color: var(--dark-text-secondary);
}

body.light-mode .theme-modal-label {
  color: var(--light-text-secondary);
}

/* Modal input styles */
.theme-modal-input {
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

body.dark-mode .theme-modal-input {
  background-color: rgba(30, 41, 59, 0.8);
  border-color: rgba(51, 65, 85, 0.7);
  color: var(--dark-text-primary);
}

body.light-mode .theme-modal-input {
  background-color: rgba(255, 255, 255, 0.8);
  border-color: rgba(203, 213, 225, 0.8);
  color: var(--light-text-primary);
}

/* Modal close button */
.theme-modal-close {
  transition: color 0.3s ease, background-color 0.3s ease;
}

body.dark-mode .theme-modal-close {
  color: var(--dark-text-muted);
}

body.dark-mode .theme-modal-close:hover {
  color: var(--dark-text-primary);
  background-color: rgba(51, 65, 85, 0.5);
}

body.light-mode .theme-modal-close {
  color: var(--light-text-muted);
}

body.light-mode .theme-modal-close:hover {
  color: var(--light-text-primary);
  background-color: rgba(241, 245, 249, 0.8);
}

/* Page background styles */
.theme-page-bg {
  transition: background-color 0.3s ease;
}

body.dark-mode .theme-page-bg {
  background-color: var(--dark-bg-secondary);
}

body.light-mode .theme-page-bg {
  background-color: var(--light-bg-secondary);
}

/* Page container styles */
.theme-container {
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

body.dark-mode .theme-container {
  background-color: var(--dark-bg-tertiary);
  border-color: var(--dark-border);
}

body.light-mode .theme-container {
  background-color: white;
  border-color: var(--light-border);
}

/* Page text styles */
.theme-text {
  transition: color 0.3s ease;
}

body.dark-mode .theme-text {
  color: var(--dark-text-primary);
}

body.light-mode .theme-text {
  color: var(--light-text-primary);
}

/* Page label styles */
.theme-label {
  transition: color 0.3s ease;
}

body.dark-mode .theme-label {
  color: var(--dark-text-secondary);
}

body.light-mode .theme-label {
  color: var(--light-text-secondary);
}

/* Page input styles */
.theme-input {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

body.dark-mode .theme-input {
  background-color: rgba(30, 41, 59, 0.8);
  border-color: var(--dark-border);
  color: var(--dark-text-primary);
}

body.dark-mode .theme-input:focus {
  border-color: var(--dark-accent);
}

body.light-mode .theme-input {
  background-color: white;
  border-color: var(--light-border);
  color: var(--light-text-primary);
}

body.light-mode .theme-input:focus {
  border-color: var(--light-accent);
}

/* Page button styles */
.theme-button {
  transition: background-color 0.3s ease, color 0.3s ease;
}

body.dark-mode .theme-button-primary {
  background-color: var(--dark-accent);
  color: var(--dark-text-primary);
}

body.dark-mode .theme-button-primary:hover {
  background-color: var(--dark-accent-hover);
}

body.light-mode .theme-button-primary {
  background-color: var(--light-accent);
  color: white;
}

body.light-mode .theme-button-primary:hover {
  background-color: var(--light-accent-hover);
}

/* Modal input styles */
.theme-modal-input {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

body.dark-mode .theme-modal-input {
  background-color: rgba(30, 41, 59, 0.8);
  border-color: var(--dark-border);
  color: var(--dark-text-primary);
}

body.dark-mode .theme-modal-input:focus {
  border-color: var(--dark-accent);
}

body.light-mode .theme-modal-input {
  background-color: white;
  border-color: var(--light-border);
  color: var(--light-text-primary);
}

body.light-mode .theme-modal-input:focus {
  border-color: var(--light-accent);
}

/* Modal label styles */
.theme-modal-label {
  transition: color 0.3s ease;
}

body.dark-mode .theme-modal-label {
  color: var(--dark-text-secondary);
}

body.light-mode .theme-modal-label {
  color: var(--light-text-secondary);
}
