/**
 * Service de Téléchargement Direct pour les Commandes Vocales
 * Gère le téléchargement automatique de fichiers spécifiques
 */

class DownloadService {
  constructor() {
    this.baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:8000';
    this.apiUrl = this.baseUrl.endsWith('/api') ? this.baseUrl : this.baseUrl + '/api';
  }

  /**
   * Télécharge automatiquement un fichier basé sur les filtres
   * @param {Object} filters - Filtres de recherche (date, fileType, etc.)
   * @param {string} token - Token d'authentification
   * @param {string} enterpriseCode - Code de l'entreprise
   * @returns {Promise<boolean>} - Succès du téléchargement
   */
  async downloadFileByFilters(filters, token, enterpriseCode) {
    try {
      console.log('DownloadService: Starting download with filters:', filters);

      // 1. Rechercher les fichiers correspondants
      const files = await this.searchFiles(filters, token, enterpriseCode);

      if (!files || files.length === 0) {
        throw new Error('Aucun fichier trouvé correspondant aux critères');
      }

      // 2. Déterminer le mode de téléchargement
      if (this.isDownloadAllRequest(filters)) {
        // Télécharger tous les fichiers correspondants
        return await this.downloadMultipleFiles(files, token);
      } else {
        // Télécharger le fichier le plus récent
        const targetFile = this.selectMostRecentFile(files, filters);

        if (!targetFile) {
          throw new Error('Aucun fichier approprié trouvé');
        }

        await this.downloadFile(targetFile, token);
        console.log('DownloadService: File downloaded successfully:', targetFile.name);
        return true;
      }

    } catch (error) {
      console.error('DownloadService: Download failed:', error);
      throw error;
    }
  }

  /**
   * Vérifie si c'est une demande de téléchargement de tous les fichiers
   */
  isDownloadAllRequest(filters) {
    return filters.searchTerms &&
           filters.searchTerms.some(term =>
             ['all', 'tous', 'toutes', 'everything', 'tout'].includes(term.toLowerCase())
           );
  }

  /**
   * Télécharge plusieurs fichiers en ZIP
   */
  async downloadMultipleFiles(files, token) {
    try {
      console.log(`DownloadService: Downloading ${files.length} files as ZIP`);

      // Créer un ZIP avec tous les fichiers
      const JSZip = await import('jszip');
      const zip = new JSZip.default();

      // Ajouter chaque fichier au ZIP
      for (const file of files) {
        if (file.content) {
          const fileName = this.generateFileName(file);
          zip.file(fileName, file.content);
        }
      }

      // Générer le ZIP
      const zipBlob = await zip.generateAsync({ type: 'blob' });

      // Télécharger le ZIP
      const url = window.URL.createObjectURL(zipBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `meeting_files_${new Date().toISOString().split('T')[0]}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log('DownloadService: Multiple files downloaded as ZIP');
      return true;

    } catch (error) {
      console.error('DownloadService: Multiple files download failed:', error);
      // Fallback: télécharger les fichiers un par un
      return await this.downloadFilesIndividually(files, token);
    }
  }

  /**
   * Télécharge les fichiers un par un (fallback)
   */
  async downloadFilesIndividually(files, token) {
    let successCount = 0;

    for (const file of files) {
      try {
        await this.downloadFile(file, token);
        successCount++;
        // Petit délai entre les téléchargements
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.error('Failed to download file:', file.name, error);
      }
    }

    if (successCount > 0) {
      console.log(`DownloadService: Downloaded ${successCount}/${files.length} files individually`);
      return true;
    } else {
      throw new Error('Aucun fichier n\'a pu être téléchargé');
    }
  }

  /**
   * Recherche les fichiers selon les filtres
   */
  async searchFiles(filters, token, enterpriseCode) {
    try {
      // Utiliser l'API existante meeting-files au lieu de files/search
      const response = await fetch(`${this.apiUrl}/meeting-files/user/${this.getCurrentUserId(token)}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Erreur de recherche: ${response.status}`);
      }

      const files = await response.json();
      console.log('DownloadService: Found files:', files);

      // Filtrer les fichiers selon les critères
      let filteredFiles = files || [];

      // Filtrer par type de fichier
      if (filters.fileType && filters.fileType !== 'all') {
        filteredFiles = filteredFiles.filter(file => {
          const fileType = this.getFileTypeFromFile(file);
          return fileType === filters.fileType;
        });
      }

      // Filtrer par date
      if (filters.date) {
        const targetDate = filters.date.toISOString().split('T')[0];
        filteredFiles = filteredFiles.filter(file => {
          const fileDate = new Date(file.createdAt || file.date).toISOString().split('T')[0];
          return fileDate === targetDate;
        });
      }

      console.log('DownloadService: Filtered files:', filteredFiles);
      return filteredFiles;

    } catch (error) {
      console.error('DownloadService: Search failed:', error);
      throw error;
    }
  }

  /**
   * Extrait l'ID utilisateur du token JWT
   */
  getCurrentUserId(token) {
    try {
      // Décoder le token JWT pour obtenir l'UID
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.uid || payload.user_id;
    } catch (error) {
      console.error('Error decoding token:', error);
      throw new Error('Invalid token format');
    }
  }

  /**
   * Sélectionne le fichier le plus récent correspondant aux critères
   */
  selectMostRecentFile(files, filters) {
    let filteredFiles = files;

    // Filtrer par type si spécifié
    if (filters.fileType && filters.fileType !== 'all') {
      filteredFiles = files.filter(file => {
        const fileType = this.getFileTypeFromFile(file);
        return fileType === filters.fileType;
      });
    }

    // Filtrer par date si spécifiée
    if (filters.date) {
      const targetDate = filters.date.toISOString().split('T')[0];
      filteredFiles = filteredFiles.filter(file => {
        const fileDate = new Date(file.createdAt || file.date).toISOString().split('T')[0];
        return fileDate === targetDate;
      });
    }

    // Trier par date de création (plus récent en premier)
    filteredFiles.sort((a, b) => {
      const dateA = new Date(a.createdAt || a.date);
      const dateB = new Date(b.createdAt || b.date);
      return dateB - dateA;
    });

    return filteredFiles[0] || null;
  }

  /**
   * Détermine le type de fichier à partir des métadonnées
   */
  getFileTypeFromFile(file) {
    const fileName = (file.name || '').toLowerCase();
    const fileType = (file.type || '').toLowerCase();

    if (fileName.includes('transcript') || fileType.includes('transcript')) {
      return 'transcript';
    }
    if (fileName.includes('summary') || fileName.includes('résumé') || fileType.includes('summary')) {
      return 'summary';
    }
    if (fileName.includes('recording') || fileName.includes('enregistrement') || fileType.includes('recording')) {
      return 'recording';
    }

    return 'unknown';
  }

  /**
   * Télécharge un fichier spécifique
   */
  async downloadFile(file, token) {
    try {
      console.log('DownloadService: Downloading file:', file);

      // Vérifier si le fichier a du contenu directement
      if (file.content) {
        // Créer un blob à partir du contenu
        const blob = new Blob([file.content], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);

        // Générer un nom de fichier approprié
        const fileName = this.generateFileName(file);

        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Nettoyer l'URL
        window.URL.revokeObjectURL(url);

        console.log('DownloadService: File downloaded successfully:', fileName);
        return true;
      }

      // Si pas de contenu direct, essayer de télécharger via API
      let downloadUrl;
      if (file.downloadUrl) {
        downloadUrl = file.downloadUrl;
      } else if (file._id || file.id) {
        downloadUrl = `${this.apiUrl}/meeting-files/download/${file._id || file.id}`;
      } else {
        throw new Error('Fichier sans contenu et sans URL de téléchargement');
      }

      // Télécharger le fichier via API
      const response = await fetch(downloadUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Erreur de téléchargement: ${response.status}`);
      }

      // Créer un blob et déclencher le téléchargement
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      const fileName = this.generateFileName(file);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Nettoyer l'URL
      window.URL.revokeObjectURL(url);

      return true;

    } catch (error) {
      console.error('DownloadService: File download failed:', error);
      throw error;
    }
  }

  /**
   * Génère un nom de fichier approprié
   */
  generateFileName(file) {
    const fileType = this.getFileTypeFromFile(file);
    const date = new Date(file.createdAt || file.date).toISOString().split('T')[0];

    let baseName = file.name || `${fileType}_${date}`;

    // Ajouter l'extension appropriée
    if (!baseName.includes('.')) {
      const extension = fileType === 'recording' ? '.mp3' : '.txt';
      baseName += extension;
    }

    return baseName;
  }

  /**
   * Télécharge le dernier fichier d'un type spécifique
   */
  async downloadLatestFile(fileType, token, enterpriseCode) {
    const filters = {
      fileType: fileType,
      date: null, // Pas de filtre de date pour "le dernier"
      searchTerms: []
    };

    return this.downloadFileByFilters(filters, token, enterpriseCode);
  }

  /**
   * Télécharge un fichier d'une date spécifique
   */
  async downloadFileFromDate(fileType, date, token, enterpriseCode) {
    const filters = {
      fileType: fileType,
      date: date,
      searchTerms: []
    };

    return this.downloadFileByFilters(filters, token, enterpriseCode);
  }
}

export default DownloadService;
