import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Sparkles, 
  Zap, 
  Heart,
  Star,
  Settings,
  Download,
  Share,
  Bell
} from 'lucide-react';

import AnimatedButton from './AnimatedButton';
import AnimatedCard, { FeatureCard, StatsCard, NotificationCard } from './AnimatedCard';
import LoadingSpinner, { SkeletonLoader } from './LoadingSpinner';
import { useToast } from './ToastNotification';
import { StaggerContainer, StaggerItem } from './PageTransition';
import { 
  FloatingActionButton, 
  FloatingParticles, 
  FloatingNotification,
  FloatingMenu,
  FloatingProgress 
} from './FloatingElements';
import AnimatedBackground from './AnimatedBackground';

const VisualEffectsDemo = () => {
  const [currentDemo, setCurrentDemo] = useState('buttons');
  const [isPlaying, setIsPlaying] = useState(false);
  const [showFloatingMenu, setShowFloatingMenu] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showNotification, setShowNotification] = useState(false);
  const [backgroundVariant, setBackgroundVariant] = useState('gradient');
  
  const { success, error, warning, info } = useToast();

  const demoSections = [
    { id: 'buttons', label: 'Animated Buttons', icon: Play },
    { id: 'cards', label: 'Animated Cards', icon: Star },
    { id: 'loading', label: 'Loading States', icon: RotateCcw },
    { id: 'floating', label: 'Floating Elements', icon: Sparkles },
    { id: 'backgrounds', label: 'Backgrounds', icon: Zap }
  ];

  const handleToastDemo = (type) => {
    const messages = {
      success: 'Operation completed successfully!',
      error: 'Something went wrong. Please try again.',
      warning: 'Please check your input before proceeding.',
      info: 'Here\'s some helpful information for you.'
    };

    switch (type) {
      case 'success':
        success(messages.success, { title: 'Success!' });
        break;
      case 'error':
        error(messages.error, { title: 'Error!' });
        break;
      case 'warning':
        warning(messages.warning, { title: 'Warning!' });
        break;
      case 'info':
        info(messages.info, { title: 'Info' });
        break;
    }
  };

  const simulateProgress = () => {
    setProgress(0);
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const floatingMenuItems = [
    { icon: Settings, label: 'Settings', onClick: () => handleToastDemo('info') },
    { icon: Download, label: 'Download', onClick: () => handleToastDemo('success') },
    { icon: Share, label: 'Share', onClick: () => handleToastDemo('warning') },
    { icon: Heart, label: 'Favorite', onClick: () => handleToastDemo('error') }
  ];

  const renderButtonsDemo = () => (
    <StaggerContainer className="space-y-6">
      <StaggerItem>
        <h3 className="text-2xl font-bold mb-4">Button Variants</h3>
        <div className="flex flex-wrap gap-4">
          <AnimatedButton variant="primary" onClick={() => handleToastDemo('success')}>
            Primary Button
          </AnimatedButton>
          <AnimatedButton variant="secondary" onClick={() => handleToastDemo('info')}>
            Secondary Button
          </AnimatedButton>
          <AnimatedButton variant="success" icon={Heart} onClick={() => handleToastDemo('success')}>
            Success with Icon
          </AnimatedButton>
          <AnimatedButton variant="danger" onClick={() => handleToastDemo('error')}>
            Danger Button
          </AnimatedButton>
          <AnimatedButton variant="outline" onClick={() => handleToastDemo('warning')}>
            Outline Button
          </AnimatedButton>
          <AnimatedButton variant="ghost" onClick={() => handleToastDemo('info')}>
            Ghost Button
          </AnimatedButton>
        </div>
      </StaggerItem>

      <StaggerItem>
        <h3 className="text-2xl font-bold mb-4">Button Sizes</h3>
        <div className="flex flex-wrap gap-4 items-center">
          <AnimatedButton size="sm" variant="primary">Small</AnimatedButton>
          <AnimatedButton size="md" variant="primary">Medium</AnimatedButton>
          <AnimatedButton size="lg" variant="primary">Large</AnimatedButton>
          <AnimatedButton size="xl" variant="primary">Extra Large</AnimatedButton>
        </div>
      </StaggerItem>

      <StaggerItem>
        <h3 className="text-2xl font-bold mb-4">Button States</h3>
        <div className="flex flex-wrap gap-4">
          <AnimatedButton variant="primary" loading>Loading Button</AnimatedButton>
          <AnimatedButton variant="secondary" disabled>Disabled Button</AnimatedButton>
          <AnimatedButton variant="success" icon={Play}>With Icon</AnimatedButton>
        </div>
      </StaggerItem>
    </StaggerContainer>
  );

  const renderCardsDemo = () => (
    <StaggerContainer className="space-y-6">
      <StaggerItem>
        <h3 className="text-2xl font-bold mb-4">Card Variants</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <AnimatedCard variant="default" className="p-6">
            <h4 className="text-lg font-semibold mb-2">Default Card</h4>
            <p className="text-gray-600">This is a default card with standard styling.</p>
          </AnimatedCard>
          
          <AnimatedCard variant="glass" className="p-6">
            <h4 className="text-lg font-semibold mb-2">Glass Card</h4>
            <p className="text-gray-600">This card has a glass morphism effect.</p>
          </AnimatedCard>
          
          <AnimatedCard variant="gradient" className="p-6">
            <h4 className="text-lg font-semibold mb-2">Gradient Card</h4>
            <p className="text-gray-600">This card features a gradient background.</p>
          </AnimatedCard>
        </div>
      </StaggerItem>

      <StaggerItem>
        <h3 className="text-2xl font-bold mb-4">Feature Cards</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <FeatureCard
            icon={Zap}
            title="Fast Performance"
            description="Lightning-fast loading and smooth animations"
            delay={0.1}
          />
          <FeatureCard
            icon={Heart}
            title="User Friendly"
            description="Intuitive design that users love"
            delay={0.2}
          />
          <FeatureCard
            icon={Star}
            title="High Quality"
            description="Premium components and effects"
            delay={0.3}
          />
        </div>
      </StaggerItem>

      <StaggerItem>
        <h3 className="text-2xl font-bold mb-4">Stats Cards</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <StatsCard
            value="1,234"
            label="Total Users"
            icon={Star}
            trend={12}
            delay={0.1}
          />
          <StatsCard
            value="98.5%"
            label="Uptime"
            icon={Zap}
            trend={2}
            delay={0.2}
          />
          <StatsCard
            value="4.9"
            label="Rating"
            icon={Heart}
            trend={-1}
            delay={0.3}
          />
        </div>
      </StaggerItem>
    </StaggerContainer>
  );

  const renderLoadingDemo = () => (
    <StaggerContainer className="space-y-6">
      <StaggerItem>
        <h3 className="text-2xl font-bold mb-4">Loading Spinners</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div className="text-center">
            <LoadingSpinner variant="spinner" size="lg" />
            <p className="mt-2 text-sm">Spinner</p>
          </div>
          <div className="text-center">
            <LoadingSpinner variant="dots" size="lg" />
            <p className="mt-2 text-sm">Dots</p>
          </div>
          <div className="text-center">
            <LoadingSpinner variant="pulse" size="lg" />
            <p className="mt-2 text-sm">Pulse</p>
          </div>
          <div className="text-center">
            <LoadingSpinner variant="bars" size="lg" />
            <p className="mt-2 text-sm">Bars</p>
          </div>
        </div>
      </StaggerItem>

      <StaggerItem>
        <h3 className="text-2xl font-bold mb-4">Skeleton Loaders</h3>
        <div className="space-y-4">
          <SkeletonLoader variant="text" lines={3} />
          <SkeletonLoader variant="card" />
          <SkeletonLoader variant="avatar" />
        </div>
      </StaggerItem>
    </StaggerContainer>
  );

  const renderFloatingDemo = () => (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold mb-4">Floating Elements</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <AnimatedCard className="p-6">
          <h4 className="text-lg font-semibold mb-4">Toast Notifications</h4>
          <div className="space-y-2">
            <AnimatedButton size="sm" variant="success" onClick={() => handleToastDemo('success')}>
              Success Toast
            </AnimatedButton>
            <AnimatedButton size="sm" variant="danger" onClick={() => handleToastDemo('error')}>
              Error Toast
            </AnimatedButton>
            <AnimatedButton size="sm" variant="secondary" onClick={() => handleToastDemo('warning')}>
              Warning Toast
            </AnimatedButton>
            <AnimatedButton size="sm" variant="primary" onClick={() => handleToastDemo('info')}>
              Info Toast
            </AnimatedButton>
          </div>
        </AnimatedCard>

        <AnimatedCard className="p-6">
          <h4 className="text-lg font-semibold mb-4">Progress Demo</h4>
          <div className="space-y-4">
            <AnimatedButton onClick={simulateProgress}>
              Start Progress
            </AnimatedButton>
            {progress > 0 && (
              <FloatingProgress 
                progress={progress} 
                position="top-center"
                showPercentage={true}
              />
            )}
          </div>
        </AnimatedCard>
      </div>

      {/* Floating Action Button */}
      <FloatingActionButton
        icon={Bell}
        onClick={() => setShowNotification(!showNotification)}
        tooltip="Toggle Notification"
      />

      {/* Floating Menu */}
      <FloatingMenu
        items={floatingMenuItems}
        isOpen={showFloatingMenu}
        onToggle={() => setShowFloatingMenu(!showFloatingMenu)}
        mainIcon={Settings}
        position="bottom-left"
      />

      {/* Floating Notification */}
      {showNotification && (
        <FloatingNotification
          message="This is a floating notification!"
          type="info"
          onClose={() => setShowNotification(false)}
          position="top-right"
        />
      )}
    </div>
  );

  const renderBackgroundsDemo = () => (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold mb-4">Background Variants</h3>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <AnimatedButton 
          variant={backgroundVariant === 'gradient' ? 'primary' : 'outline'}
          onClick={() => setBackgroundVariant('gradient')}
        >
          Gradient
        </AnimatedButton>
        <AnimatedButton 
          variant={backgroundVariant === 'particles' ? 'primary' : 'outline'}
          onClick={() => setBackgroundVariant('particles')}
        >
          Particles
        </AnimatedButton>
        <AnimatedButton 
          variant={backgroundVariant === 'waves' ? 'primary' : 'outline'}
          onClick={() => setBackgroundVariant('waves')}
        >
          Waves
        </AnimatedButton>
        <AnimatedButton 
          variant={backgroundVariant === 'geometric' ? 'primary' : 'outline'}
          onClick={() => setBackgroundVariant('geometric')}
        >
          Geometric
        </AnimatedButton>
      </div>

      <AnimatedCard className="p-6">
        <p className="text-gray-600">
          The background changes based on your selection. Each variant offers a different 
          visual experience with animated elements.
        </p>
      </AnimatedCard>
    </div>
  );

  return (
    <div className="min-h-screen relative">
      <AnimatedBackground variant={backgroundVariant} />
      
      <div className="relative z-10 container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-bold mb-4">Visual Effects Demo</h1>
          <p className="text-xl text-gray-600">
            Explore the enhanced visual effects in your Neeting project
          </p>
        </motion.div>

        {/* Navigation */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {demoSections.map((section) => (
            <AnimatedButton
              key={section.id}
              variant={currentDemo === section.id ? 'primary' : 'outline'}
              icon={section.icon}
              onClick={() => setCurrentDemo(section.id)}
            >
              {section.label}
            </AnimatedButton>
          ))}
        </div>

        {/* Demo Content */}
        <motion.div
          key={currentDemo}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          {currentDemo === 'buttons' && renderButtonsDemo()}
          {currentDemo === 'cards' && renderCardsDemo()}
          {currentDemo === 'loading' && renderLoadingDemo()}
          {currentDemo === 'floating' && renderFloatingDemo()}
          {currentDemo === 'backgrounds' && renderBackgroundsDemo()}
        </motion.div>
      </div>
    </div>
  );
};

export default VisualEffectsDemo;
