# Neeting - Plateforme de Visioconférence Intelligente

[![Déploiement Frontend](https://img.shields.io/badge/Frontend-Deployed-success)](https://render.com)
[![Déploiement Backend](https://img.shields.io/badge/Backend-Deployed-success)](https://render.com)
[![Statut](https://img.shields.io/badge/Statut-Production-brightgreen)](https://github.com)

Une application de visioconférence moderne avec intelligence artificielle intégrée, développée dans le cadre d'un Projet de Fin d'Études (PFE) à l'entreprise Omnilink.

## 🎯 Vue d'Ensemble

Neeting est une plateforme complète de visioconférence qui intègre des technologies d'intelligence artificielle avancées pour améliorer l'expérience des réunions virtuelles. Le projet combine reconnaissance vocale, génération automatique de résumés, et gestion intelligente des entreprises.

## ✨ Fonctionnalités Clés

### 🎥 **Visioconférence Avancée**
- Réunions vidéo HD en temps réel avec WebRTC
- Partage d'écran et contrôles audio/vidéo intuitifs
- Interface responsive adaptée à tous les appareils
- Gestion des participants en temps réel

### 🤖 **Intelligence Artificielle Intégrée**
- **Transcription automatique** avec Microsoft Azure Speech Services
- **Génération de résumés** intelligents avec Cohere AI
- **Assistant vocal multilingue** (Français, Anglais, Arabe)
- **Commandes vocales** pour navigation et contrôle

### 👥 **Gestion d'Entreprise**
- Authentification sécurisée avec Firebase
- Espaces d'entreprise isolés et sécurisés
- Gestion des rôles et permissions
- Validation d'administrateur pour nouveaux comptes

### 📅 **Calendrier et Planification**
- Planification de réunions intégrée
- Invitations automatiques aux participants
- Gestion des événements récurrents
- Synchronisation en temps réel

### 📁 **Gestion Intelligente des Fichiers**
- Sauvegarde automatique des transcriptions
- Génération automatique de comptes-rendus
- Téléchargement et partage de documents
- Historique complet par entreprise

## 🛠️ Stack Technique

### **Frontend**
- **React.js 18** - Interface utilisateur moderne
- **Tailwind CSS** - Design system responsive
- **Firebase Authentication** - Authentification sécurisée
- **Framer Motion** - Animations fluides
- **i18next** - Support multilingue

### **Backend**
- **Node.js & Express** - API RESTful performante
- **MongoDB & Mongoose** - Base de données NoSQL
- **Socket.io** - Communication temps réel
- **Firebase Admin SDK** - Gestion utilisateurs

### **Intelligence Artificielle**
- **Microsoft Azure Cognitive Services** - Reconnaissance vocale
- **Cohere AI** - Génération de texte et résumés
- **Service Vocal Unifié** - Architecture propriétaire

### **Déploiement & Infrastructure**
- **Render.com** - Hébergement frontend et backend
- **MongoDB Atlas** - Base de données cloud
- **GitHub Actions** - CI/CD automatisé

## 🚀 Démarrage Rapide

### Prérequis
- Node.js 16+ 
- MongoDB
- Comptes Firebase, Azure, et Cohere

### Installation

```bash
# Cloner le repository
git clone [repository-url]
cd neeting-project

# Installer les dépendances
cd frontend && npm install
cd ../backend && npm install

# Configurer les variables d'environnement
# Voir la documentation complète dans frontend/README.md

# Démarrer les services
cd backend && npm run dev    # Port 8000
cd frontend && npm run dev   # Port 3000
```

## 📊 Architecture

```
neeting-project/
├── 📁 frontend/              # Application React
│   ├── src/
│   │   ├── components/      # Composants UI
│   │   ├── pages/          # Pages principales
│   │   ├── services/       # Services et API
│   │   └── contexts/       # Contextes React
├── 📁 backend/              # API Node.js
│   ├── Models/             # Modèles MongoDB
│   ├── Routes/             # Routes API
│   └── middleware/         # Middlewares
├── 📁 les diagrammes/      # Documentation technique
└── 📄 render.yaml         # Configuration déploiement
```

## 🎙️ Commandes Vocales

Le système supporte des commandes vocales naturelles :

```javascript
// Navigation
"aller au calendrier"           → Navigation automatique
"voir mon profil"              → Ouverture du profil
"gérer l'entreprise"           → Accès administration

// Réunions
"créer une réunion demain à 14h" → Planification automatique
"démarrer l'enregistrement"     → Début transcription

// Recherche
"chercher projet Alpha"         → Recherche intelligente
"voir les fichiers d'hier"     → Filtrage par date
```

## 🔧 Fonctionnalités Avancées

### Service Vocal Unifié
Architecture propriétaire combinant :
- Reconnaissance vocale temps réel
- Analyse sémantique des commandes
- Exécution automatique d'actions
- Support multilingue natif

### Sécurité et Conformité
- Chiffrement end-to-end des communications
- Isolation des données par entreprise
- Authentification multi-facteurs
- Audit trail complet

### Performance et Scalabilité
- Architecture microservices
- Cache intelligent
- Optimisation des requêtes
- Monitoring en temps réel

## 📈 Métriques du Projet

- **Lignes de code** : ~15,000+
- **Composants React** : 25+
- **Routes API** : 30+
- **Tests** : En cours d'implémentation
- **Performance** : Lighthouse Score 90+

## 🤝 Équipe de Développement

Projet développé dans le cadre d'un PFE à **Omnilink** avec :
- Supervision académique universitaire
- Encadrement professionnel en entreprise
- Méthodologie Agile/Scrum
- Revues de code régulières

## 📄 Documentation

- **Documentation technique** : `les diagrammes/`
- **Guide d'installation** : `frontend/README.md`
- **API Documentation** : `backend/README.md`
- **Diagrammes UML** : Disponibles dans le dossier diagrammes

## 🚀 Déploiement

### Production
- **Frontend** : Déployé sur Render.com
- **Backend** : API déployée sur Render.com
- **Base de données** : MongoDB Atlas
- **CDN** : Intégré pour les assets statiques

### Environnements
- **Développement** : Local (localhost:3000/8000)
- **Staging** : Branches de test
- **Production** : Déploiement automatique

## 📞 Support et Contact

Pour toute question technique ou collaboration :
- 📧 Email : [contact-email]
- 📚 Documentation : Dossier `les diagrammes/`
- 🐛 Issues : GitHub Issues
- 💬 Discussions : GitHub Discussions

---

**Neeting** - Révolutionner les réunions virtuelles avec l'intelligence artificielle 🚀
