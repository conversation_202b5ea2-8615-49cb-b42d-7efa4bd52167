// filepath: /c:/PFE-2025/backend/routes/enterpriseRoutes.js
const express = require('express');
const Enterprise = require('../Models/Enterprise');
const User = require('../Models/user');
const router = express.Router();

// Create a new enterprise
router.post('/', async (req, res) => {
  const { name, code, address, email, phone, founders, numberOfEmployees, adminEmail } = req.body;

  try {
    // Check if the enterprise code already exists
    const existingEnterprise = await Enterprise.findOne({ code });
    if (existingEnterprise) {
      return res.status(400).json({ message: 'Enterprise code already exists' });
    }

    const newEnterprise = new Enterprise({ name, code, address, email, phone, founders, numberOfEmployees, adminEmail });
    await newEnterprise.save();

    // --- Ensure admin user exists in MongoDB with status 'active' ---
    const User = require('../Models/user');
    let adminUser = await User.findOne({ email: adminEmail });
    if (!adminUser) {
      adminUser = new User({
        firebaseUid: adminEmail, // Use email as UID if not registered in Firebase yet
        firstName: 'Admin',
        lastName: '',
        email: adminEmail,
        phone: '',
        position: 'Admin',
        enterpriseCode: code,
        status: 'active'
      });
      await adminUser.save();
    } else if (adminUser.status !== 'active') {
      adminUser.status = 'active';
      adminUser.enterpriseCode = code;
      await adminUser.save();
    }
    // ---

    res.status(201).json({ message: 'Enterprise created successfully' });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get enterprise by code
router.get('/:code', async (req, res) => {
  try {
    const enterprise = await Enterprise.findOne({ code: req.params.code });
    if (!enterprise) {
      return res.status(404).json({ message: 'Enterprise not found' });
    }
    res.json(enterprise);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get enterprise by admin email
router.get('/admin/:email', async (req, res) => {
  try {
    console.log('Looking for enterprise with admin email:', req.params.email);
    const enterprise = await Enterprise.findOne({ adminEmail: req.params.email });
    
    if (!enterprise) {
      console.log('No enterprise found for admin email');
      return res.status(404).json({ message: 'Enterprise not found' });
    }
    
    console.log('Enterprise found:', enterprise);
    res.json(enterprise);
  } catch (error) {
    console.error('Error finding enterprise:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get active users for an enterprise
router.get('/:enterpriseCode/users/active', async (req, res) => {
  try {
    console.log('Fetching active users for enterprise:', req.params.enterpriseCode);
    
    const users = await User.find({ 
      enterpriseCode: req.params.enterpriseCode,
      status: 'active'
    });

    console.log(`Found ${users.length} active users`);
    
    // Map users to include required fields
    const mappedUsers = users.map(user => ({
      _id: user._id,
      firebaseUid: user.firebaseUid,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      position: user.position,
      phone: user.phone,
      status: user.status
    }));

    res.json(mappedUsers);
  } catch (error) {
    console.error('Error getting enterprise users:', {
      error: error.message,
      stack: error.stack,
      enterpriseCode: req.params.enterpriseCode
    });
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Update enterprise by code
router.put('/:code', async (req, res) => {
  const { name, address, email, phone, founders, numberOfEmployees } = req.body;

  try {
    const updatedEnterprise = await Enterprise.findOneAndUpdate(
      { code: req.params.code },
      { name, address, email, phone, founders, numberOfEmployees },
      { new: true }
    );
    res.json(updatedEnterprise);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;